################################################################################
# MRS Version: 1.9.2
# �Զ����ɵ��ļ�����Ҫ�༭��
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../APP/audio.c \
../APP/cry_detect.c \
../APP/esp8266.c \
../APP/key.c \
../APP/lcd.c \
../APP/timer.c \
../APP/uart.c

OBJS += \
./APP/audio.o \
./APP/cry_detect.o \
./APP/esp8266.o \
./APP/key.o \
./APP/lcd.o \
./APP/timer.o \
./APP/uart.o

C_DEPS += \
./APP/audio.d \
./APP/cry_detect.d \
./APP/esp8266.d \
./APP/key.d \
./APP/lcd.d \
./APP/timer.d \
./APP/uart.d


# Each subdirectory must supply rules for building sources it contributes
APP/%.o: ../APP/%.c
	@	@	riscv-none-embed-gcc -march=rv32imacxw -mabi=ilp32 -msmall-data-limit=8 -msave-restore -Os -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-common -Wunused -Wuninitialized  -g -I"D:\Desktop\May\��������\����\tbh_text01\v1.15����\v1.15\Debug" -I"D:\Desktop\May\��������\����\tbh_text01\v1.15����\v1.15\Core" -I"D:\Desktop\May\��������\����\tbh_text01\v1.15����\v1.15\User" -I"D:\Desktop\May\��������\����\tbh_text01\v1.15����\v1.15\Peripheral\inc" -std=gnu99 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -c -o "$@" "$<"
	@	@

