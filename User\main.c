/*ͷ�ļ�*/
#include "debug.h"
#include <string.h> // 添加string.h头文件
#include "lcd.h"
#include "timer.h"
#include "key.h"
#include "uart.h"
#include "audio.h"
#include "esp8266.h"
#include "cry_detect.h"

/*����������*/
void TIM3_IRQ<PERSON>andler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//��ʱ��2�����ж�
void USART3_IRQ<PERSON><PERSON>ler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//����2�����ж�
void UART6_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//����2�����ж�
void UART8_IRQHandler(void) __attribute__((interrupt("WCH-Interrupt-fast")));//����2�����ж�
u32 json(u8* string, u8* params, u8 len);
//�Լ�д�ĺ���ҲҪ�Ž�ȥ


/*����������*/
unsigned int time1000ms;//�Ǹ�����
unsigned int led_flag=1;
unsigned long int uwtick;
u8 string9[] = "AT+MQTTPUB=0,\"$sys/W2PeVM9f4z/ch32/thing/property/post\",\"{\\\"id\\\":\\\"123\\\"\\,\\\"params\\\":{\\\"cry\\\":{\\\"value\\\":2\\}}}\",0,0\r\n";
//uint8_t key_val;
//uint8_t key_old;
//uint8_t key_up;
//uint8_t key_down;
uint8_t uart6_rec_string[256]={0};
uint8_t uart6_rec_tick;
uint8_t uart6_rec_index;


uint8_t uart8_rec_string[256]={0};
uint8_t uart8_rec_tick;
uint8_t uart8_rec_index;

/*��Ļ��������*/
//uint8_t num;
//void lcd_proc()
//{
//    LCD_ShowIntNum(0, 0, num, 3, RED, WHITE, 16);
//    //��ʾnum�����֣��������ʾ��λ
//    num++;
//}


/*������������*/
//��ƽ�����ᱻ�����ܶ��  ��Ϊ����  ��������Ҫ���ñ��س���
//void key_proc()
//{
//    key_val=key_read();
//    key_down=key_val&(key_val^key_old);
//    key_up=~key_val&(key_val^key_old);
//    key_old=key_val;
//
//    if(key_down==5)
//    {num++;}
//
//}



/*ϵͳ��ʱ��ʱ��3�жϷ�����*/
  void TIM3_IRQHandler(void)//ÿһ����
  {
      if(TIM_GetITStatus(TIM3, TIM_IT_Update)!=RESET)
      {
          uwtick++;
          uart6_rec_tick++;
          uart8_rec_tick++;
      }
      TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
  }

  //esp8266
  void esp8266_proc()
  {
    if(uart6_rec_index==0)
      {return;}
    if(uart6_rec_tick>10)
    {
//        USART_Printf_Init(115200);
//        printf("This is printf example\r\n");
//        printf("%s",uart6_rec_string);
        usart1_send_string(uart6_rec_string, uart6_rec_index);
        char* add1 = strstr(uart6_rec_string,"appkey");
        int appkey=0;
        sscanf(add1,"appkey\":%d",&appkey);

        switch(appkey)
        {
        case 1:
            audio_play(1);//ԭ��1
        break;
        case 2:
            audio_play(2);//ԭ��2
        break;
        case 3:
            audio_play(3);//����
        break;
        case 4:
            audio_play(10);
        break;
        }
        //strstr();
//        sscnf();


//        led_flag=json(uart6_rec_string, "stop", uart6_rec_index);
//        if(led_flag==1)
//        {
//            uart6_send_string(string9, sizeof(string9)-1);
//                Delay_Ms(1000);
//                Delay_Ms(1000);
//                Delay_Ms(1000);
//        }

        uart6_rec_index=0;//����ʮ���뷢��
    }
  }


 //����ʶ��
  void tianwen_proc()
  {
      if(uart8_rec_index==0)
      {return;}
      if(uart8_rec_tick>10)
      {
          if(strncmp(uart8_rec_string,"aa",2)==0)
          {
              led_flag=0;
          }
          uart8_rec_index=0;//����ʮ���뷢��
      }
  }

/*���������*/
typedef struct{
    void (*task_func)(void);//������
        unsigned long int rate_ms;  //����ִ������
        unsigned long int last_run; //�����ϴ����е�ʱ��

}task_t;

task_t scheduler_task[]={
//        {lcd_proc,100,0},//100msִ��һ��
        {esp8266_proc,2,0},
        {tianwen_proc,50,0},
        {cry_detect_proc,100,0}, // 哭声检测任务，100ms执行一次
};
unsigned char task_num;  //������������
void scheduler_init()
{
    task_num=sizeof(scheduler_task)/sizeof(task_t);  //������������
}
void scheduler_run()
{
    unsigned char i;
    for(i=0;i<task_num;i++)
    {
        unsigned long int now_time=uwtick;  //���µ�ǰϵͳʱ��
        if(now_time> (scheduler_task[i].last_run+scheduler_task[i].rate_ms) )
        {
            scheduler_task[i].last_run=now_time;//��¼��������ʱ��
            scheduler_task[i].task_func();//��������
        }
    }
}




//����6�жϣ������ж�+�����жϣ�  esp8266

void UART6_IRQHandler(void)
{
    u8 temp=0;
    if(USART_GetITStatus(UART6, USART_IT_RXNE) != RESET)
    {
        uart6_rec_tick=0;
        //ֻҪ���ڽ������ݣ�����Զ����0

        temp=USART_ReceiveData(UART6);//��Ƭ�����յ��Է��͵�����
        //��ֵ����ʱ����
        //��ʱ������ֵ��������
        uart6_rec_string[uart6_rec_index]=temp;
        uart6_rec_index++;
    }
    USART_ClearITPendingBit(UART6, USART_IT_RXNE);
}

//����8���ռӷ����ж�
void UART8_IRQHandler(void)
{
    u8 temp=0;
    if(USART_GetITStatus(UART8, USART_IT_RXNE) != RESET)
    {
        uart8_rec_tick=0;
        //ֻҪ���ڽ������ݣ�����Զ����0

        temp=USART_ReceiveData(UART8);//��Ƭ�����յ��Է��͵�����
        //��ֵ����ʱ����
        //��ʱ������ֵ��������
        uart8_rec_string[uart8_rec_index]=temp;
        uart8_rec_index++;

    }
    USART_ClearITPendingBit(UART8, USART_IT_RXNE);
}


//�������жϣ������ж�+�����жϣ�
u8 usart3_state;
void USART3_IRQHandler(void)
{
    uint8_t temp;
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        temp=USART_ReceiveData(USART3);//��Ƭ�����յ��Է��͵�����
        USART_SendData(USART3, temp+1);//��Ƭ�����͵�������
//        if(temp=='1')
//        {
//            GPIO_ResetBits(GPIOC, GPIO_Pin_4);
//        }
//        else {
//            GPIO_SetBits(GPIOC, GPIO_Pin_4);
//        }
    }
    USART_ClearITPendingBit(USART3, USART_IT_RXNE);
}







uint8_t u_add[3]={'1','2','3'};
//��Ƶ96��  96 000 000 ����һ��96 000 000��     ��Ƶ96����һ��1 000 000  һ������ǵ�1 000����
/*������*/
int main(void)
{
    //ģ���ʼ��  ���ִ�е�������Ҫ�ŵ����������
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	SystemCoreClockUpdate();
//	USART_SendData(USART1, '1');

	Delay_Init();
//	schedulet_init();
//	key_init();
//	TIM2_PWM_Init();//�����ʼ��
//	LCD_Init();
    Tim3_Init(1000, 96-1);//һ�����һ��
    Usart3_Init();
    Uart8_Init();
	USART_Printf_Init(115200);
	printf("SystemClk:%d\r\n",SystemCoreClock);
	printf( "ChipID:%08x\r\n", DBGMCU_GetCHIPID() );
	printf("System Starting...\r\n");
    scheduler_init();
    audio_init();
    audio_yinliang(20);


//    audio_play(2);
    esp8266_init();
    onenet_init();
    cry_detect_init(); // 初始化哭声检测模块
        printf("Cry detection initialized\r\n");
       GPIO_InitTypeDef GPIO_InitStructure = {0};//��װһ���ṹ��
       RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
       GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
       GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
       GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
       GPIO_Init(GPIOC, &GPIO_InitStructure);//����ṹ���׵�ַ
//	LCD_Fill(0, 0, 127, 127, WHITE);
//	lcd_show_chinese(0,0,"�Ұ���",RED,WHITE,16,0);//    LCD_ShowPicture(1, 1, 128, 128, gImage_1);//������ʾ
	//  TIM_SetCompare1(TIM2, 1500);
//	unsigned char i=0;
//	while(i<128)//ѭ������
//	{
//	    LCD_Fill(i, 10, i, 20, RED);//������
//	    Delay_Ms(100);
//	    i++;
//	}
//	lcd_show_chinese(0, 0, "�й�", RED, WHITE, 16, 0);
//	Delay_Ms(500);
//    LCD_ShowChinese(1, 1, "��",RED,WHITE,16, 0);//�ַ���˫����  ���ַ�������  ���ְ׵� ��С16

//    LCD_ShowPicture(1, 1, 128, 128, gImage_1);//������ʾ
    //��������ʾͼƬ�Ļ���������ʾ����
    //Ҳ������ʾͼƬ
	while(1)
    {
	    scheduler_run();
//	    if(led_flag==0)
//	    {
//           GPIO_SetBits(GPIOC, GPIO_Pin_3);//Ϩ��
//	    }
////       Delay_Ms(500);
//	    else {
//	        GPIO_ResetBits(GPIOC, GPIO_Pin_3);
//        }

//       Delay_Ms(500);
//	    USART_SendData(USART3, 'a');
//	    Delay_Ms(1000);
//	    USART_SendData(USART3, 'a');
//	    while (USART_GetFlagStatus(USART3, USART_FLAG_TC) == RESET); // �ȴ��������
//	    Delay_Ms(500);
//	    GPIO_ResetBits(GPIOA, GPIO_Pin_0);//�͵�ƽ
//	    Delay_Ms(20);
//	    GPIO_SetBits(GPIOA, GPIO_Pin_0);//�ߵ�ƽ
//	    Delay_Us(1000);//һ����

	}
}


u32 json(u8* string, u8* params, u8 len)
{
    char* str = (char*)string;
    char* key = (char*)params;

    // ��������ģʽ��"params":{
    char* params_start = strstr(str, "\"params\":{");
    if (!params_start) return 0;
    params_start += 9;  // ����"params":{ ��9���ַ�

    // �����ֵƥ��ģʽ
    char pattern[32];
    snprintf(pattern, sizeof(pattern), "\"%s\":", key);

    // ��params����������Ŀ���ֶ�
    char* key_pos = strstr(params_start, pattern);
    if (!key_pos) return 0;

    // ��λ��ֵ��ʼλ��
    char* val_start = key_pos + strlen(pattern);

    // �������ܵĿո���Ʊ���
    while (*val_start == ' ' || *val_start == '\t') val_start++;

    /* ��������ֵ���� */
    // ���true/false
    if (*val_start == 't') {
        if (strncmp(val_start, "true", 4) == 0) {
            // ��֤�����ַ��Ϸ��ԣ�����/������/�ո�
            char next_char = val_start[4];
            if (next_char == ',' || next_char == '}' ||
                next_char == ' ' || next_char == '\t') {
                return 1;
            }
        }
    }
    else if (*val_start == 'f') {
        if (strncmp(val_start, "false", 5) == 0) {
            // ��֤�����ַ��Ϸ���
            char next_char = val_start[5];
            if (next_char == ',' || next_char == '}' ||
                next_char == ' ' || next_char == '\t') {
                return 0;
            }
        }
    }

    // ���ֽ���
    u32 result = 0;
    for (u8 i = 0; i < len; i++) {
        if (*val_start >= '0' && *val_start <= '9') {
            result = result * 10 + (*val_start - '0');
            val_start++;
        } else {
            break;
        }
    }

    return result;
}


