/*
 * cry_detect.h
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#ifndef APP_CRY_DETECT_H_
#define APP_CRY_DETECT_H_

#include "debug.h"
#include "../FFT/kiss_fft.h"

/* 配置常量 */
#define SAMPLE_SIZE 1024            // 采样缓冲区大小
#define ENERGY_THRESHOLD 1000       // 能量阈值
#define ZCR_THRESHOLD 100           // 零交叉率阈值
#define PEAK_THRESHOLD 100          // 峰值阈值
#define SMOOTH_SIZE 3               // 平滑滤波窗口大小

/* 哭声类型定义 */
typedef enum {
    CRY_TYPE_SLEEPY = 0,    // 困倦哭声(低频为主)
    CRY_TYPE_ANGRY = 1,     // 愤怒哭声(中频为主)
    CRY_TYPE_HUNGRY = 2,    // 饥饿哭声(高频为主)
    CRY_TYPE_UNKNOWN = 3    // 未知类型
} cry_type_e;

/* 系统状态结构体 */
typedef struct {
    uint8_t initialized;        // 初始化标志
    uint8_t cry_detected;       // 哭声检测标志
    cry_type_e cry_type;        // 哭声类型
    uint32_t last_send_time;    // 上次发送时间
} cry_status_t;

/* 外部变量声明 */
extern cry_status_t cry_status;
extern u16 TxBuf[SAMPLE_SIZE];
extern s16 Calibrattion_Val;

/* 函数声明 */
void cry_detect_init(void);
void cry_detect_proc(void);
uint8_t cry_get_status(void);
cry_type_e cry_get_type(void);
void cry_classify_cry_type(void);

/* 内部函数声明 */
void ADC_Function_Init(void);
void DMA_Tx_Init(DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize);
u16 Get_ConversionVal(s16 val);

#endif /* APP_CRY_DETECT_H_ */
