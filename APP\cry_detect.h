/*
 * cry_detect.h
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#ifndef APP_CRY_DETECT_H_
#define APP_CRY_DETECT_H_

#include "debug.h"
#include "../FFT/kiss_fft.h"

/* 配置常量 */
#define SAMPLE_SIZE 1024            // 采样缓冲区大小
#define ENERGY_THRESHOLD 1000       // 能量阈值
#define ZCR_THRESHOLD 100           // 零交叉率阈值
#define PEAK_THRESHOLD 100          // 峰值阈值
#define SMOOTH_SIZE 3               // 平滑滤波窗口大小

/* 自适应噪声估计配置 */
#define NOISE_BUFFER_SIZE 64        // 噪声样本缓冲区大小
#define NOISE_UPDATE_INTERVAL 3000  // 噪声基线更新间隔(30秒,以100ms为单位)
#define MIN_QUIET_SAMPLES 30        // 最少安静样本数
#define NOISE_FACTOR 2.5f           // 噪声因子(阈值=噪声基线*因子)

/* 动态阈值调整配置 */
#define MIN_ENERGY_THRESHOLD 200    // 最小能量阈值
#define MAX_ENERGY_THRESHOLD 4000   // 最大能量阈值
#define BASE_ZCR_THRESHOLD 80       // 基础零交叉率阈值
#define MIN_ZCR_THRESHOLD 50        // 最小零交叉率阈值
#define MAX_ZCR_THRESHOLD 150       // 最大零交叉率阈值
#define THRESHOLD_SMOOTH_FACTOR 0.8f // 阈值平滑因子

/* 自动增益控制(AGC)配置 */
#define TARGET_RMS 1000.0f          // 目标RMS值
#define MIN_GAIN 0.5f               // 最小增益
#define MAX_GAIN 4.0f               // 最大增益
#define AGC_SMOOTH_FACTOR 0.9f      // AGC平滑因子
#define MIN_RMS_THRESHOLD 50.0f     // 最小RMS阈值(防止对噪声过度放大)

/* 用户环境校准配置 */
#define CALIBRATION_SAMPLE_COUNT 300 // 校准样本数量(30秒@100ms)
#define CALIBRATION_QUIET_TIME 100   // 校准前安静时间(10秒)
#define CALIBRATION_FLASH_ADDR 0x08020000 // 校准数据Flash地址
#define CALIBRATION_MAGIC 0x12345678 // 校准数据魔数

/* 哭声类型定义 */
typedef enum {
    CRY_TYPE_SLEEPY = 0,    // 困倦哭声(低频为主)
    CRY_TYPE_ANGRY = 1,     // 愤怒哭声(中频为主)
    CRY_TYPE_HUNGRY = 2,    // 饥饿哭声(高频为主)
    CRY_TYPE_UNKNOWN = 3    // 未知类型
} cry_type_e;

/* 系统状态结构体 */
typedef struct {
    uint8_t initialized;        // 初始化标志
    uint8_t cry_detected;       // 哭声检测标志
    cry_type_e cry_type;        // 哭声类型
    uint32_t last_send_time;    // 上次发送时间
} cry_status_t;

/* 外部变量声明 */
extern cry_status_t cry_status;
extern u16 TxBuf[SAMPLE_SIZE];
extern s16 Calibrattion_Val;

/* 函数声明 */
void cry_detect_init(void);
void cry_detect_proc(void);
uint8_t cry_get_status(void);
cry_type_e cry_get_type(void);

/* 自适应噪声估计函数 */
void noise_estimation_init(void);
void noise_estimation_proc(uint32_t current_energy, uint8_t current_status);
uint32_t get_noise_baseline(void);
uint32_t get_adaptive_energy_threshold(void);

/* 动态阈值调整函数 */
void dynamic_threshold_init(void);
void dynamic_threshold_update(uint32_t current_energy, uint16_t current_zcr);
uint32_t get_current_energy_threshold(void);
uint16_t get_current_zcr_threshold(void);

/* 自动增益控制(AGC)函数 */
void agc_init(void);
void agc_process(int16_t* samples, uint16_t length);
float get_current_gain(void);
float calculate_rms(int16_t* samples, uint16_t length);

/* 校准数据结构 */
typedef struct {
    uint32_t magic;                  // 魔数，用于验证数据有效性
    uint32_t noise_baseline;         // 校准的噪声基线
    uint32_t energy_threshold;       // 校准的能量阈值
    uint16_t zcr_threshold;          // 校准的零交叉率阈值
    float agc_target_rms;            // 校准的AGC目标RMS
    uint32_t checksum;               // 校验和
} calibration_data_t;

/* 用户环境校准函数 */
void calibration_init(void);
void calibration_start(void);
void calibration_process(uint32_t energy, uint16_t zcr, float rms);
uint8_t calibration_is_active(void);
uint8_t calibration_load_from_flash(void);
void calibration_save_to_flash(void);
void calibration_reset_to_default(void);

/* 内部函数声明 */
void ADC_Function_Init(void);
void DMA_Tx_Init(DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize);
u16 Get_ConversionVal(s16 val);

#endif /* APP_CRY_DETECT_H_ */
