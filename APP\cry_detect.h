/*
 * cry_detect.h
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#ifndef APP_CRY_DETECT_H_
#define APP_CRY_DETECT_H_

#include "debug.h"

/* 配置常量 */
#define CRY_SAMPLE_RATE 8000        // 采样率8kHz
#define CRY_FFT_SIZE 512            // FFT点数
#define CRY_BUFFER_SIZE 1024        // 音频缓冲区大小(双缓冲)
#define CRY_FREQ_RESOLUTION 15.6f   // 频率分辨率(Hz/bin)

/* 特征提取参数 */
#define CRY_BASE_FREQ_MIN_BIN 19    // 基频下限bin(300Hz)
#define CRY_BASE_FREQ_MAX_BIN 38    // 基频上限bin(600Hz)
#define CRY_HIGH_FREQ_MIN_BIN 64    // 高频下限bin(1kHz)
#define CRY_HIGH_FREQ_MAX_BIN 192   // 高频上限bin(3kHz)

/* 检测阈值 */
#define CRY_BASE_FREQ_THRESHOLD 0.3f    // 基频能量比阈值
#define CRY_HIGH_FREQ_THRESHOLD 0.2f    // 高频能量比阈值
#define CRY_DETECT_COUNT_THRESHOLD 3    // 连续检测次数阈值

/* 系统配置 */
#define CRY_TASK_PERIOD_MS 100      // 任务执行周期(ms)
#define CRY_SEND_INTERVAL_MS 5000   // MQTT发送间隔(ms)
#define CRY_ALERT_SOUND_ID 4        // 哭声提示音ID

/* 哭声分类配置 */
#define CRY_CONFIDENCE_THRESHOLD 70         // 置信度阈值
#define CRY_HISTORY_FRAMES 8                // 历史帧数(800ms历史)
#define CRY_CLASSIFICATION_INTERVAL_MS 500  // 分类输出间隔(ms)

/* 哭声类型枚举 */
typedef enum {
    CRY_TYPE_UNKNOWN = 0,           // 未知类型
    CRY_TYPE_HUNGRY = 1,            // 饥饿哭声
    CRY_TYPE_SICK = 2,              // 生病哭声
    CRY_TYPE_SLEEPY = 3,            // 困倦哭声
    CRY_TYPE_BORED = 4              // 无聊哭声
} cry_type_e;

/* 哭声特征数据结构 */
typedef struct {
    float base_freq_ratio;          // 基频能量比
    float high_freq_ratio;          // 高频能量比
    float spectral_centroid;        // 频谱重心(Hz)
    float spectral_rolloff;         // 频谱滚降点(Hz)
    float energy_variance;          // 能量方差
    float zero_crossing_rate;       // 零交叉率
    uint8_t cry_detected;           // 哭声检测标志
    uint8_t detect_count;           // 连续检测计数
    uint8_t cry_type;               // 哭声类型(cry_type_e)
    uint8_t confidence;             // 分类置信度(0-100)
} cry_feature_t;

/* 配置管理结构体 */
typedef struct {
    float base_threshold;           // 基频阈值
    float high_threshold;           // 高频阈值
    uint8_t enable;                 // 功能使能
    uint8_t debug_mode;             // 调试模式
    // 分类功能配置
    uint8_t classification_enable;  // 分类功能使能
    uint8_t confidence_threshold;   // 置信度阈值(0-100)
    float sick_centroid_threshold;  // 生病哭声频谱重心阈值
    float hungry_centroid_min;      // 饥饿哭声频谱重心下限
    float hungry_centroid_max;      // 饥饿哭声频谱重心上限
    float sleepy_centroid_max;      // 困倦哭声频谱重心上限
    float variance_threshold;       // 能量方差阈值
} cry_config_t;

/* 系统状态结构体 */
typedef struct {
    uint8_t initialized;            // 初始化标志
    uint8_t audio_ready;            // 音频数据就绪
    uint8_t fft_busy;               // FFT计算忙标志
    uint32_t last_send_time;        // 上次发送时间
} cry_status_t;

/* 外部变量声明 */
extern cry_status_t cry_status;     // 系统状态

/* 函数声明 */
void cry_detect_init(void);         // 初始化哭声检测模块
void cry_detect_proc(void);         // 哭声检测主处理函数
uint8_t cry_get_status(void);       // 获取检测状态
void cry_set_config(cry_config_t* config);     // 设置配置参数
cry_config_t* cry_get_config(void); // 获取配置参数
void cry_reset_status(void);        // 重置检测状态
void cry_enable(uint8_t enable);    // 使能/禁用检测功能
float* cry_get_magnitude_spectrum(void);   // 获取幅度谱数据
cry_feature_t* cry_get_feature_data(void); // 获取特征数据
void cry_load_default_config(void);        // 加载默认配置
uint8_t cry_validate_config(cry_config_t* config); // 验证配置有效性
uint8_t cry_get_classification_type(void); // 获取当前分类类型
uint8_t cry_get_classification_confidence(void); // 获取分类置信度
void cry_print_features_debug(void);       // 调试输出特征信息
void cry_print_history_debug(void);        // 调试输出历史信息
void cry_set_classification_config(cry_config_t* config); // 设置分类配置
cry_config_t* cry_get_classification_config(void); // 获取分类配置

#endif /* APP_CRY_DETECT_H_ */
