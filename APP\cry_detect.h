/*
 * cry_detect.h
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#ifndef APP_CRY_DETECT_H_
#define APP_CRY_DETECT_H_

#include "debug.h"
#include "../FFT/kiss_fft.h"

/* 配置常量 */
#define SAMPLE_SIZE 1024            // 采样缓冲区大小
#define ENERGY_THRESHOLD 1000       // 能量阈值
#define ZCR_THRESHOLD 100           // 零交叉率阈值
#define PEAK_THRESHOLD 100          // 峰值阈值
#define SMOOTH_SIZE 3               // 平滑滤波窗口大小

/* 自适应噪声估计配置 */
#define NOISE_BUFFER_SIZE 64        // 噪声样本缓冲区大小
#define NOISE_UPDATE_INTERVAL 3000  // 噪声基线更新间隔(30秒,以100ms为单位)
#define MIN_QUIET_SAMPLES 30        // 最少安静样本数
#define NOISE_FACTOR 2.5f           // 噪声因子(阈值=噪声基线*因子)

/* 动态阈值调整配置 */
#define MIN_ENERGY_THRESHOLD 200    // 最小能量阈值
#define MAX_ENERGY_THRESHOLD 4000   // 最大能量阈值
#define BASE_ZCR_THRESHOLD 80       // 基础零交叉率阈值
#define MIN_ZCR_THRESHOLD 50        // 最小零交叉率阈值
#define MAX_ZCR_THRESHOLD 150       // 最大零交叉率阈值
#define THRESHOLD_SMOOTH_FACTOR 0.8f // 阈值平滑因子

/* 自动增益控制(AGC)配置 */
#define TARGET_RMS 1000.0f          // 目标RMS值
#define MIN_GAIN 0.5f               // 最小增益
#define MAX_GAIN 4.0f               // 最大增益
#define AGC_SMOOTH_FACTOR 0.9f      // AGC平滑因子
#define MIN_RMS_THRESHOLD 50.0f     // 最小RMS阈值(防止对噪声过度放大)

/* 用户环境校准配置 */
#define CALIBRATION_SAMPLE_COUNT 300 // 校准样本数量(30秒@100ms)
#define CALIBRATION_QUIET_TIME 100   // 校准前安静时间(10秒)
#define CALIBRATION_FLASH_ADDR 0x08020000 // 校准数据Flash地址
#define CALIBRATION_MAGIC 0x12345678 // 校准数据魔数

/* 增强特征提取配置 */
#define SAMPLE_RATE 8000            // 采样率
#define SPECTRAL_FEATURES_ENABLED 1 // 启用频谱特征
#define ENERGY_VARIANCE_WINDOW 10   // 能量方差计算窗口
#define FEATURE_SMOOTH_FACTOR 0.7f  // 特征平滑因子

/* 置信度评估配置 */
#define CONFIDENCE_THRESHOLD 75.0f  // 置信度阈值(75%)
#define CONFIDENCE_HISTORY_SIZE 5   // 置信度历史记录大小
#define MIN_CONFIDENCE_SAMPLES 3    // 最小置信度样本数
#define CONFIDENCE_SMOOTH_FACTOR 0.8f // 置信度平滑因子

/* 调试和监控配置 */
#define DEBUG_MODE_ENABLED 1        // 启用调试模式
#define DEBUG_BUFFER_SIZE 128       // 调试命令缓冲区大小
#define PERFORMANCE_STATS_SIZE 10   // 性能统计历史大小
#define DEBUG_OUTPUT_INTERVAL 1000  // 调试输出间隔(ms)
#define MAX_DEBUG_COMMAND_LEN 32    // 最大调试命令长度

/* 哭声类型定义 */
typedef enum {
    CRY_TYPE_SLEEPY = 0,    // 困倦哭声(低频为主)
    CRY_TYPE_ANGRY = 1,     // 愤怒哭声(中频为主)
    CRY_TYPE_HUNGRY = 2,    // 饥饿哭声(高频为主)
    CRY_TYPE_UNKNOWN = 3    // 未知类型
} cry_type_e;

/* 系统状态结构体 */
typedef struct {
    uint8_t initialized;        // 初始化标志
    uint8_t cry_detected;       // 哭声检测标志
    cry_type_e cry_type;        // 哭声类型
    uint32_t last_send_time;    // 上次发送时间
} cry_status_t;

/* 外部变量声明 */
extern cry_status_t cry_status;
extern u16 TxBuf[SAMPLE_SIZE];
extern s16 Calibrattion_Val;

/* 函数声明 */
void cry_detect_init(void);
void cry_detect_proc(void);
uint8_t cry_get_status(void);
cry_type_e cry_get_type(void);

/* 自适应噪声估计函数 */
void noise_estimation_init(void);
void noise_estimation_proc(uint32_t current_energy, uint8_t current_status);
uint32_t get_noise_baseline(void);
uint32_t get_adaptive_energy_threshold(void);

/* 动态阈值调整函数 */
void dynamic_threshold_init(void);
void dynamic_threshold_update(uint32_t current_energy, uint16_t current_zcr);
uint32_t get_current_energy_threshold(void);
uint16_t get_current_zcr_threshold(void);

/* 自动增益控制(AGC)函数 */
void agc_init(void);
void agc_process(int16_t* samples, uint16_t length);
float get_current_gain(void);
float calculate_rms(int16_t* samples, uint16_t length);

/* 增强特征数据结构 */
typedef struct {
    float spectral_centroid;         // 频谱质心
    float spectral_bandwidth;       // 频谱带宽
    float energy_variance;           // 短时能量方差
    float spectral_rolloff;          // 频谱滚降点
    uint32_t energy;                 // 传统能量特征
    uint16_t zcr;                    // 传统零交叉率特征
} audio_features_t;

/* 置信度评估数据结构 */
typedef struct {
    float overall_confidence;        // 总体置信度
    float feature_confidences[6];    // 各特征置信度
    uint8_t cry_type;               // 分类结果
    uint8_t is_high_confidence;     // 是否高置信度
    uint32_t timestamp;             // 时间戳
} confidence_result_t;

/* 性能统计数据结构 */
typedef struct {
    uint32_t total_detections;       // 总检测次数
    uint32_t cry_detections;         // 哭声检测次数
    uint32_t high_confidence_count;  // 高置信度检测次数
    uint32_t low_confidence_count;   // 低置信度检测次数
    uint32_t false_positives;        // 误报次数(用户可标记)
    uint32_t processing_time_us;     // 处理时间(微秒)
    float average_confidence;        // 平均置信度
    uint32_t last_update_time;       // 最后更新时间
} performance_stats_t;

/* 调试状态数据结构 */
typedef struct {
    uint8_t debug_enabled;           // 调试模式开关
    uint8_t real_time_monitor;       // 实时监控开关
    uint8_t feature_display;         // 特征显示开关
    uint8_t performance_monitor;     // 性能监控开关
    uint32_t debug_output_counter;   // 调试输出计数器
    char last_command[MAX_DEBUG_COMMAND_LEN]; // 最后执行的命令
} debug_state_t;

/* 校准数据结构 */
typedef struct {
    uint32_t magic;                  // 魔数，用于验证数据有效性
    uint32_t noise_baseline;         // 校准的噪声基线
    uint32_t energy_threshold;       // 校准的能量阈值
    uint16_t zcr_threshold;          // 校准的零交叉率阈值
    float agc_target_rms;            // 校准的AGC目标RMS
    uint32_t checksum;               // 校验和
} calibration_data_t;

/* 增强特征提取函数 */
void enhanced_features_init(void);
void extract_enhanced_features(float* fft_magnitude, int16_t* audio_data, audio_features_t* features);
float calculate_spectral_centroid(float* magnitude, uint16_t length);
float calculate_spectral_bandwidth(float* magnitude, uint16_t length, float centroid);
float calculate_energy_variance(uint32_t current_energy);
float calculate_spectral_rolloff(float* magnitude, uint16_t length, float threshold);
uint8_t classify_cry_with_enhanced_features(audio_features_t* features);

/* 置信度评估函数 */
void confidence_init(void);
float calculate_confidence(audio_features_t* features, uint8_t cry_type);
float evaluate_feature_confidence(float feature_value, uint8_t feature_type, uint8_t cry_type);
void update_confidence_history(float confidence);
float get_average_confidence(void);
uint8_t is_high_confidence_result(float confidence);
void process_classification_with_confidence(uint8_t cry_type, float confidence);

/* 调试和监控函数 */
void debug_init(void);
void debug_print_system_status(void);
void debug_print_features(audio_features_t* features);
void debug_print_performance_stats(void);
void debug_process_command(char* command);
void debug_toggle_mode(uint8_t mode);
void debug_set_parameter(const char* param_name, int value);
void debug_print_help(void);
void performance_stats_init(void);
void performance_stats_update(uint8_t cry_detected, float confidence, uint32_t processing_time);
void performance_stats_mark_false_positive(void);
void debug_real_time_monitor(void);

/* 用户环境校准函数 */
void calibration_init(void);
void calibration_start(void);
void calibration_process(uint32_t energy, uint16_t zcr, float rms);
uint8_t calibration_is_active(void);
uint8_t calibration_load_from_flash(void);
void calibration_save_to_flash(void);
void calibration_reset_to_default(void);

/* 内部函数声明 */
void ADC_Function_Init(void);
void DMA_Tx_Init(DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize);
u16 Get_ConversionVal(s16 val);

#endif /* APP_CRY_DETECT_H_ */
