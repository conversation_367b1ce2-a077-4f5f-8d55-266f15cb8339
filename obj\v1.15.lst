
v1.15.elf:     file format elf32-littleriscv
v1.15.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x00002028 memsz 0x00002028 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x00002028 align 2**12
         filesz 0x00000220 memsz 0x00000440 flags rw-
    LOAD off    0x00004800 vaddr 0x20007800 paddr 0x20007800 align 2**12
         filesz 0x00000000 memsz 0x00000800 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         00000004  00000000  00000000  00001000  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .vector       000001bc  00000004  00000004  00001004  2**1
                  CONTENTS, ALLOC, LOAD, READ<PERSON>L<PERSON>, CODE
  2 .text         00001e68  000001c0  000001c0  000011c0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .fini         00000000  00002028  00002028  00004220  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  4 .dalign       00000000  20000000  20000000  00004220  2**0
                  CONTENTS
  5 .dlalign      00000000  00002028  00002028  00004220  2**0
                  CONTENTS
  6 .data         00000220  20000000  00002028  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .bss          00000220  20000220  00002248  00004220  2**2
                  ALLOC
  8 .stack        00000800  20007800  20007800  00004800  2**0
                  ALLOC
  9 .debug_info   00010f4b  00000000  00000000  00004220  2**0
                  CONTENTS, READONLY, DEBUGGING
 10 .debug_abbrev 00002897  00000000  00000000  0001516b  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 000008f0  00000000  00000000  00017a08  2**3
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_ranges 00000968  00000000  00000000  000182f8  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_line   0000be42  00000000  00000000  00018c60  2**0
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_str    00002df6  00000000  00000000  00024aa2  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      00000033  00000000  00000000  00027898  2**0
                  CONTENTS, READONLY
 16 .debug_frame  00001de4  00000000  00000000  000278cc  2**2
                  CONTENTS, READONLY, DEBUGGING
 17 .debug_loc    0000501c  00000000  00000000  000296b0  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .stabstr      00000117  00000000  00000000  0002e6cc  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
00000004 l    d  .vector	00000000 .vector
000001c0 l    d  .text	00000000 .text
00002028 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
00002028 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000220 l    d  .bss	00000000 .bss
20007800 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .stabstr	00000000 .stabstr
00000000 l    df *ABS*	00000000 ./Startup/startup_ch32v30x_D8C.o
00000004 l       .vector	00000000 _vector_base
00000000 l    df *ABS*	00000000 ch32v30x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v30x.c
00000000 l    df *ABS*	00000000 ch32v30x_gpio.c
00000000 l    df *ABS*	00000000 ch32v30x_misc.c
00000000 l    df *ABS*	00000000 ch32v30x_rcc.c
20000028 l     O .data	00000010 APBAHBPrescTable
20000210 l     O .data	00000004 ADCPrescTable
00000000 l    df *ABS*	00000000 ch32v30x_tim.c
00000000 l    df *ABS*	00000000 ch32v30x_usart.c
00000000 l    df *ABS*	00000000 debug.c
20000230 l     O .bss	00000002 p_ms
20000232 l     O .bss	00000001 p_us
20000214 l     O .data	00000004 curbrk.5276
00000000 l    df *ABS*	00000000 audio.c
00000000 l    df *ABS*	00000000 lcd.c
00000000 l    df *ABS*	00000000 timer.c
00000000 l    df *ABS*	00000000 uart.c
00000000 l    df *ABS*	00000000 memcpy.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 sbrkr.c
00000000 l    df *ABS*	00000000 sscanf.c
00000000 l    df *ABS*	00000000 stdio.c
00000000 l    df *ABS*	00000000 strlen.c
00000000 l    df *ABS*	00000000 strncmp.c
00000000 l    df *ABS*	00000000 strstr.c
00000000 l    df *ABS*	00000000 mlock.c
00000000 l    df *ABS*	00000000 nano-vfscanf.c
00000000 l    df *ABS*	00000000 nano-vfscanf_i.c
00000000 l    df *ABS*	00000000 sccl.c
00000000 l    df *ABS*	00000000 strtol.c
00001a6e l     F .text	00000138 _strtol_l.isra.0
00000000 l    df *ABS*	00000000 strtoul.c
00001bba l     F .text	0000012e _strtoul_l.isra.0
00000000 l    df *ABS*	00000000 ungetc.c
00000000 l    df *ABS*	00000000 locale.c
00000000 l    df *ABS*	00000000 mbtowc_r.c
00000000 l    df *ABS*	00000000 memchr.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 wctomb_r.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 findfp.c
00000000 l    df *ABS*	00000000 ctype_.c
00000000 l    df *ABS*	00000000 impure.c
20000038 l     O .data	00000060 impure_data
00000000 l    df *ABS*	00000000 reent.c
00000000 l    df *ABS*	00000000 close.c
00001e5e g     F .text	00000014 _malloc_usable_size_r
000006c0  w      .text	00000000 EXTI2_IRQHandler
000016b6 g     F .text	000000e0 _scanf_chars
000006c0  w      .text	00000000 TIM8_TRG_COM_IRQHandler
000006c0  w      .text	00000000 TIM8_CC_IRQHandler
000003a4 g     F .text	0000003c UART8_IRQHandler
20000222 g     O .bss	00000001 uart6_rec_tick
20000a08 g       .data	00000000 __global_pointer$
000001c8 g     F .text	00000028 .hidden __riscv_save_8
000006c0  w      .text	00000000 TIM1_CC_IRQHandler
00000222 g     F .text	00000002 HardFault_Handler
0000123a g     F .text	00000002 __malloc_unlock
00000214 g     F .text	0000000c .hidden __riscv_restore_3
000006c0  w      .text	00000000 TIM6_IRQHandler
000006c0  w      .text	00000000 SysTick_Handler
0000081a g     F .text	0000004e NVIC_Init
000006c0  w      .text	00000000 PVD_IRQHandler
000006c0  w      .text	00000000 SDIO_IRQHandler
000006c0  w      .text	00000000 TIM9_BRK_IRQHandler
00000200 g     F .text	00000020 .hidden __riscv_restore_10
00001d86 g     F .text	00000018 __locale_ctype_ptr
000006c0  w      .text	00000000 DMA2_Channel8_IRQHandler
00000220 g     F .text	00000002 NMI_Handler
000006c0  w      .text	00000000 CAN2_RX1_IRQHandler
000006c0  w      .text	00000000 EXTI3_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_11
000006c0  w      .text	00000000 USBHS_IRQHandler
00000bfc g     F .text	0000000a USART_GetFlagStatus
000006c0  w      .text	00000000 DMA2_Channel9_IRQHandler
000006c0  w      .text	00000000 TIM10_CC_IRQHandler
2000043c g     O .bss	00000004 errno
00001796 g     F .text	00000256 _scanf_i
000011c6 g     F .text	00000004 __seofread
00000d04 g     F .text	0000000a audio_init
20000220 g       .bss	00000000 _sbss
00000800 g       *ABS*	00000000 __stack_size
00000c8c g     F .text	00000052 USART_Printf_Init
000006c0  w      .text	00000000 USBFS_IRQHandler
000002a0 g     F .text	0000003c tianwen_proc
00000214 g     F .text	0000000c .hidden __riscv_restore_2
00000d56 g     F .text	0000004a audio_yinliang
00000f2a g     F .text	000000b2 memcpy
000006c0  w      .text	00000000 EXTI0_IRQHandler
000006c0  w      .text	00000000 I2C2_EV_IRQHandler
000006c0  w      .text	00000000 TIM10_TRG_COM_IRQHandler
00000ac4 g     F .text	00000018 TIM_Cmd
2000020c g     O .data	00000004 SystemCoreClock
00000bf4 g     F .text	00000008 USART_ReceiveData
00000004 g       .init	00000000 _einit
00000b06 g     F .text	0000000c TIM_ClearITPendingBit
000009de g     F .text	0000001e RCC_APB2PeriphClockCmd
000001c0 g     F .text	00000030 .hidden __riscv_save_12
000006c0  w      .text	00000000 CAN2_SCE_IRQHandler
000006c0  w      .text	00000000 ADC1_2_IRQHandler
000012d2 g     F .text	00000042 __ssrefill_r
0000074c g     F .text	000000c0 GPIO_Init
000006c0  w      .text	00000000 Break_Point_Handler
00000200 g     F .text	00000020 .hidden __riscv_restore_11
2000022c g     O .bss	00000004 NVIC_Priority_Group
000006c0  w      .text	00000000 SPI1_IRQHandler
00000ba0 g     F .text	00000016 USART_Cmd
0000114e g     F .text	00000028 _sbrk_r
000006c0  w      .text	00000000 TAMPER_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_1
00000df4 g     F .text	00000088 Usart3_Init
00000214 g     F .text	0000000c .hidden __riscv_restore_0
000001d6 g     F .text	0000001a .hidden __riscv_save_7
00000c4e g     F .text	00000014 USART_ClearITPendingBit
000006c0  w      .text	00000000 CAN2_RX0_IRQHandler
2000033c g     O .bss	00000100 uart8_rec_string
00001314 g     F .text	000003a2 __ssvfscanf_r
000006c0  w      .text	00000000 TIM8_UP_IRQHandler
000009fc g     F .text	0000001e RCC_APB1PeriphClockCmd
000006c0  w      .text	00000000 Ecall_M_Mode_Handler
20007800 g       .stack	00000000 _heap_end
0000020a g     F .text	00000016 .hidden __riscv_restore_5
00001de8 g     F .text	00000058 _realloc_r
000006c0  w      .text	00000000 DMA2_Channel2_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel4_IRQHandler
00000cde g     F .text	00000026 _sbrk
000006c0  w      .text	00000000 TIM9_UP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_6
000003e0 g     F .text	00000044 USART3_IRQHandler
000006c0  w      .text	00000000 RTC_IRQHandler
20000440 g       .bss	00000000 _ebss
00000c12 g     F .text	0000003c USART_GetITStatus
000006c0  w      .text	00000000 DMA1_Channel7_IRQHandler
000006c0  w      .text	00000000 CAN1_RX1_IRQHandler
00000c62 g     F .text	0000002a Delay_Init
000006c0  w      .text	00000000 DVP_IRQHandler
000006c0  w      .text	00000000 UART5_IRQHandler
00000d0e g     F .text	00000048 audio_play
0000080c g     F .text	00000004 GPIO_SetBits
000006c0  w      .text	00000000 TIM4_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_9
000006c0  w      .text	00000000 DMA2_Channel1_IRQHandler
00001e8c g     O .text	00000020 __sf_fake_stderr
000001d6 g     F .text	0000001a .hidden __riscv_save_4
000006c0  w      .text	00000000 I2C1_EV_IRQHandler
00000aee g     F .text	00000018 TIM_GetITStatus
00000868 g     F .text	00000176 RCC_GetClocksFreq
000006c0  w      .text	00000000 DMA1_Channel6_IRQHandler
000006c0  w      .text	00000000 UART4_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel4_IRQHandler
00001dd0 g     F .text	00000018 memchr
00000b12 g     F .text	0000008e USART_Init
00000fdc g     F .text	000000a4 _free_r
000002dc g     F .text	00000040 TIM3_IRQHandler
000006c0  w      .text	00000000 RCC_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_3
000006c0  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel1_IRQHandler
00000efc g     F .text	0000002e usart1_send_string
0000123c g     F .text	00000096 _sungetc_r
00000000 g       .init	00000000 _start
000006c0  w      .text	00000000 DMA2_Channel7_IRQHandler
20000018 g     O .data	00000010 AHBPrescTable
00001204 g     F .text	00000034 strstr
0000031c g     F .text	0000003c scheduler_run
000006c0  w      .text	00000000 EXTI15_10_IRQHandler
00000adc g     F .text	00000012 TIM_ITConfig
000011dc g     F .text	00000028 strncmp
00000bb6 g     F .text	00000036 USART_ITConfig
00001ce8 g     F .text	00000014 _strtoul_r
00000810 g     F .text	00000004 GPIO_ResetBits
000006c0  w      .text	00000000 TIM7_IRQHandler
000006c0  w      .text	00000000 CAN2_TX_IRQHandler
20000000 g       .dalign	00000000 _data_vma
000006c0  w      .text	00000000 TIM5_IRQHandler
20000220 g     O .bss	00000001 task_num
00001176 g     F .text	00000050 sscanf
000006c0  w      .text	00000000 EXTI9_5_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_10
000006c0  w      .text	00000000 ETH_WKUP_IRQHandler
00001238 g     F .text	00000002 __malloc_lock
0000020a g     F .text	00000016 .hidden __riscv_restore_4
00000200 g     F .text	00000020 .hidden __riscv_restore_8
000001d6 g     F .text	0000001a .hidden __riscv_save_6
000006c0  w      .text	00000000 SPI2_IRQHandler
00001eac g     O .text	00000020 __sf_fake_stdin
00000200 g     F .text	00000020 .hidden __riscv_restore_9
0000020a g     F .text	00000016 .hidden __riscv_restore_7
00000424 g     F .text	00000078 main
000006c0  w      .text	00000000 TIM10_BRK_IRQHandler
000006c0  w      .text	00000000 TIM9_CC_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel5_IRQHandler
00001ba6 g     F .text	00000014 _strtol_r
00001080 g     F .text	000000ce _malloc_r
00001e40 g     F .text	0000001e __ascii_wctomb
00001cfc g     F .text	00000084 __submore
000006c0  w      .text	00000000 DMA1_Channel5_IRQHandler
000006c0  w      .text	00000000 EXTI4_IRQHandler
000006c0  w      .text	00000000 USB_LP_CAN1_RX0_IRQHandler
00001176 g     F .text	00000050 siscanf
00000c06 g     F .text	0000000c USART_ClearFlag
0000049c g     F .text	000000fa SystemInit
000006c0  w      .text	00000000 RNG_IRQHandler
20000221 g     O .bss	00000001 uart6_rec_index
000006c0  w      .text	00000000 USB_HP_CAN1_TX_IRQHandler
00000e7c g     F .text	00000080 Uart8_Init
00000000 g       .init	00000000 _sinit
000006c0  w      .text	00000000 DMA1_Channel3_IRQHandler
00000da4 g     F .text	00000050 Tim3_Init
000006c0  w      .text	00000000 ETH_IRQHandler
20000218 g     O .data	00000004 _impure_ptr
000006c0  w      .text	00000000 TIM1_UP_IRQHandler
000006c0  w      .text	00000000 WWDG_IRQHandler
000006c0  w      .text	00000000 USBHSWakeup_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel11_IRQHandler
00001d9e g     F .text	00000032 __ascii_mbtowc
000006c0  w      .text	00000000 Ecall_U_Mode_Handler
000006c0  w      .text	00000000 DMA2_Channel6_IRQHandler
000006c0  w      .text	00000000 TIM2_IRQHandler
20008000 g       .stack	00000000 _eusrstack
000001f0 g     F .text	0000000c .hidden __riscv_save_2
000006c0  w      .text	00000000 SW_Handler
00001314 g     F .text	000003a2 __ssvfiscanf_r
000006c0  w      .text	00000000 TIM1_BRK_IRQHandler
00000bec g     F .text	00000008 USART_SendData
000006c0  w      .text	00000000 DMA2_Channel10_IRQHandler
000006c0  w      .text	00000000 EXTI1_IRQHandler
000001d6 g     F .text	0000001a .hidden __riscv_save_5
20000220 g       .data	00000000 _edata
20000440 g       .bss	00000000 _end
20000223 g     O .bss	00000001 uart8_rec_index
00000a1a g     F .text	000000aa TIM_TimeBaseInit
000006c0  w      .text	00000000 RTCAlarm_IRQHandler
00002028 g       .dlalign	00000000 _data_lma
000006c0  w      .text	00000000 TIM10_UP_IRQHandler
000006c0  w      .text	00000000 TIM9_TRG_COM_IRQHandler
000006c0  w      .text	00000000 UART7_IRQHandler
000006c0  w      .text	00000000 USART2_IRQHandler
00000358 g     F .text	0000004c UART6_IRQHandler
00000596 g     F .text	0000012a SystemCoreClockUpdate
000006c0  w      .text	00000000 I2C2_ER_IRQHandler
000006c0  w      .text	00000000 DMA1_Channel2_IRQHandler
00001ecc g     O .text	00000020 __sf_fake_stdout
000019ec g     F .text	00000082 __sccl
20000208 g     O .data	00000004 led_flag
000001fc g     F .text	00000024 .hidden __riscv_restore_12
000006c0  w      .text	00000000 TIM8_BRK_IRQHandler
00001f24 g     O .text	00000101 _ctype_
000006c2  w      .text	00000000 handle_reset
000006c0  w      .text	00000000 CAN1_SCE_IRQHandler
20000000 g     O .data	00000018 scheduler_task
000001f0 g     F .text	0000000c .hidden __riscv_save_0
000006c0  w      .text	00000000 FLASH_IRQHandler
000006c0  w      .text	00000000 USART1_IRQHandler
20000224 g     O .bss	00000001 uart8_rec_tick
000011ca g     F .text	00000012 strlen
00000da0 g     F .text	00000004 SPI3_IRQHandler
00001d80 g     F .text	00000006 __locale_ctype_ptr_l
20000238 g     O .bss	00000004 __malloc_sbrk_start
2000023c g     O .bss	00000100 uart6_rec_string
000006c0  w      .text	00000000 I2C1_ER_IRQHandler
00000814 g     F .text	00000006 NVIC_PriorityGroupConfig
20000234 g     O .bss	00000004 __malloc_free_list
00000214 g     F .text	0000000c .hidden __riscv_restore_1
00000224 g     F .text	0000007c esp8266_proc
20000228 g     O .bss	00000004 uwtick
20000098 g     O .data	0000016c __global_locale
000006c0  w      .text	00000000 USBWakeUp_IRQHandler
000006c0  w      .text	00000000 DMA2_Channel3_IRQHandler



Disassembly of section .init:

00000000 <_sinit>:
   0:	6c20006f          	j	6c2 <handle_reset>

Disassembly of section .vector:

00000004 <_vector_base>:
	...
   c:	0220                	addi	s0,sp,264
   e:	0000                	unimp
  10:	0222                	slli	tp,tp,0x8
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	06c0                	addi	s0,sp,836
	...
  22:	0000                	unimp
  24:	06c0                	addi	s0,sp,836
  26:	0000                	unimp
  28:	06c0                	addi	s0,sp,836
	...
  32:	0000                	unimp
  34:	06c0                	addi	s0,sp,836
  36:	0000                	unimp
  38:	0000                	unimp
  3a:	0000                	unimp
  3c:	06c0                	addi	s0,sp,836
  3e:	0000                	unimp
  40:	0000                	unimp
  42:	0000                	unimp
  44:	06c0                	addi	s0,sp,836
  46:	0000                	unimp
  48:	06c0                	addi	s0,sp,836
  4a:	0000                	unimp
  4c:	06c0                	addi	s0,sp,836
  4e:	0000                	unimp
  50:	06c0                	addi	s0,sp,836
  52:	0000                	unimp
  54:	06c0                	addi	s0,sp,836
  56:	0000                	unimp
  58:	06c0                	addi	s0,sp,836
  5a:	0000                	unimp
  5c:	06c0                	addi	s0,sp,836
  5e:	0000                	unimp
  60:	06c0                	addi	s0,sp,836
  62:	0000                	unimp
  64:	06c0                	addi	s0,sp,836
  66:	0000                	unimp
  68:	06c0                	addi	s0,sp,836
  6a:	0000                	unimp
  6c:	06c0                	addi	s0,sp,836
  6e:	0000                	unimp
  70:	06c0                	addi	s0,sp,836
  72:	0000                	unimp
  74:	06c0                	addi	s0,sp,836
  76:	0000                	unimp
  78:	06c0                	addi	s0,sp,836
  7a:	0000                	unimp
  7c:	06c0                	addi	s0,sp,836
  7e:	0000                	unimp
  80:	06c0                	addi	s0,sp,836
  82:	0000                	unimp
  84:	06c0                	addi	s0,sp,836
  86:	0000                	unimp
  88:	06c0                	addi	s0,sp,836
  8a:	0000                	unimp
  8c:	06c0                	addi	s0,sp,836
  8e:	0000                	unimp
  90:	06c0                	addi	s0,sp,836
  92:	0000                	unimp
  94:	06c0                	addi	s0,sp,836
  96:	0000                	unimp
  98:	06c0                	addi	s0,sp,836
  9a:	0000                	unimp
  9c:	06c0                	addi	s0,sp,836
  9e:	0000                	unimp
  a0:	06c0                	addi	s0,sp,836
  a2:	0000                	unimp
  a4:	06c0                	addi	s0,sp,836
  a6:	0000                	unimp
  a8:	06c0                	addi	s0,sp,836
  aa:	0000                	unimp
  ac:	06c0                	addi	s0,sp,836
  ae:	0000                	unimp
  b0:	06c0                	addi	s0,sp,836
  b2:	0000                	unimp
  b4:	06c0                	addi	s0,sp,836
  b6:	0000                	unimp
  b8:	02dc                	addi	a5,sp,324
  ba:	0000                	unimp
  bc:	06c0                	addi	s0,sp,836
  be:	0000                	unimp
  c0:	06c0                	addi	s0,sp,836
  c2:	0000                	unimp
  c4:	06c0                	addi	s0,sp,836
  c6:	0000                	unimp
  c8:	06c0                	addi	s0,sp,836
  ca:	0000                	unimp
  cc:	06c0                	addi	s0,sp,836
  ce:	0000                	unimp
  d0:	06c0                	addi	s0,sp,836
  d2:	0000                	unimp
  d4:	06c0                	addi	s0,sp,836
  d6:	0000                	unimp
  d8:	06c0                	addi	s0,sp,836
  da:	0000                	unimp
  dc:	06c0                	addi	s0,sp,836
  de:	0000                	unimp
  e0:	03e0                	addi	s0,sp,460
  e2:	0000                	unimp
  e4:	06c0                	addi	s0,sp,836
  e6:	0000                	unimp
  e8:	06c0                	addi	s0,sp,836
  ea:	0000                	unimp
  ec:	06c0                	addi	s0,sp,836
  ee:	0000                	unimp
  f0:	06c0                	addi	s0,sp,836
  f2:	0000                	unimp
  f4:	06c0                	addi	s0,sp,836
  f6:	0000                	unimp
  f8:	06c0                	addi	s0,sp,836
  fa:	0000                	unimp
  fc:	06c0                	addi	s0,sp,836
  fe:	0000                	unimp
 100:	06c0                	addi	s0,sp,836
 102:	0000                	unimp
 104:	0000                	unimp
 106:	0000                	unimp
 108:	06c0                	addi	s0,sp,836
 10a:	0000                	unimp
 10c:	06c0                	addi	s0,sp,836
 10e:	0000                	unimp
 110:	0da0                	addi	s0,sp,728
 112:	0000                	unimp
 114:	06c0                	addi	s0,sp,836
 116:	0000                	unimp
 118:	06c0                	addi	s0,sp,836
 11a:	0000                	unimp
 11c:	06c0                	addi	s0,sp,836
 11e:	0000                	unimp
 120:	06c0                	addi	s0,sp,836
 122:	0000                	unimp
 124:	06c0                	addi	s0,sp,836
 126:	0000                	unimp
 128:	06c0                	addi	s0,sp,836
 12a:	0000                	unimp
 12c:	06c0                	addi	s0,sp,836
 12e:	0000                	unimp
 130:	06c0                	addi	s0,sp,836
 132:	0000                	unimp
 134:	06c0                	addi	s0,sp,836
 136:	0000                	unimp
 138:	06c0                	addi	s0,sp,836
 13a:	0000                	unimp
 13c:	06c0                	addi	s0,sp,836
 13e:	0000                	unimp
 140:	06c0                	addi	s0,sp,836
 142:	0000                	unimp
 144:	06c0                	addi	s0,sp,836
 146:	0000                	unimp
 148:	06c0                	addi	s0,sp,836
 14a:	0000                	unimp
 14c:	06c0                	addi	s0,sp,836
 14e:	0000                	unimp
 150:	06c0                	addi	s0,sp,836
 152:	0000                	unimp
 154:	06c0                	addi	s0,sp,836
 156:	0000                	unimp
 158:	06c0                	addi	s0,sp,836
 15a:	0000                	unimp
 15c:	06c0                	addi	s0,sp,836
 15e:	0000                	unimp
 160:	0358                	addi	a4,sp,388
 162:	0000                	unimp
 164:	06c0                	addi	s0,sp,836
 166:	0000                	unimp
 168:	03a4                	addi	s1,sp,456
 16a:	0000                	unimp
 16c:	06c0                	addi	s0,sp,836
 16e:	0000                	unimp
 170:	06c0                	addi	s0,sp,836
 172:	0000                	unimp
 174:	06c0                	addi	s0,sp,836
 176:	0000                	unimp
 178:	06c0                	addi	s0,sp,836
 17a:	0000                	unimp
 17c:	06c0                	addi	s0,sp,836
 17e:	0000                	unimp
 180:	06c0                	addi	s0,sp,836
 182:	0000                	unimp
 184:	06c0                	addi	s0,sp,836
 186:	0000                	unimp
 188:	06c0                	addi	s0,sp,836
 18a:	0000                	unimp
 18c:	06c0                	addi	s0,sp,836
 18e:	0000                	unimp
 190:	06c0                	addi	s0,sp,836
 192:	0000                	unimp
 194:	06c0                	addi	s0,sp,836
 196:	0000                	unimp
 198:	06c0                	addi	s0,sp,836
 19a:	0000                	unimp
 19c:	06c0                	addi	s0,sp,836
 19e:	0000                	unimp
 1a0:	06c0                	addi	s0,sp,836
	...

Disassembly of section .text:

000001c0 <__riscv_save_12>:
     1c0:	7139                	addi	sp,sp,-64
     1c2:	4301                	li	t1,0
     1c4:	c66e                	sw	s11,12(sp)
     1c6:	a019                	j	1cc <__riscv_save_10+0x4>

000001c8 <__riscv_save_10>:
     1c8:	7139                	addi	sp,sp,-64
     1ca:	5341                	li	t1,-16
     1cc:	c86a                	sw	s10,16(sp)
     1ce:	ca66                	sw	s9,20(sp)
     1d0:	cc62                	sw	s8,24(sp)
     1d2:	ce5e                	sw	s7,28(sp)
     1d4:	a019                	j	1da <__riscv_save_4+0x4>

000001d6 <__riscv_save_4>:
     1d6:	7139                	addi	sp,sp,-64
     1d8:	5301                	li	t1,-32
     1da:	d05a                	sw	s6,32(sp)
     1dc:	d256                	sw	s5,36(sp)
     1de:	d452                	sw	s4,40(sp)
     1e0:	d64e                	sw	s3,44(sp)
     1e2:	d84a                	sw	s2,48(sp)
     1e4:	da26                	sw	s1,52(sp)
     1e6:	dc22                	sw	s0,56(sp)
     1e8:	de06                	sw	ra,60(sp)
     1ea:	40610133          	sub	sp,sp,t1
     1ee:	8282                	jr	t0

000001f0 <__riscv_save_0>:
     1f0:	1141                	addi	sp,sp,-16
     1f2:	c04a                	sw	s2,0(sp)
     1f4:	c226                	sw	s1,4(sp)
     1f6:	c422                	sw	s0,8(sp)
     1f8:	c606                	sw	ra,12(sp)
     1fa:	8282                	jr	t0

000001fc <__riscv_restore_12>:
     1fc:	4db2                	lw	s11,12(sp)
     1fe:	0141                	addi	sp,sp,16

00000200 <__riscv_restore_10>:
     200:	4d02                	lw	s10,0(sp)
     202:	4c92                	lw	s9,4(sp)
     204:	4c22                	lw	s8,8(sp)
     206:	4bb2                	lw	s7,12(sp)
     208:	0141                	addi	sp,sp,16

0000020a <__riscv_restore_4>:
     20a:	4b02                	lw	s6,0(sp)
     20c:	4a92                	lw	s5,4(sp)
     20e:	4a22                	lw	s4,8(sp)
     210:	49b2                	lw	s3,12(sp)
     212:	0141                	addi	sp,sp,16

00000214 <__riscv_restore_0>:
     214:	4902                	lw	s2,0(sp)
     216:	4492                	lw	s1,4(sp)
     218:	4422                	lw	s0,8(sp)
     21a:	40b2                	lw	ra,12(sp)
     21c:	0141                	addi	sp,sp,16
     21e:	8082                	ret

00000220 <NMI_Handler>:
     220:	a001                	j	220 <NMI_Handler>

00000222 <HardFault_Handler>:
     222:	a001                	j	222 <HardFault_Handler>

00000224 <esp8266_proc>:
     224:	8191c583          	lbu	a1,-2023(gp) # 20000221 <uart6_rec_index>
     228:	c9bd                	beqz	a1,29e <esp8266_proc+0x7a>
     22a:	fc7ff2ef          	jal	t0,1f0 <__riscv_save_0>
     22e:	81918493          	addi	s1,gp,-2023 # 20000221 <uart6_rec_index>
     232:	81a1c703          	lbu	a4,-2022(gp) # 20000222 <uart6_rec_tick>
     236:	47a9                	li	a5,10
     238:	1141                	addi	sp,sp,-16
     23a:	04e7f263          	bgeu	a5,a4,27e <esp8266_proc+0x5a>
     23e:	83418513          	addi	a0,gp,-1996 # 2000023c <uart6_rec_string>
     242:	4bb000ef          	jal	ra,efc <usart1_send_string>
     246:	000025b7          	lui	a1,0x2
     24a:	e7458593          	addi	a1,a1,-396 # 1e74 <_malloc_usable_size_r+0x16>
     24e:	83418513          	addi	a0,gp,-1996 # 2000023c <uart6_rec_string>
     252:	7b3000ef          	jal	ra,1204 <strstr>
     256:	000025b7          	lui	a1,0x2
     25a:	0070                	addi	a2,sp,12
     25c:	e7c58593          	addi	a1,a1,-388 # 1e7c <_malloc_usable_size_r+0x1e>
     260:	c602                	sw	zero,12(sp)
     262:	715000ef          	jal	ra,1176 <siscanf>
     266:	47b2                	lw	a5,12(sp)
     268:	4709                	li	a4,2
     26a:	02e78663          	beq	a5,a4,296 <esp8266_proc+0x72>
     26e:	00f74a63          	blt	a4,a5,282 <esp8266_proc+0x5e>
     272:	4705                	li	a4,1
     274:	4505                	li	a0,1
     276:	00e78d63          	beq	a5,a4,290 <esp8266_proc+0x6c>
     27a:	00048023          	sb	zero,0(s1)
     27e:	0141                	addi	sp,sp,16
     280:	bf51                	j	214 <__riscv_restore_0>
     282:	470d                	li	a4,3
     284:	00e78b63          	beq	a5,a4,29a <esp8266_proc+0x76>
     288:	4711                	li	a4,4
     28a:	4529                	li	a0,10
     28c:	fee797e3          	bne	a5,a4,27a <esp8266_proc+0x56>
     290:	27f000ef          	jal	ra,d0e <audio_play>
     294:	b7dd                	j	27a <esp8266_proc+0x56>
     296:	4509                	li	a0,2
     298:	bfe5                	j	290 <esp8266_proc+0x6c>
     29a:	450d                	li	a0,3
     29c:	bfd5                	j	290 <esp8266_proc+0x6c>
     29e:	8082                	ret

000002a0 <tianwen_proc>:
     2a0:	81b1c703          	lbu	a4,-2021(gp) # 20000223 <uart8_rec_index>
     2a4:	cb1d                	beqz	a4,2da <tianwen_proc+0x3a>
     2a6:	f4bff2ef          	jal	t0,1f0 <__riscv_save_0>
     2aa:	81b18413          	addi	s0,gp,-2021 # 20000223 <uart8_rec_index>
     2ae:	81c1c703          	lbu	a4,-2020(gp) # 20000224 <uart8_rec_tick>
     2b2:	47a9                	li	a5,10
     2b4:	02e7f263          	bgeu	a5,a4,2d8 <tianwen_proc+0x38>
     2b8:	000025b7          	lui	a1,0x2
     2bc:	4609                	li	a2,2
     2be:	e8858593          	addi	a1,a1,-376 # 1e88 <_malloc_usable_size_r+0x2a>
     2c2:	93418513          	addi	a0,gp,-1740 # 2000033c <uart8_rec_string>
     2c6:	717000ef          	jal	ra,11dc <strncmp>
     2ca:	e509                	bnez	a0,2d4 <tianwen_proc+0x34>
     2cc:	200007b7          	lui	a5,0x20000
     2d0:	2007a423          	sw	zero,520(a5) # 20000208 <led_flag>
     2d4:	00040023          	sb	zero,0(s0)
     2d8:	bf35                	j	214 <__riscv_restore_0>
     2da:	8082                	ret

000002dc <TIM3_IRQHandler>:
     2dc:	40000537          	lui	a0,0x40000
     2e0:	4585                	li	a1,1
     2e2:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     2e6:	009000ef          	jal	ra,aee <TIM_GetITStatus>
     2ea:	c105                	beqz	a0,30a <TIM3_IRQHandler+0x2e>
     2ec:	82018793          	addi	a5,gp,-2016 # 20000228 <uwtick>
     2f0:	4398                	lw	a4,0(a5)
     2f2:	0705                	addi	a4,a4,1
     2f4:	c398                	sw	a4,0(a5)
     2f6:	81a18793          	addi	a5,gp,-2022 # 20000222 <uart6_rec_tick>
     2fa:	2398                	lbu	a4,0(a5)
     2fc:	0705                	addi	a4,a4,1
     2fe:	a398                	sb	a4,0(a5)
     300:	81c18793          	addi	a5,gp,-2020 # 20000224 <uart8_rec_tick>
     304:	2398                	lbu	a4,0(a5)
     306:	0705                	addi	a4,a4,1
     308:	a398                	sb	a4,0(a5)
     30a:	40000537          	lui	a0,0x40000
     30e:	4585                	li	a1,1
     310:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     314:	7f2000ef          	jal	ra,b06 <TIM_ClearITPendingBit>
     318:	30200073          	mret

0000031c <scheduler_run>:
     31c:	ebbff2ef          	jal	t0,1d6 <__riscv_save_4>
     320:	200004b7          	lui	s1,0x20000
     324:	4401                	li	s0,0
     326:	00048493          	mv	s1,s1
     32a:	4a31                	li	s4,12
     32c:	8181c783          	lbu	a5,-2024(gp) # 20000220 <_edata>
     330:	00f46363          	bltu	s0,a5,336 <scheduler_run+0x1a>
     334:	bdd9                	j	20a <__riscv_restore_4>
     336:	034407b3          	mul	a5,s0,s4
     33a:	8201a683          	lw	a3,-2016(gp) # 20000228 <uwtick>
     33e:	97a6                	add	a5,a5,s1
     340:	4798                	lw	a4,8(a5)
     342:	43d0                	lw	a2,4(a5)
     344:	9732                	add	a4,a4,a2
     346:	00d77563          	bgeu	a4,a3,350 <scheduler_run+0x34>
     34a:	c794                	sw	a3,8(a5)
     34c:	439c                	lw	a5,0(a5)
     34e:	9782                	jalr	a5
     350:	0405                	addi	s0,s0,1
     352:	0ff47413          	andi	s0,s0,255
     356:	bfd9                	j	32c <scheduler_run+0x10>

00000358 <UART6_IRQHandler>:
     358:	1141                	addi	sp,sp,-16
     35a:	c622                	sw	s0,12(sp)
     35c:	40002437          	lui	s0,0x40002
     360:	52500593          	li	a1,1317
     364:	80040513          	addi	a0,s0,-2048 # 40001800 <_eusrstack+0x1fff9800>
     368:	0ab000ef          	jal	ra,c12 <USART_GetITStatus>
     36c:	c105                	beqz	a0,38c <UART6_IRQHandler+0x34>
     36e:	80040513          	addi	a0,s0,-2048
     372:	80018d23          	sb	zero,-2022(gp) # 20000222 <uart6_rec_tick>
     376:	07f000ef          	jal	ra,bf4 <USART_ReceiveData>
     37a:	81918713          	addi	a4,gp,-2023 # 20000221 <uart6_rec_index>
     37e:	2314                	lbu	a3,0(a4)
     380:	83418793          	addi	a5,gp,-1996 # 2000023c <uart6_rec_string>
     384:	97b6                	add	a5,a5,a3
     386:	0685                	addi	a3,a3,1
     388:	a388                	sb	a0,0(a5)
     38a:	a314                	sb	a3,0(a4)
     38c:	40002537          	lui	a0,0x40002
     390:	52500593          	li	a1,1317
     394:	80050513          	addi	a0,a0,-2048 # 40001800 <_eusrstack+0x1fff9800>
     398:	0b7000ef          	jal	ra,c4e <USART_ClearITPendingBit>
     39c:	4432                	lw	s0,12(sp)
     39e:	0141                	addi	sp,sp,16
     3a0:	30200073          	mret

000003a4 <UART8_IRQHandler>:
     3a4:	52500593          	li	a1,1317
     3a8:	40002537          	lui	a0,0x40002
     3ac:	067000ef          	jal	ra,c12 <USART_GetITStatus>
     3b0:	c105                	beqz	a0,3d0 <UART8_IRQHandler+0x2c>
     3b2:	40002537          	lui	a0,0x40002
     3b6:	80018e23          	sb	zero,-2020(gp) # 20000224 <uart8_rec_tick>
     3ba:	03b000ef          	jal	ra,bf4 <USART_ReceiveData>
     3be:	81b18713          	addi	a4,gp,-2021 # 20000223 <uart8_rec_index>
     3c2:	2314                	lbu	a3,0(a4)
     3c4:	93418793          	addi	a5,gp,-1740 # 2000033c <uart8_rec_string>
     3c8:	97b6                	add	a5,a5,a3
     3ca:	0685                	addi	a3,a3,1
     3cc:	a388                	sb	a0,0(a5)
     3ce:	a314                	sb	a3,0(a4)
     3d0:	52500593          	li	a1,1317
     3d4:	40002537          	lui	a0,0x40002
     3d8:	077000ef          	jal	ra,c4e <USART_ClearITPendingBit>
     3dc:	30200073          	mret

000003e0 <USART3_IRQHandler>:
     3e0:	1141                	addi	sp,sp,-16
     3e2:	c622                	sw	s0,12(sp)
     3e4:	40005437          	lui	s0,0x40005
     3e8:	52500593          	li	a1,1317
     3ec:	80040513          	addi	a0,s0,-2048 # 40004800 <_eusrstack+0x1fffc800>
     3f0:	023000ef          	jal	ra,c12 <USART_GetITStatus>
     3f4:	cd01                	beqz	a0,40c <USART3_IRQHandler+0x2c>
     3f6:	80040513          	addi	a0,s0,-2048
     3fa:	7fa000ef          	jal	ra,bf4 <USART_ReceiveData>
     3fe:	0ff57593          	andi	a1,a0,255
     402:	0585                	addi	a1,a1,1
     404:	80040513          	addi	a0,s0,-2048
     408:	7e4000ef          	jal	ra,bec <USART_SendData>
     40c:	40005537          	lui	a0,0x40005
     410:	52500593          	li	a1,1317
     414:	80050513          	addi	a0,a0,-2048 # 40004800 <_eusrstack+0x1fffc800>
     418:	037000ef          	jal	ra,c4e <USART_ClearITPendingBit>
     41c:	4432                	lw	s0,12(sp)
     41e:	0141                	addi	sp,sp,16
     420:	30200073          	mret

00000424 <main>:
     424:	dcdff2ef          	jal	t0,1f0 <__riscv_save_0>
     428:	1141                	addi	sp,sp,-16
     42a:	4509                	li	a0,2
     42c:	26e5                	jal	814 <NVIC_PriorityGroupConfig>
     42e:	22a5                	jal	596 <SystemCoreClockUpdate>
     430:	033000ef          	jal	ra,c62 <Delay_Init>
     434:	05f00593          	li	a1,95
     438:	3e800513          	li	a0,1000
     43c:	169000ef          	jal	ra,da4 <Tim3_Init>
     440:	1b5000ef          	jal	ra,df4 <Usart3_Init>
     444:	239000ef          	jal	ra,e7c <Uart8_Init>
     448:	6571                	lui	a0,0x1c
     44a:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x1a1d8>
     44e:	03f000ef          	jal	ra,c8c <USART_Printf_Init>
     452:	4709                	li	a4,2
     454:	80e18c23          	sb	a4,-2024(gp) # 20000220 <_edata>
     458:	0ad000ef          	jal	ra,d04 <audio_init>
     45c:	4551                	li	a0,20
     45e:	0f9000ef          	jal	ra,d56 <audio_yinliang>
     462:	4585                	li	a1,1
     464:	4541                	li	a0,16
     466:	c202                	sw	zero,4(sp)
     468:	c402                	sw	zero,8(sp)
     46a:	c602                	sw	zero,12(sp)
     46c:	2b8d                	jal	9de <RCC_APB2PeriphClockCmd>
     46e:	47a1                	li	a5,8
     470:	827c                	sh	a5,4(sp)
     472:	47c1                	li	a5,16
     474:	c63e                	sw	a5,12(sp)
     476:	004c                	addi	a1,sp,4
     478:	478d                	li	a5,3
     47a:	40011537          	lui	a0,0x40011
     47e:	c43e                	sw	a5,8(sp)
     480:	20000437          	lui	s0,0x20000
     484:	24e1                	jal	74c <GPIO_Init>
     486:	3d59                	jal	31c <scheduler_run>
     488:	20842783          	lw	a5,520(s0) # 20000208 <led_flag>
     48c:	45a1                	li	a1,8
     48e:	40011537          	lui	a0,0x40011
     492:	e399                	bnez	a5,498 <main+0x74>
     494:	2ea5                	jal	80c <GPIO_SetBits>
     496:	bfc5                	j	486 <main+0x62>
     498:	2ea5                	jal	810 <GPIO_ResetBits>
     49a:	b7f5                	j	486 <main+0x62>

0000049c <SystemInit>:
     49c:	400217b7          	lui	a5,0x40021
     4a0:	4398                	lw	a4,0(a5)
     4a2:	f0ff06b7          	lui	a3,0xf0ff0
     4a6:	1141                	addi	sp,sp,-16
     4a8:	00176713          	ori	a4,a4,1
     4ac:	c398                	sw	a4,0(a5)
     4ae:	43d8                	lw	a4,4(a5)
     4b0:	00020637          	lui	a2,0x20
     4b4:	8f75                	and	a4,a4,a3
     4b6:	c3d8                	sw	a4,4(a5)
     4b8:	4398                	lw	a4,0(a5)
     4ba:	fef706b7          	lui	a3,0xfef70
     4be:	16fd                	addi	a3,a3,-1
     4c0:	8f75                	and	a4,a4,a3
     4c2:	c398                	sw	a4,0(a5)
     4c4:	4398                	lw	a4,0(a5)
     4c6:	fffc06b7          	lui	a3,0xfffc0
     4ca:	16fd                	addi	a3,a3,-1
     4cc:	8f75                	and	a4,a4,a3
     4ce:	c398                	sw	a4,0(a5)
     4d0:	43d8                	lw	a4,4(a5)
     4d2:	ff0106b7          	lui	a3,0xff010
     4d6:	16fd                	addi	a3,a3,-1
     4d8:	8f75                	and	a4,a4,a3
     4da:	c3d8                	sw	a4,4(a5)
     4dc:	4398                	lw	a4,0(a5)
     4de:	ec0006b7          	lui	a3,0xec000
     4e2:	16fd                	addi	a3,a3,-1
     4e4:	8f75                	and	a4,a4,a3
     4e6:	c398                	sw	a4,0(a5)
     4e8:	00ff0737          	lui	a4,0xff0
     4ec:	c798                	sw	a4,8(a5)
     4ee:	0207a623          	sw	zero,44(a5) # 4002102c <_eusrstack+0x2001902c>
     4f2:	c402                	sw	zero,8(sp)
     4f4:	c602                	sw	zero,12(sp)
     4f6:	4398                	lw	a4,0(a5)
     4f8:	66c1                	lui	a3,0x10
     4fa:	8f55                	or	a4,a4,a3
     4fc:	c398                	sw	a4,0(a5)
     4fe:	400216b7          	lui	a3,0x40021
     502:	6705                	lui	a4,0x1
     504:	429c                	lw	a5,0(a3)
     506:	8ff1                	and	a5,a5,a2
     508:	c63e                	sw	a5,12(sp)
     50a:	47a2                	lw	a5,8(sp)
     50c:	0785                	addi	a5,a5,1
     50e:	c43e                	sw	a5,8(sp)
     510:	47b2                	lw	a5,12(sp)
     512:	e781                	bnez	a5,51a <SystemInit+0x7e>
     514:	47a2                	lw	a5,8(sp)
     516:	fee797e3          	bne	a5,a4,504 <SystemInit+0x68>
     51a:	400217b7          	lui	a5,0x40021
     51e:	439c                	lw	a5,0(a5)
     520:	00e79713          	slli	a4,a5,0xe
     524:	06075763          	bgez	a4,592 <SystemInit+0xf6>
     528:	4785                	li	a5,1
     52a:	c63e                	sw	a5,12(sp)
     52c:	4732                	lw	a4,12(sp)
     52e:	4785                	li	a5,1
     530:	04f71f63          	bne	a4,a5,58e <SystemInit+0xf2>
     534:	400217b7          	lui	a5,0x40021
     538:	43d8                	lw	a4,4(a5)
     53a:	ffc106b7          	lui	a3,0xffc10
     53e:	16fd                	addi	a3,a3,-1
     540:	c3d8                	sw	a4,4(a5)
     542:	43d8                	lw	a4,4(a5)
     544:	c3d8                	sw	a4,4(a5)
     546:	43d8                	lw	a4,4(a5)
     548:	40076713          	ori	a4,a4,1024
     54c:	c3d8                	sw	a4,4(a5)
     54e:	43d8                	lw	a4,4(a5)
     550:	8f75                	and	a4,a4,a3
     552:	c3d8                	sw	a4,4(a5)
     554:	43d8                	lw	a4,4(a5)
     556:	002906b7          	lui	a3,0x290
     55a:	8f55                	or	a4,a4,a3
     55c:	c3d8                	sw	a4,4(a5)
     55e:	4398                	lw	a4,0(a5)
     560:	010006b7          	lui	a3,0x1000
     564:	8f55                	or	a4,a4,a3
     566:	c398                	sw	a4,0(a5)
     568:	4398                	lw	a4,0(a5)
     56a:	00671693          	slli	a3,a4,0x6
     56e:	fe06dde3          	bgez	a3,568 <SystemInit+0xcc>
     572:	43d8                	lw	a4,4(a5)
     574:	400216b7          	lui	a3,0x40021
     578:	9b71                	andi	a4,a4,-4
     57a:	c3d8                	sw	a4,4(a5)
     57c:	43d8                	lw	a4,4(a5)
     57e:	00276713          	ori	a4,a4,2
     582:	c3d8                	sw	a4,4(a5)
     584:	4721                	li	a4,8
     586:	42dc                	lw	a5,4(a3)
     588:	8bb1                	andi	a5,a5,12
     58a:	fee79ee3          	bne	a5,a4,586 <SystemInit+0xea>
     58e:	0141                	addi	sp,sp,16
     590:	8082                	ret
     592:	c602                	sw	zero,12(sp)
     594:	bf61                	j	52c <SystemInit+0x90>

00000596 <SystemCoreClockUpdate>:
     596:	400216b7          	lui	a3,0x40021
     59a:	42d8                	lw	a4,4(a3)
     59c:	4611                	li	a2,4
     59e:	8b31                	andi	a4,a4,12
     5a0:	80418793          	addi	a5,gp,-2044 # 2000020c <SystemCoreClock>
     5a4:	00c70563          	beq	a4,a2,5ae <SystemCoreClockUpdate+0x18>
     5a8:	4621                	li	a2,8
     5aa:	02c70863          	beq	a4,a2,5da <SystemCoreClockUpdate+0x44>
     5ae:	007a1737          	lui	a4,0x7a1
     5b2:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     5b6:	c398                	sw	a4,0(a5)
     5b8:	40021737          	lui	a4,0x40021
     5bc:	4358                	lw	a4,4(a4)
     5be:	8311                	srli	a4,a4,0x4
     5c0:	00f77693          	andi	a3,a4,15
     5c4:	20000737          	lui	a4,0x20000
     5c8:	01870713          	addi	a4,a4,24 # 20000018 <AHBPrescTable>
     5cc:	9736                	add	a4,a4,a3
     5ce:	2314                	lbu	a3,0(a4)
     5d0:	4398                	lw	a4,0(a5)
     5d2:	00d75733          	srl	a4,a4,a3
     5d6:	c398                	sw	a4,0(a5)
     5d8:	8082                	ret
     5da:	42d8                	lw	a4,4(a3)
     5dc:	42d4                	lw	a3,4(a3)
     5de:	6641                	lui	a2,0x10
     5e0:	8349                	srli	a4,a4,0x12
     5e2:	8b3d                	andi	a4,a4,15
     5e4:	8ef1                	and	a3,a3,a2
     5e6:	00270613          	addi	a2,a4,2
     5ea:	cf15                	beqz	a4,626 <SystemCoreClockUpdate+0x90>
     5ec:	473d                	li	a4,15
     5ee:	02e60f63          	beq	a2,a4,62c <SystemCoreClockUpdate+0x96>
     5f2:	4741                	li	a4,16
     5f4:	02e60f63          	beq	a2,a4,632 <SystemCoreClockUpdate+0x9c>
     5f8:	4745                	li	a4,17
     5fa:	4581                	li	a1,0
     5fc:	00e61363          	bne	a2,a4,602 <SystemCoreClockUpdate+0x6c>
     600:	4641                	li	a2,16
     602:	e2a1                	bnez	a3,642 <SystemCoreClockUpdate+0xac>
     604:	40024737          	lui	a4,0x40024
     608:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     60c:	8b41                	andi	a4,a4,16
     60e:	c70d                	beqz	a4,638 <SystemCoreClockUpdate+0xa2>
     610:	007a1737          	lui	a4,0x7a1
     614:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     618:	02c70633          	mul	a2,a4,a2
     61c:	c390                	sw	a2,0(a5)
     61e:	ddc9                	beqz	a1,5b8 <SystemCoreClockUpdate+0x22>
     620:	4398                	lw	a4,0(a5)
     622:	8305                	srli	a4,a4,0x1
     624:	bf49                	j	5b6 <SystemCoreClockUpdate+0x20>
     626:	4581                	li	a1,0
     628:	4649                	li	a2,18
     62a:	bfe1                	j	602 <SystemCoreClockUpdate+0x6c>
     62c:	4585                	li	a1,1
     62e:	4635                	li	a2,13
     630:	bfc9                	j	602 <SystemCoreClockUpdate+0x6c>
     632:	4581                	li	a1,0
     634:	463d                	li	a2,15
     636:	b7f1                	j	602 <SystemCoreClockUpdate+0x6c>
     638:	003d1737          	lui	a4,0x3d1
     63c:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3ce8d8>
     640:	bfe1                	j	618 <SystemCoreClockUpdate+0x82>
     642:	40021537          	lui	a0,0x40021
     646:	5558                	lw	a4,44(a0)
     648:	00f71693          	slli	a3,a4,0xf
     64c:	5558                	lw	a4,44(a0)
     64e:	0406df63          	bgez	a3,6ac <SystemCoreClockUpdate+0x116>
     652:	8311                	srli	a4,a4,0x4
     654:	8b3d                	andi	a4,a4,15
     656:	00170693          	addi	a3,a4,1
     65a:	007a1737          	lui	a4,0x7a1
     65e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     662:	02d75733          	divu	a4,a4,a3
     666:	c398                	sw	a4,0(a5)
     668:	5554                	lw	a3,44(a0)
     66a:	82a1                	srli	a3,a3,0x8
     66c:	8abd                	andi	a3,a3,15
     66e:	e28d                	bnez	a3,690 <SystemCoreClockUpdate+0xfa>
     670:	4695                	li	a3,5
     672:	02d70733          	mul	a4,a4,a3
     676:	8305                	srli	a4,a4,0x1
     678:	c398                	sw	a4,0(a5)
     67a:	40021737          	lui	a4,0x40021
     67e:	5758                	lw	a4,44(a4)
     680:	4394                	lw	a3,0(a5)
     682:	8b3d                	andi	a4,a4,15
     684:	0705                	addi	a4,a4,1
     686:	02e6d733          	divu	a4,a3,a4
     68a:	c398                	sw	a4,0(a5)
     68c:	4398                	lw	a4,0(a5)
     68e:	b769                	j	618 <SystemCoreClockUpdate+0x82>
     690:	4505                	li	a0,1
     692:	00a69463          	bne	a3,a0,69a <SystemCoreClockUpdate+0x104>
     696:	46e5                	li	a3,25
     698:	bfe9                	j	672 <SystemCoreClockUpdate+0xdc>
     69a:	453d                	li	a0,15
     69c:	00a69663          	bne	a3,a0,6a8 <SystemCoreClockUpdate+0x112>
     6a0:	46d1                	li	a3,20
     6a2:	02e68733          	mul	a4,a3,a4
     6a6:	bfc9                	j	678 <SystemCoreClockUpdate+0xe2>
     6a8:	0689                	addi	a3,a3,2
     6aa:	bfe5                	j	6a2 <SystemCoreClockUpdate+0x10c>
     6ac:	8b3d                	andi	a4,a4,15
     6ae:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     6b2:	007a1737          	lui	a4,0x7a1
     6b6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     6ba:	02d75733          	divu	a4,a4,a3
     6be:	b7f1                	j	68a <SystemCoreClockUpdate+0xf4>

000006c0 <ADC1_2_IRQHandler>:
     6c0:	a001                	j	6c0 <ADC1_2_IRQHandler>

000006c2 <handle_reset>:
     6c2:	20000197          	auipc	gp,0x20000
     6c6:	34618193          	addi	gp,gp,838 # 20000a08 <__global_pointer$>
     6ca:	20008117          	auipc	sp,0x20008
     6ce:	93610113          	addi	sp,sp,-1738 # 20008000 <_eusrstack>
     6d2:	00002517          	auipc	a0,0x2
     6d6:	95650513          	addi	a0,a0,-1706 # 2028 <_data_lma>
     6da:	20000597          	auipc	a1,0x20000
     6de:	92658593          	addi	a1,a1,-1754 # 20000000 <_data_vma>
     6e2:	81818613          	addi	a2,gp,-2024 # 20000220 <_edata>
     6e6:	00c5fa63          	bgeu	a1,a2,6fa <handle_reset+0x38>
     6ea:	00052283          	lw	t0,0(a0)
     6ee:	0055a023          	sw	t0,0(a1)
     6f2:	0511                	addi	a0,a0,4
     6f4:	0591                	addi	a1,a1,4
     6f6:	fec5eae3          	bltu	a1,a2,6ea <handle_reset+0x28>
     6fa:	81818513          	addi	a0,gp,-2024 # 20000220 <_edata>
     6fe:	a3818593          	addi	a1,gp,-1480 # 20000440 <_ebss>
     702:	00b57763          	bgeu	a0,a1,710 <handle_reset+0x4e>
     706:	00052023          	sw	zero,0(a0)
     70a:	0511                	addi	a0,a0,4
     70c:	feb56de3          	bltu	a0,a1,706 <handle_reset+0x44>
     710:	42fd                	li	t0,31
     712:	bc029073          	csrw	0xbc0,t0
     716:	42ad                	li	t0,11
     718:	80429073          	csrw	0x804,t0
     71c:	000062b7          	lui	t0,0x6
     720:	08828293          	addi	t0,t0,136 # 6088 <_data_lma+0x4060>
     724:	30029073          	csrw	mstatus,t0
     728:	00000297          	auipc	t0,0x0
     72c:	8dc28293          	addi	t0,t0,-1828 # 4 <_einit>
     730:	0032e293          	ori	t0,t0,3
     734:	30529073          	csrw	mtvec,t0
     738:	d65ff0ef          	jal	ra,49c <SystemInit>
     73c:	00000297          	auipc	t0,0x0
     740:	ce828293          	addi	t0,t0,-792 # 424 <main>
     744:	34129073          	csrw	mepc,t0
     748:	30200073          	mret

0000074c <GPIO_Init>:
     74c:	459c                	lw	a5,8(a1)
     74e:	0107f713          	andi	a4,a5,16
     752:	00f7f813          	andi	a6,a5,15
     756:	c701                	beqz	a4,75e <GPIO_Init+0x12>
     758:	41d8                	lw	a4,4(a1)
     75a:	00e86833          	or	a6,a6,a4
     75e:	218e                	lhu	a1,0(a1)
     760:	0ff5f713          	andi	a4,a1,255
     764:	c339                	beqz	a4,7aa <GPIO_Init+0x5e>
     766:	4118                	lw	a4,0(a0)
     768:	4681                	li	a3,0
     76a:	4e85                	li	t4,1
     76c:	4f3d                	li	t5,15
     76e:	02800f93          	li	t6,40
     772:	04800293          	li	t0,72
     776:	4e21                	li	t3,8
     778:	00de9633          	sll	a2,t4,a3
     77c:	00c5f8b3          	and	a7,a1,a2
     780:	03161163          	bne	a2,a7,7a2 <GPIO_Init+0x56>
     784:	00269893          	slli	a7,a3,0x2
     788:	011f1333          	sll	t1,t5,a7
     78c:	fff34313          	not	t1,t1
     790:	00e37733          	and	a4,t1,a4
     794:	011818b3          	sll	a7,a6,a7
     798:	00e8e733          	or	a4,a7,a4
     79c:	05f79f63          	bne	a5,t6,7fa <GPIO_Init+0xae>
     7a0:	c950                	sw	a2,20(a0)
     7a2:	0685                	addi	a3,a3,1
     7a4:	fdc69ae3          	bne	a3,t3,778 <GPIO_Init+0x2c>
     7a8:	c118                	sw	a4,0(a0)
     7aa:	0ff00713          	li	a4,255
     7ae:	04b77563          	bgeu	a4,a1,7f8 <GPIO_Init+0xac>
     7b2:	4154                	lw	a3,4(a0)
     7b4:	4621                	li	a2,8
     7b6:	4e85                	li	t4,1
     7b8:	4f3d                	li	t5,15
     7ba:	02800f93          	li	t6,40
     7be:	04800293          	li	t0,72
     7c2:	4e41                	li	t3,16
     7c4:	00ce98b3          	sll	a7,t4,a2
     7c8:	0115f733          	and	a4,a1,a7
     7cc:	02e89263          	bne	a7,a4,7f0 <GPIO_Init+0xa4>
     7d0:	00261713          	slli	a4,a2,0x2
     7d4:	1701                	addi	a4,a4,-32
     7d6:	00ef1333          	sll	t1,t5,a4
     7da:	fff34313          	not	t1,t1
     7de:	00d376b3          	and	a3,t1,a3
     7e2:	00e81733          	sll	a4,a6,a4
     7e6:	8ed9                	or	a3,a3,a4
     7e8:	01f79d63          	bne	a5,t6,802 <__stack_size+0x2>
     7ec:	01152a23          	sw	a7,20(a0)
     7f0:	0605                	addi	a2,a2,1
     7f2:	fdc619e3          	bne	a2,t3,7c4 <GPIO_Init+0x78>
     7f6:	c154                	sw	a3,4(a0)
     7f8:	8082                	ret
     7fa:	fa5794e3          	bne	a5,t0,7a2 <GPIO_Init+0x56>
     7fe:	c910                	sw	a2,16(a0)
     800:	b74d                	j	7a2 <GPIO_Init+0x56>
     802:	fe5797e3          	bne	a5,t0,7f0 <GPIO_Init+0xa4>
     806:	01152823          	sw	a7,16(a0)
     80a:	b7dd                	j	7f0 <GPIO_Init+0xa4>

0000080c <GPIO_SetBits>:
     80c:	c90c                	sw	a1,16(a0)
     80e:	8082                	ret

00000810 <GPIO_ResetBits>:
     810:	c94c                	sw	a1,20(a0)
     812:	8082                	ret

00000814 <NVIC_PriorityGroupConfig>:
     814:	82a1a223          	sw	a0,-2012(gp) # 2000022c <NVIC_Priority_Group>
     818:	8082                	ret

0000081a <NVIC_Init>:
     81a:	8241a703          	lw	a4,-2012(gp) # 2000022c <NVIC_Priority_Group>
     81e:	4789                	li	a5,2
     820:	2110                	lbu	a2,0(a0)
     822:	02f71163          	bne	a4,a5,844 <NVIC_Init+0x2a>
     826:	3114                	lbu	a3,1(a0)
     828:	478d                	li	a5,3
     82a:	00d7ed63          	bltu	a5,a3,844 <NVIC_Init+0x2a>
     82e:	213c                	lbu	a5,2(a0)
     830:	069a                	slli	a3,a3,0x6
     832:	e000e737          	lui	a4,0xe000e
     836:	0796                	slli	a5,a5,0x5
     838:	8fd5                	or	a5,a5,a3
     83a:	0ff7f793          	andi	a5,a5,255
     83e:	9732                	add	a4,a4,a2
     840:	40f70023          	sb	a5,1024(a4) # e000e400 <_eusrstack+0xc0006400>
     844:	4154                	lw	a3,4(a0)
     846:	4705                	li	a4,1
     848:	00565793          	srli	a5,a2,0x5
     84c:	00c71733          	sll	a4,a4,a2
     850:	ca89                	beqz	a3,862 <NVIC_Init+0x48>
     852:	04078793          	addi	a5,a5,64 # 40021040 <_eusrstack+0x20019040>
     856:	078a                	slli	a5,a5,0x2
     858:	e000e6b7          	lui	a3,0xe000e
     85c:	97b6                	add	a5,a5,a3
     85e:	c398                	sw	a4,0(a5)
     860:	8082                	ret
     862:	06078793          	addi	a5,a5,96
     866:	bfc5                	j	856 <NVIC_Init+0x3c>

00000868 <RCC_GetClocksFreq>:
     868:	40021737          	lui	a4,0x40021
     86c:	435c                	lw	a5,4(a4)
     86e:	4691                	li	a3,4
     870:	8bb1                	andi	a5,a5,12
     872:	00d78563          	beq	a5,a3,87c <RCC_GetClocksFreq+0x14>
     876:	46a1                	li	a3,8
     878:	06d78263          	beq	a5,a3,8dc <RCC_GetClocksFreq+0x74>
     87c:	007a17b7          	lui	a5,0x7a1
     880:	20078793          	addi	a5,a5,512 # 7a1200 <_data_lma+0x79f1d8>
     884:	c11c                	sw	a5,0(a0)
     886:	40021637          	lui	a2,0x40021
     88a:	425c                	lw	a5,4(a2)
     88c:	20000737          	lui	a4,0x20000
     890:	02870713          	addi	a4,a4,40 # 20000028 <APBAHBPrescTable>
     894:	8391                	srli	a5,a5,0x4
     896:	8bbd                	andi	a5,a5,15
     898:	97ba                	add	a5,a5,a4
     89a:	2394                	lbu	a3,0(a5)
     89c:	411c                	lw	a5,0(a0)
     89e:	00d7d7b3          	srl	a5,a5,a3
     8a2:	c15c                	sw	a5,4(a0)
     8a4:	4254                	lw	a3,4(a2)
     8a6:	82a1                	srli	a3,a3,0x8
     8a8:	8a9d                	andi	a3,a3,7
     8aa:	96ba                	add	a3,a3,a4
     8ac:	2294                	lbu	a3,0(a3)
     8ae:	00d7d6b3          	srl	a3,a5,a3
     8b2:	c514                	sw	a3,8(a0)
     8b4:	4254                	lw	a3,4(a2)
     8b6:	82ad                	srli	a3,a3,0xb
     8b8:	8a9d                	andi	a3,a3,7
     8ba:	9736                	add	a4,a4,a3
     8bc:	2318                	lbu	a4,0(a4)
     8be:	00e7d7b3          	srl	a5,a5,a4
     8c2:	c55c                	sw	a5,12(a0)
     8c4:	4258                	lw	a4,4(a2)
     8c6:	8339                	srli	a4,a4,0xe
     8c8:	00377693          	andi	a3,a4,3
     8cc:	80818713          	addi	a4,gp,-2040 # 20000210 <ADCPrescTable>
     8d0:	9736                	add	a4,a4,a3
     8d2:	2318                	lbu	a4,0(a4)
     8d4:	02e7d7b3          	divu	a5,a5,a4
     8d8:	c91c                	sw	a5,16(a0)
     8da:	8082                	ret
     8dc:	435c                	lw	a5,4(a4)
     8de:	4358                	lw	a4,4(a4)
     8e0:	66c1                	lui	a3,0x10
     8e2:	83c9                	srli	a5,a5,0x12
     8e4:	8f75                	and	a4,a4,a3
     8e6:	1ffff6b7          	lui	a3,0x1ffff
     8ea:	70c6a683          	lw	a3,1804(a3) # 1ffff70c <_data_lma+0x1fffd6e4>
     8ee:	8bbd                	andi	a5,a5,15
     8f0:	0789                	addi	a5,a5,2
     8f2:	01169613          	slli	a2,a3,0x11
     8f6:	00064863          	bltz	a2,906 <RCC_GetClocksFreq+0x9e>
     8fa:	46c5                	li	a3,17
     8fc:	4601                	li	a2,0
     8fe:	02d79263          	bne	a5,a3,922 <RCC_GetClocksFreq+0xba>
     902:	47c9                	li	a5,18
     904:	a839                	j	922 <RCC_GetClocksFreq+0xba>
     906:	4689                	li	a3,2
     908:	02d78f63          	beq	a5,a3,946 <RCC_GetClocksFreq+0xde>
     90c:	46bd                	li	a3,15
     90e:	02d78e63          	beq	a5,a3,94a <RCC_GetClocksFreq+0xe2>
     912:	46c1                	li	a3,16
     914:	02d78e63          	beq	a5,a3,950 <RCC_GetClocksFreq+0xe8>
     918:	46c5                	li	a3,17
     91a:	4601                	li	a2,0
     91c:	00d79363          	bne	a5,a3,922 <RCC_GetClocksFreq+0xba>
     920:	47c1                	li	a5,16
     922:	ef1d                	bnez	a4,960 <RCC_GetClocksFreq+0xf8>
     924:	40024737          	lui	a4,0x40024
     928:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     92c:	8b41                	andi	a4,a4,16
     92e:	c705                	beqz	a4,956 <RCC_GetClocksFreq+0xee>
     930:	007a1737          	lui	a4,0x7a1
     934:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     938:	02f707b3          	mul	a5,a4,a5
     93c:	c11c                	sw	a5,0(a0)
     93e:	d621                	beqz	a2,886 <RCC_GetClocksFreq+0x1e>
     940:	411c                	lw	a5,0(a0)
     942:	8385                	srli	a5,a5,0x1
     944:	b781                	j	884 <RCC_GetClocksFreq+0x1c>
     946:	4601                	li	a2,0
     948:	bf6d                	j	902 <RCC_GetClocksFreq+0x9a>
     94a:	4605                	li	a2,1
     94c:	47b5                	li	a5,13
     94e:	bfd1                	j	922 <RCC_GetClocksFreq+0xba>
     950:	4601                	li	a2,0
     952:	47bd                	li	a5,15
     954:	b7f9                	j	922 <RCC_GetClocksFreq+0xba>
     956:	003d1737          	lui	a4,0x3d1
     95a:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3ce8d8>
     95e:	bfe9                	j	938 <RCC_GetClocksFreq+0xd0>
     960:	400215b7          	lui	a1,0x40021
     964:	55d8                	lw	a4,44(a1)
     966:	00f71693          	slli	a3,a4,0xf
     96a:	55d8                	lw	a4,44(a1)
     96c:	0406df63          	bgez	a3,9ca <RCC_GetClocksFreq+0x162>
     970:	8311                	srli	a4,a4,0x4
     972:	8b3d                	andi	a4,a4,15
     974:	00170693          	addi	a3,a4,1
     978:	007a1737          	lui	a4,0x7a1
     97c:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     980:	02d75733          	divu	a4,a4,a3
     984:	c118                	sw	a4,0(a0)
     986:	55d4                	lw	a3,44(a1)
     988:	82a1                	srli	a3,a3,0x8
     98a:	8abd                	andi	a3,a3,15
     98c:	e28d                	bnez	a3,9ae <RCC_GetClocksFreq+0x146>
     98e:	4695                	li	a3,5
     990:	02d70733          	mul	a4,a4,a3
     994:	8305                	srli	a4,a4,0x1
     996:	c118                	sw	a4,0(a0)
     998:	40021737          	lui	a4,0x40021
     99c:	5758                	lw	a4,44(a4)
     99e:	4114                	lw	a3,0(a0)
     9a0:	8b3d                	andi	a4,a4,15
     9a2:	0705                	addi	a4,a4,1
     9a4:	02e6d733          	divu	a4,a3,a4
     9a8:	c118                	sw	a4,0(a0)
     9aa:	4118                	lw	a4,0(a0)
     9ac:	b771                	j	938 <RCC_GetClocksFreq+0xd0>
     9ae:	4585                	li	a1,1
     9b0:	00b69463          	bne	a3,a1,9b8 <RCC_GetClocksFreq+0x150>
     9b4:	46e5                	li	a3,25
     9b6:	bfe9                	j	990 <RCC_GetClocksFreq+0x128>
     9b8:	45bd                	li	a1,15
     9ba:	00b69663          	bne	a3,a1,9c6 <RCC_GetClocksFreq+0x15e>
     9be:	46d1                	li	a3,20
     9c0:	02e68733          	mul	a4,a3,a4
     9c4:	bfc9                	j	996 <RCC_GetClocksFreq+0x12e>
     9c6:	0689                	addi	a3,a3,2
     9c8:	bfe5                	j	9c0 <RCC_GetClocksFreq+0x158>
     9ca:	8b3d                	andi	a4,a4,15
     9cc:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     9d0:	007a1737          	lui	a4,0x7a1
     9d4:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     9d8:	02d75733          	divu	a4,a4,a3
     9dc:	b7f1                	j	9a8 <RCC_GetClocksFreq+0x140>

000009de <RCC_APB2PeriphClockCmd>:
     9de:	c599                	beqz	a1,9ec <RCC_APB2PeriphClockCmd+0xe>
     9e0:	40021737          	lui	a4,0x40021
     9e4:	4f1c                	lw	a5,24(a4)
     9e6:	8d5d                	or	a0,a0,a5
     9e8:	cf08                	sw	a0,24(a4)
     9ea:	8082                	ret
     9ec:	400217b7          	lui	a5,0x40021
     9f0:	4f98                	lw	a4,24(a5)
     9f2:	fff54513          	not	a0,a0
     9f6:	8d79                	and	a0,a0,a4
     9f8:	cf88                	sw	a0,24(a5)
     9fa:	8082                	ret

000009fc <RCC_APB1PeriphClockCmd>:
     9fc:	c599                	beqz	a1,a0a <RCC_APB1PeriphClockCmd+0xe>
     9fe:	40021737          	lui	a4,0x40021
     a02:	4f5c                	lw	a5,28(a4)
     a04:	8d5d                	or	a0,a0,a5
     a06:	cf48                	sw	a0,28(a4)
     a08:	8082                	ret
     a0a:	400217b7          	lui	a5,0x40021
     a0e:	4fd8                	lw	a4,28(a5)
     a10:	fff54513          	not	a0,a0
     a14:	8d79                	and	a0,a0,a4
     a16:	cfc8                	sw	a0,28(a5)
     a18:	8082                	ret

00000a1a <TIM_TimeBaseInit>:
     a1a:	211e                	lhu	a5,0(a0)
     a1c:	40013737          	lui	a4,0x40013
     a20:	c0070693          	addi	a3,a4,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a24:	07c2                	slli	a5,a5,0x10
     a26:	83c1                	srli	a5,a5,0x10
     a28:	04d50063          	beq	a0,a3,a68 <TIM_TimeBaseInit+0x4e>
     a2c:	400006b7          	lui	a3,0x40000
     a30:	02d50c63          	beq	a0,a3,a68 <TIM_TimeBaseInit+0x4e>
     a34:	40068693          	addi	a3,a3,1024 # 40000400 <_eusrstack+0x1fff8400>
     a38:	02d50863          	beq	a0,a3,a68 <TIM_TimeBaseInit+0x4e>
     a3c:	400016b7          	lui	a3,0x40001
     a40:	80068613          	addi	a2,a3,-2048 # 40000800 <_eusrstack+0x1fff8800>
     a44:	02c50263          	beq	a0,a2,a68 <TIM_TimeBaseInit+0x4e>
     a48:	c0068693          	addi	a3,a3,-1024
     a4c:	00d50e63          	beq	a0,a3,a68 <TIM_TimeBaseInit+0x4e>
     a50:	40070713          	addi	a4,a4,1024
     a54:	00e50a63          	beq	a0,a4,a68 <TIM_TimeBaseInit+0x4e>
     a58:	40015737          	lui	a4,0x40015
     a5c:	c0070693          	addi	a3,a4,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     a60:	00d50463          	beq	a0,a3,a68 <TIM_TimeBaseInit+0x4e>
     a64:	00e51663          	bne	a0,a4,a70 <TIM_TimeBaseInit+0x56>
     a68:	21ba                	lhu	a4,2(a1)
     a6a:	f8f7f793          	andi	a5,a5,-113
     a6e:	8fd9                	or	a5,a5,a4
     a70:	40001737          	lui	a4,0x40001
     a74:	00e50c63          	beq	a0,a4,a8c <TIM_TimeBaseInit+0x72>
     a78:	40070713          	addi	a4,a4,1024 # 40001400 <_eusrstack+0x1fff9400>
     a7c:	00e50863          	beq	a0,a4,a8c <TIM_TimeBaseInit+0x72>
     a80:	cff7f793          	andi	a5,a5,-769
     a84:	21fa                	lhu	a4,6(a1)
     a86:	07c2                	slli	a5,a5,0x10
     a88:	83c1                	srli	a5,a5,0x10
     a8a:	8fd9                	or	a5,a5,a4
     a8c:	a11e                	sh	a5,0(a0)
     a8e:	21de                	lhu	a5,4(a1)
     a90:	b55e                	sh	a5,44(a0)
     a92:	219e                	lhu	a5,0(a1)
     a94:	b51e                	sh	a5,40(a0)
     a96:	400137b7          	lui	a5,0x40013
     a9a:	c0078713          	addi	a4,a5,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a9e:	00e50e63          	beq	a0,a4,aba <TIM_TimeBaseInit+0xa0>
     aa2:	40078793          	addi	a5,a5,1024
     aa6:	00f50a63          	beq	a0,a5,aba <TIM_TimeBaseInit+0xa0>
     aaa:	400157b7          	lui	a5,0x40015
     aae:	c0078713          	addi	a4,a5,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     ab2:	00e50463          	beq	a0,a4,aba <TIM_TimeBaseInit+0xa0>
     ab6:	00f51463          	bne	a0,a5,abe <TIM_TimeBaseInit+0xa4>
     aba:	259c                	lbu	a5,8(a1)
     abc:	b91e                	sh	a5,48(a0)
     abe:	4785                	li	a5,1
     ac0:	a95e                	sh	a5,20(a0)
     ac2:	8082                	ret

00000ac4 <TIM_Cmd>:
     ac4:	211e                	lhu	a5,0(a0)
     ac6:	c589                	beqz	a1,ad0 <TIM_Cmd+0xc>
     ac8:	0017e793          	ori	a5,a5,1
     acc:	a11e                	sh	a5,0(a0)
     ace:	8082                	ret
     ad0:	07c2                	slli	a5,a5,0x10
     ad2:	83c1                	srli	a5,a5,0x10
     ad4:	9bf9                	andi	a5,a5,-2
     ad6:	07c2                	slli	a5,a5,0x10
     ad8:	83c1                	srli	a5,a5,0x10
     ada:	bfcd                	j	acc <TIM_Cmd+0x8>

00000adc <TIM_ITConfig>:
     adc:	255e                	lhu	a5,12(a0)
     ade:	c601                	beqz	a2,ae6 <TIM_ITConfig+0xa>
     ae0:	8ddd                	or	a1,a1,a5
     ae2:	a54e                	sh	a1,12(a0)
     ae4:	8082                	ret
     ae6:	fff5c593          	not	a1,a1
     aea:	8dfd                	and	a1,a1,a5
     aec:	bfdd                	j	ae2 <TIM_ITConfig+0x6>

00000aee <TIM_GetITStatus>:
     aee:	291e                	lhu	a5,16(a0)
     af0:	254a                	lhu	a0,12(a0)
     af2:	8fed                	and	a5,a5,a1
     af4:	0542                	slli	a0,a0,0x10
     af6:	8141                	srli	a0,a0,0x10
     af8:	c789                	beqz	a5,b02 <TIM_GetITStatus+0x14>
     afa:	8d6d                	and	a0,a0,a1
     afc:	00a03533          	snez	a0,a0
     b00:	8082                	ret
     b02:	4501                	li	a0,0
     b04:	8082                	ret

00000b06 <TIM_ClearITPendingBit>:
     b06:	fff5c593          	not	a1,a1
     b0a:	05c2                	slli	a1,a1,0x10
     b0c:	81c1                	srli	a1,a1,0x10
     b0e:	a90e                	sh	a1,16(a0)
     b10:	8082                	ret

00000b12 <USART_Init>:
     b12:	edeff2ef          	jal	t0,1f0 <__riscv_save_0>
     b16:	2916                	lhu	a3,16(a0)
     b18:	77f5                	lui	a5,0xffffd
     b1a:	17fd                	addi	a5,a5,-1
     b1c:	8ff5                	and	a5,a5,a3
     b1e:	21f6                	lhu	a3,6(a1)
     b20:	25da                	lhu	a4,12(a1)
     b22:	7179                	addi	sp,sp,-48
     b24:	8fd5                	or	a5,a5,a3
     b26:	a91e                	sh	a5,16(a0)
     b28:	2556                	lhu	a3,12(a0)
     b2a:	77fd                	lui	a5,0xfffff
     b2c:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <_eusrstack+0xdfff69f3>
     b30:	8ff5                	and	a5,a5,a3
     b32:	21d6                	lhu	a3,4(a1)
     b34:	842a                	mv	s0,a0
     b36:	c62e                	sw	a1,12(sp)
     b38:	8fd5                	or	a5,a5,a3
     b3a:	2596                	lhu	a3,8(a1)
     b3c:	8fd5                	or	a5,a5,a3
     b3e:	25b6                	lhu	a3,10(a1)
     b40:	8fd5                	or	a5,a5,a3
     b42:	a55e                	sh	a5,12(a0)
     b44:	295e                	lhu	a5,20(a0)
     b46:	07c2                	slli	a5,a5,0x10
     b48:	83c1                	srli	a5,a5,0x10
     b4a:	cff7f793          	andi	a5,a5,-769
     b4e:	8fd9                	or	a5,a5,a4
     b50:	a95e                	sh	a5,20(a0)
     b52:	0868                	addi	a0,sp,28
     b54:	3b11                	jal	868 <RCC_GetClocksFreq>
     b56:	400147b7          	lui	a5,0x40014
     b5a:	80078793          	addi	a5,a5,-2048 # 40013800 <_eusrstack+0x2000b800>
     b5e:	45b2                	lw	a1,12(sp)
     b60:	02f41e63          	bne	s0,a5,b9c <USART_Init+0x8a>
     b64:	57a2                	lw	a5,40(sp)
     b66:	4765                	li	a4,25
     b68:	02e787b3          	mul	a5,a5,a4
     b6c:	4198                	lw	a4,0(a1)
     b6e:	06400693          	li	a3,100
     b72:	070a                	slli	a4,a4,0x2
     b74:	02e7d7b3          	divu	a5,a5,a4
     b78:	02d7d733          	divu	a4,a5,a3
     b7c:	02d7f7b3          	remu	a5,a5,a3
     b80:	0712                	slli	a4,a4,0x4
     b82:	0792                	slli	a5,a5,0x4
     b84:	03278793          	addi	a5,a5,50
     b88:	02d7d7b3          	divu	a5,a5,a3
     b8c:	8bbd                	andi	a5,a5,15
     b8e:	8fd9                	or	a5,a5,a4
     b90:	07c2                	slli	a5,a5,0x10
     b92:	83c1                	srli	a5,a5,0x10
     b94:	a41e                	sh	a5,8(s0)
     b96:	6145                	addi	sp,sp,48
     b98:	e7cff06f          	j	214 <__riscv_restore_0>
     b9c:	5792                	lw	a5,36(sp)
     b9e:	b7e1                	j	b66 <USART_Init+0x54>

00000ba0 <USART_Cmd>:
     ba0:	c591                	beqz	a1,bac <USART_Cmd+0xc>
     ba2:	255e                	lhu	a5,12(a0)
     ba4:	6709                	lui	a4,0x2
     ba6:	8fd9                	or	a5,a5,a4
     ba8:	a55e                	sh	a5,12(a0)
     baa:	8082                	ret
     bac:	255a                	lhu	a4,12(a0)
     bae:	77f9                	lui	a5,0xffffe
     bb0:	17fd                	addi	a5,a5,-1
     bb2:	8ff9                	and	a5,a5,a4
     bb4:	bfd5                	j	ba8 <USART_Cmd+0x8>

00000bb6 <USART_ITConfig>:
     bb6:	0ff5f713          	andi	a4,a1,255
     bba:	4785                	li	a5,1
     bbc:	8315                	srli	a4,a4,0x5
     bbe:	00b795b3          	sll	a1,a5,a1
     bc2:	00f71963          	bne	a4,a5,bd4 <USART_ITConfig+0x1e>
     bc6:	00c50793          	addi	a5,a0,12
     bca:	4398                	lw	a4,0(a5)
     bcc:	ce01                	beqz	a2,be4 <USART_ITConfig+0x2e>
     bce:	8dd9                	or	a1,a1,a4
     bd0:	c38c                	sw	a1,0(a5)
     bd2:	8082                	ret
     bd4:	4689                	li	a3,2
     bd6:	01450793          	addi	a5,a0,20
     bda:	fed718e3          	bne	a4,a3,bca <USART_ITConfig+0x14>
     bde:	01050793          	addi	a5,a0,16
     be2:	b7e5                	j	bca <USART_ITConfig+0x14>
     be4:	fff5c593          	not	a1,a1
     be8:	8df9                	and	a1,a1,a4
     bea:	b7dd                	j	bd0 <USART_ITConfig+0x1a>

00000bec <USART_SendData>:
     bec:	1ff5f593          	andi	a1,a1,511
     bf0:	a14e                	sh	a1,4(a0)
     bf2:	8082                	ret

00000bf4 <USART_ReceiveData>:
     bf4:	214a                	lhu	a0,4(a0)
     bf6:	1ff57513          	andi	a0,a0,511
     bfa:	8082                	ret

00000bfc <USART_GetFlagStatus>:
     bfc:	210a                	lhu	a0,0(a0)
     bfe:	8d6d                	and	a0,a0,a1
     c00:	00a03533          	snez	a0,a0
     c04:	8082                	ret

00000c06 <USART_ClearFlag>:
     c06:	fff5c593          	not	a1,a1
     c0a:	05c2                	slli	a1,a1,0x10
     c0c:	81c1                	srli	a1,a1,0x10
     c0e:	a10e                	sh	a1,0(a0)
     c10:	8082                	ret

00000c12 <USART_GetITStatus>:
     c12:	0ff5f713          	andi	a4,a1,255
     c16:	4685                	li	a3,1
     c18:	8315                	srli	a4,a4,0x5
     c1a:	00b697b3          	sll	a5,a3,a1
     c1e:	02d71163          	bne	a4,a3,c40 <USART_GetITStatus+0x2e>
     c22:	255a                	lhu	a4,12(a0)
     c24:	8ff9                	and	a5,a5,a4
     c26:	211a                	lhu	a4,0(a0)
     c28:	4501                	li	a0,0
     c2a:	0742                	slli	a4,a4,0x10
     c2c:	8341                	srli	a4,a4,0x10
     c2e:	cb81                	beqz	a5,c3e <USART_GetITStatus+0x2c>
     c30:	4505                	li	a0,1
     c32:	81a1                	srli	a1,a1,0x8
     c34:	00b515b3          	sll	a1,a0,a1
     c38:	8f6d                	and	a4,a4,a1
     c3a:	00e03533          	snez	a0,a4
     c3e:	8082                	ret
     c40:	4689                	li	a3,2
     c42:	00d71463          	bne	a4,a3,c4a <USART_GetITStatus+0x38>
     c46:	291a                	lhu	a4,16(a0)
     c48:	bff1                	j	c24 <USART_GetITStatus+0x12>
     c4a:	295a                	lhu	a4,20(a0)
     c4c:	bfe1                	j	c24 <USART_GetITStatus+0x12>

00000c4e <USART_ClearITPendingBit>:
     c4e:	81a1                	srli	a1,a1,0x8
     c50:	4785                	li	a5,1
     c52:	00b797b3          	sll	a5,a5,a1
     c56:	fff7c793          	not	a5,a5
     c5a:	07c2                	slli	a5,a5,0x10
     c5c:	83c1                	srli	a5,a5,0x10
     c5e:	a11e                	sh	a5,0(a0)
     c60:	8082                	ret

00000c62 <Delay_Init>:
     c62:	200007b7          	lui	a5,0x20000
     c66:	20c7a783          	lw	a5,524(a5) # 2000020c <SystemCoreClock>
     c6a:	007a1737          	lui	a4,0x7a1
     c6e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79f1d8>
     c72:	02e7d7b3          	divu	a5,a5,a4
     c76:	0ff7f793          	andi	a5,a5,255
     c7a:	82f18523          	sb	a5,-2006(gp) # 20000232 <p_us>
     c7e:	3e800713          	li	a4,1000
     c82:	02e787b3          	mul	a5,a5,a4
     c86:	82f19423          	sh	a5,-2008(gp) # 20000230 <p_ms>
     c8a:	8082                	ret

00000c8c <USART_Printf_Init>:
     c8c:	d64ff2ef          	jal	t0,1f0 <__riscv_save_0>
     c90:	842a                	mv	s0,a0
     c92:	6511                	lui	a0,0x4
     c94:	1101                	addi	sp,sp,-32
     c96:	4585                	li	a1,1
     c98:	0511                	addi	a0,a0,4
     c9a:	3391                	jal	9de <RCC_APB2PeriphClockCmd>
     c9c:	20000793          	li	a5,512
     ca0:	827c                	sh	a5,4(sp)
     ca2:	40011537          	lui	a0,0x40011
     ca6:	478d                	li	a5,3
     ca8:	c43e                	sw	a5,8(sp)
     caa:	004c                	addi	a1,sp,4
     cac:	47e1                	li	a5,24
     cae:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     cb2:	c63e                	sw	a5,12(sp)
     cb4:	3c61                	jal	74c <GPIO_Init>
     cb6:	c822                	sw	s0,16(sp)
     cb8:	40014437          	lui	s0,0x40014
     cbc:	000807b7          	lui	a5,0x80
     cc0:	080c                	addi	a1,sp,16
     cc2:	80040513          	addi	a0,s0,-2048 # 40013800 <_eusrstack+0x2000b800>
     cc6:	cc3e                	sw	a5,24(sp)
     cc8:	ca02                	sw	zero,20(sp)
     cca:	00011e23          	sh	zero,28(sp)
     cce:	3591                	jal	b12 <USART_Init>
     cd0:	4585                	li	a1,1
     cd2:	80040513          	addi	a0,s0,-2048
     cd6:	35e9                	jal	ba0 <USART_Cmd>
     cd8:	6105                	addi	sp,sp,32
     cda:	d3aff06f          	j	214 <__riscv_restore_0>

00000cde <_sbrk>:
     cde:	80c18713          	addi	a4,gp,-2036 # 20000214 <curbrk.5276>
     ce2:	431c                	lw	a5,0(a4)
     ce4:	a3818693          	addi	a3,gp,-1480 # 20000440 <_ebss>
     ce8:	953e                	add	a0,a0,a5
     cea:	00d56b63          	bltu	a0,a3,d00 <_sbrk+0x22>
     cee:	200086b7          	lui	a3,0x20008
     cf2:	80068693          	addi	a3,a3,-2048 # 20007800 <_heap_end>
     cf6:	00a6e563          	bltu	a3,a0,d00 <_sbrk+0x22>
     cfa:	c308                	sw	a0,0(a4)
     cfc:	853e                	mv	a0,a5
     cfe:	8082                	ret
     d00:	57fd                	li	a5,-1
     d02:	bfed                	j	cfc <_sbrk+0x1e>

00000d04 <audio_init>:
     d04:	cecff2ef          	jal	t0,1f0 <__riscv_save_0>
     d08:	20f5                	jal	df4 <Usart3_Init>
     d0a:	d0aff06f          	j	214 <__riscv_restore_0>

00000d0e <audio_play>:
     d0e:	ce2ff2ef          	jal	t0,1f0 <__riscv_save_0>
     d12:	004107b7          	lui	a5,0x410
     d16:	1141                	addi	sp,sp,-16
     d18:	57e78793          	addi	a5,a5,1406 # 41057e <_data_lma+0x40e556>
     d1c:	c43e                	sw	a5,8(sp)
     d1e:	8648                	sb	a0,12(sp)
     d20:	57bd                	li	a5,-17
     d22:	04454513          	xori	a0,a0,68
     d26:	400054b7          	lui	s1,0x40005
     d2a:	86c8                	sb	a0,13(sp)
     d2c:	875c                	sb	a5,14(sp)
     d2e:	4401                	li	s0,0
     d30:	80048493          	addi	s1,s1,-2048 # 40004800 <_eusrstack+0x1fffc800>
     d34:	491d                	li	s2,7
     d36:	003c                	addi	a5,sp,8
     d38:	97a2                	add	a5,a5,s0
     d3a:	238c                	lbu	a1,0(a5)
     d3c:	8526                	mv	a0,s1
     d3e:	357d                	jal	bec <USART_SendData>
     d40:	04000593          	li	a1,64
     d44:	8526                	mv	a0,s1
     d46:	3d5d                	jal	bfc <USART_GetFlagStatus>
     d48:	dd65                	beqz	a0,d40 <audio_play+0x32>
     d4a:	0405                	addi	s0,s0,1
     d4c:	ff2415e3          	bne	s0,s2,d36 <audio_play+0x28>
     d50:	0141                	addi	sp,sp,16
     d52:	cc2ff06f          	j	214 <__riscv_restore_0>

00000d56 <audio_yinliang>:
     d56:	c9aff2ef          	jal	t0,1f0 <__riscv_save_0>
     d5a:	1141                	addi	sp,sp,-16
     d5c:	47e00793          	li	a5,1150
     d60:	847c                	sh	a5,8(sp)
     d62:	03100793          	li	a5,49
     d66:	855c                	sb	a5,10(sp)
     d68:	85c8                	sb	a0,11(sp)
     d6a:	57bd                	li	a5,-17
     d6c:	03554513          	xori	a0,a0,53
     d70:	400054b7          	lui	s1,0x40005
     d74:	8648                	sb	a0,12(sp)
     d76:	86dc                	sb	a5,13(sp)
     d78:	4401                	li	s0,0
     d7a:	80048493          	addi	s1,s1,-2048 # 40004800 <_eusrstack+0x1fffc800>
     d7e:	4919                	li	s2,6
     d80:	003c                	addi	a5,sp,8
     d82:	97a2                	add	a5,a5,s0
     d84:	238c                	lbu	a1,0(a5)
     d86:	8526                	mv	a0,s1
     d88:	3595                	jal	bec <USART_SendData>
     d8a:	04000593          	li	a1,64
     d8e:	8526                	mv	a0,s1
     d90:	35b5                	jal	bfc <USART_GetFlagStatus>
     d92:	dd65                	beqz	a0,d8a <audio_yinliang+0x34>
     d94:	0405                	addi	s0,s0,1
     d96:	ff2415e3          	bne	s0,s2,d80 <audio_yinliang+0x2a>
     d9a:	0141                	addi	sp,sp,16
     d9c:	c78ff06f          	j	214 <__riscv_restore_0>

00000da0 <SPI3_IRQHandler>:
     da0:	30200073          	mret

00000da4 <Tim3_Init>:
     da4:	c4cff2ef          	jal	t0,1f0 <__riscv_save_0>
     da8:	1101                	addi	sp,sp,-32
     daa:	84aa                	mv	s1,a0
     dac:	842e                	mv	s0,a1
     dae:	4509                	li	a0,2
     db0:	4585                	li	a1,1
     db2:	31a9                	jal	9fc <RCC_APB1PeriphClockCmd>
     db4:	82e0                	sh	s0,20(sp)
     db6:	40000437          	lui	s0,0x40000
     dba:	084c                	addi	a1,sp,20
     dbc:	40040513          	addi	a0,s0,1024 # 40000400 <_eusrstack+0x1fff8400>
     dc0:	84e4                	sh	s1,24(sp)
     dc2:	00011d23          	sh	zero,26(sp)
     dc6:	00011b23          	sh	zero,22(sp)
     dca:	3981                	jal	a1a <TIM_TimeBaseInit>
     dcc:	4605                	li	a2,1
     dce:	04100593          	li	a1,65
     dd2:	40040513          	addi	a0,s0,1024
     dd6:	3319                	jal	adc <TIM_ITConfig>
     dd8:	02d00793          	li	a5,45
     ddc:	865c                	sb	a5,12(sp)
     dde:	0068                	addi	a0,sp,12
     de0:	4785                	li	a5,1
     de2:	c83e                	sw	a5,16(sp)
     de4:	3c1d                	jal	81a <NVIC_Init>
     de6:	4585                	li	a1,1
     de8:	40040513          	addi	a0,s0,1024
     dec:	39e1                	jal	ac4 <TIM_Cmd>
     dee:	6105                	addi	sp,sp,32
     df0:	c24ff06f          	j	214 <__riscv_restore_0>

00000df4 <Usart3_Init>:
     df4:	bfcff2ef          	jal	t0,1f0 <__riscv_save_0>
     df8:	7179                	addi	sp,sp,-48
     dfa:	4585                	li	a1,1
     dfc:	00040537          	lui	a0,0x40
     e00:	3ef5                	jal	9fc <RCC_APB1PeriphClockCmd>
     e02:	4585                	li	a1,1
     e04:	4521                	li	a0,8
     e06:	3ee1                	jal	9de <RCC_APB2PeriphClockCmd>
     e08:	6785                	lui	a5,0x1
     e0a:	c0078793          	addi	a5,a5,-1024 # c00 <USART_GetFlagStatus+0x4>
     e0e:	82fc                	sh	a5,20(sp)
     e10:	40011537          	lui	a0,0x40011
     e14:	478d                	li	a5,3
     e16:	cc3e                	sw	a5,24(sp)
     e18:	084c                	addi	a1,sp,20
     e1a:	47e1                	li	a5,24
     e1c:	c0050513          	addi	a0,a0,-1024 # 40010c00 <_eusrstack+0x20008c00>
     e20:	ce3e                	sw	a5,28(sp)
     e22:	92bff0ef          	jal	ra,74c <GPIO_Init>
     e26:	6789                	lui	a5,0x2
     e28:	40005437          	lui	s0,0x40005
     e2c:	58078793          	addi	a5,a5,1408 # 2580 <_data_lma+0x558>
     e30:	d03e                	sw	a5,32(sp)
     e32:	100c                	addi	a1,sp,32
     e34:	000c07b7          	lui	a5,0xc0
     e38:	80040513          	addi	a0,s0,-2048 # 40004800 <_eusrstack+0x1fffc800>
     e3c:	d43e                	sw	a5,40(sp)
     e3e:	d202                	sw	zero,36(sp)
     e40:	02011623          	sh	zero,44(sp)
     e44:	31f9                	jal	b12 <USART_Init>
     e46:	4605                	li	a2,1
     e48:	52500593          	li	a1,1317
     e4c:	80040513          	addi	a0,s0,-2048
     e50:	339d                	jal	bb6 <USART_ITConfig>
     e52:	13700793          	li	a5,311
     e56:	867c                	sh	a5,12(sp)
     e58:	4785                	li	a5,1
     e5a:	875c                	sb	a5,14(sp)
     e5c:	0068                	addi	a0,sp,12
     e5e:	4785                	li	a5,1
     e60:	c83e                	sw	a5,16(sp)
     e62:	3a65                	jal	81a <NVIC_Init>
     e64:	80040513          	addi	a0,s0,-2048
     e68:	4585                	li	a1,1
     e6a:	3b1d                	jal	ba0 <USART_Cmd>
     e6c:	04000593          	li	a1,64
     e70:	80040513          	addi	a0,s0,-2048
     e74:	3b49                	jal	c06 <USART_ClearFlag>
     e76:	6145                	addi	sp,sp,48
     e78:	b9cff06f          	j	214 <__riscv_restore_0>

00000e7c <Uart8_Init>:
     e7c:	b74ff2ef          	jal	t0,1f0 <__riscv_save_0>
     e80:	7179                	addi	sp,sp,-48
     e82:	4585                	li	a1,1
     e84:	10000513          	li	a0,256
     e88:	3e95                	jal	9fc <RCC_APB1PeriphClockCmd>
     e8a:	4585                	li	a1,1
     e8c:	4541                	li	a0,16
     e8e:	3e81                	jal	9de <RCC_APB2PeriphClockCmd>
     e90:	03000793          	li	a5,48
     e94:	82fc                	sh	a5,20(sp)
     e96:	478d                	li	a5,3
     e98:	cc3e                	sw	a5,24(sp)
     e9a:	084c                	addi	a1,sp,20
     e9c:	47e1                	li	a5,24
     e9e:	40011537          	lui	a0,0x40011
     ea2:	ce3e                	sw	a5,28(sp)
     ea4:	8a9ff0ef          	jal	ra,74c <GPIO_Init>
     ea8:	6789                	lui	a5,0x2
     eaa:	58078793          	addi	a5,a5,1408 # 2580 <_data_lma+0x558>
     eae:	d03e                	sw	a5,32(sp)
     eb0:	100c                	addi	a1,sp,32
     eb2:	000c07b7          	lui	a5,0xc0
     eb6:	40002537          	lui	a0,0x40002
     eba:	d43e                	sw	a5,40(sp)
     ebc:	d202                	sw	zero,36(sp)
     ebe:	02011623          	sh	zero,44(sp)
     ec2:	3981                	jal	b12 <USART_Init>
     ec4:	4605                	li	a2,1
     ec6:	52500593          	li	a1,1317
     eca:	40002537          	lui	a0,0x40002
     ece:	31e5                	jal	bb6 <USART_ITConfig>
     ed0:	05900793          	li	a5,89
     ed4:	865c                	sb	a5,12(sp)
     ed6:	0068                	addi	a0,sp,12
     ed8:	4785                	li	a5,1
     eda:	c83e                	sw	a5,16(sp)
     edc:	93fff0ef          	jal	ra,81a <NVIC_Init>
     ee0:	4585                	li	a1,1
     ee2:	40002537          	lui	a0,0x40002
     ee6:	396d                	jal	ba0 <USART_Cmd>
     ee8:	40002537          	lui	a0,0x40002
     eec:	04000593          	li	a1,64
     ef0:	80050513          	addi	a0,a0,-2048 # 40001800 <_eusrstack+0x1fff9800>
     ef4:	3b09                	jal	c06 <USART_ClearFlag>
     ef6:	6145                	addi	sp,sp,48
     ef8:	b1cff06f          	j	214 <__riscv_restore_0>

00000efc <usart1_send_string>:
     efc:	af4ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f00:	400144b7          	lui	s1,0x40014
     f04:	842a                	mv	s0,a0
     f06:	00b50933          	add	s2,a0,a1
     f0a:	80048493          	addi	s1,s1,-2048 # 40013800 <_eusrstack+0x2000b800>
     f0e:	01241463          	bne	s0,s2,f16 <usart1_send_string+0x1a>
     f12:	b02ff06f          	j	214 <__riscv_restore_0>
     f16:	200c                	lbu	a1,0(s0)
     f18:	8526                	mv	a0,s1
     f1a:	39c9                	jal	bec <USART_SendData>
     f1c:	04000593          	li	a1,64
     f20:	8526                	mv	a0,s1
     f22:	39e9                	jal	bfc <USART_GetFlagStatus>
     f24:	dd65                	beqz	a0,f1c <usart1_send_string+0x20>
     f26:	0405                	addi	s0,s0,1
     f28:	b7dd                	j	f0e <usart1_send_string+0x12>

00000f2a <memcpy>:
     f2a:	00a5c7b3          	xor	a5,a1,a0
     f2e:	8b8d                	andi	a5,a5,3
     f30:	00c50733          	add	a4,a0,a2
     f34:	e781                	bnez	a5,f3c <memcpy+0x12>
     f36:	478d                	li	a5,3
     f38:	00c7ed63          	bltu	a5,a2,f52 <memcpy+0x28>
     f3c:	87aa                	mv	a5,a0
     f3e:	08e57e63          	bgeu	a0,a4,fda <memcpy+0xb0>
     f42:	2194                	lbu	a3,0(a1)
     f44:	0785                	addi	a5,a5,1
     f46:	0585                	addi	a1,a1,1
     f48:	fed78fa3          	sb	a3,-1(a5) # bffff <_data_lma+0xbdfd7>
     f4c:	fee7ebe3          	bltu	a5,a4,f42 <memcpy+0x18>
     f50:	8082                	ret
     f52:	00357693          	andi	a3,a0,3
     f56:	87aa                	mv	a5,a0
     f58:	ca89                	beqz	a3,f6a <memcpy+0x40>
     f5a:	2194                	lbu	a3,0(a1)
     f5c:	0785                	addi	a5,a5,1
     f5e:	0585                	addi	a1,a1,1
     f60:	fed78fa3          	sb	a3,-1(a5)
     f64:	0037f693          	andi	a3,a5,3
     f68:	bfc5                	j	f58 <memcpy+0x2e>
     f6a:	ffc77693          	andi	a3,a4,-4
     f6e:	fe068613          	addi	a2,a3,-32
     f72:	06c7f063          	bgeu	a5,a2,fd2 <memcpy+0xa8>
     f76:	0005a383          	lw	t2,0(a1) # 40021000 <_eusrstack+0x20019000>
     f7a:	0045a283          	lw	t0,4(a1)
     f7e:	0085af83          	lw	t6,8(a1)
     f82:	00c5af03          	lw	t5,12(a1)
     f86:	0105ae83          	lw	t4,16(a1)
     f8a:	0145ae03          	lw	t3,20(a1)
     f8e:	0185a303          	lw	t1,24(a1)
     f92:	01c5a883          	lw	a7,28(a1)
     f96:	02458593          	addi	a1,a1,36
     f9a:	0077a023          	sw	t2,0(a5)
     f9e:	ffc5a803          	lw	a6,-4(a1)
     fa2:	0057a223          	sw	t0,4(a5)
     fa6:	01f7a423          	sw	t6,8(a5)
     faa:	01e7a623          	sw	t5,12(a5)
     fae:	01d7a823          	sw	t4,16(a5)
     fb2:	01c7aa23          	sw	t3,20(a5)
     fb6:	0067ac23          	sw	t1,24(a5)
     fba:	0117ae23          	sw	a7,28(a5)
     fbe:	02478793          	addi	a5,a5,36
     fc2:	ff07ae23          	sw	a6,-4(a5)
     fc6:	b775                	j	f72 <memcpy+0x48>
     fc8:	4190                	lw	a2,0(a1)
     fca:	0791                	addi	a5,a5,4
     fcc:	0591                	addi	a1,a1,4
     fce:	fec7ae23          	sw	a2,-4(a5)
     fd2:	fed7ebe3          	bltu	a5,a3,fc8 <memcpy+0x9e>
     fd6:	f6e7e6e3          	bltu	a5,a4,f42 <memcpy+0x18>
     fda:	8082                	ret

00000fdc <_free_r>:
     fdc:	c1cd                	beqz	a1,107e <_free_r+0xa2>
     fde:	ffc5a783          	lw	a5,-4(a1)
     fe2:	1141                	addi	sp,sp,-16
     fe4:	c422                	sw	s0,8(sp)
     fe6:	c606                	sw	ra,12(sp)
     fe8:	c226                	sw	s1,4(sp)
     fea:	ffc58413          	addi	s0,a1,-4
     fee:	0007d363          	bgez	a5,ff4 <_free_r+0x18>
     ff2:	943e                	add	s0,s0,a5
     ff4:	84aa                	mv	s1,a0
     ff6:	2489                	jal	1238 <__malloc_lock>
     ff8:	82c18793          	addi	a5,gp,-2004 # 20000234 <__malloc_free_list>
     ffc:	439c                	lw	a5,0(a5)
     ffe:	eb99                	bnez	a5,1014 <_free_r+0x38>
    1000:	00042223          	sw	zero,4(s0)
    1004:	8281a623          	sw	s0,-2004(gp) # 20000234 <__malloc_free_list>
    1008:	4422                	lw	s0,8(sp)
    100a:	40b2                	lw	ra,12(sp)
    100c:	8526                	mv	a0,s1
    100e:	4492                	lw	s1,4(sp)
    1010:	0141                	addi	sp,sp,16
    1012:	a425                	j	123a <__malloc_unlock>
    1014:	00f47e63          	bgeu	s0,a5,1030 <_free_r+0x54>
    1018:	4014                	lw	a3,0(s0)
    101a:	00d40733          	add	a4,s0,a3
    101e:	00e79663          	bne	a5,a4,102a <_free_r+0x4e>
    1022:	4398                	lw	a4,0(a5)
    1024:	43dc                	lw	a5,4(a5)
    1026:	9736                	add	a4,a4,a3
    1028:	c018                	sw	a4,0(s0)
    102a:	c05c                	sw	a5,4(s0)
    102c:	bfe1                	j	1004 <_free_r+0x28>
    102e:	87ba                	mv	a5,a4
    1030:	43d8                	lw	a4,4(a5)
    1032:	c319                	beqz	a4,1038 <_free_r+0x5c>
    1034:	fee47de3          	bgeu	s0,a4,102e <_free_r+0x52>
    1038:	4394                	lw	a3,0(a5)
    103a:	00d78633          	add	a2,a5,a3
    103e:	00861f63          	bne	a2,s0,105c <_free_r+0x80>
    1042:	4010                	lw	a2,0(s0)
    1044:	96b2                	add	a3,a3,a2
    1046:	c394                	sw	a3,0(a5)
    1048:	00d78633          	add	a2,a5,a3
    104c:	fac71ee3          	bne	a4,a2,1008 <_free_r+0x2c>
    1050:	4310                	lw	a2,0(a4)
    1052:	4358                	lw	a4,4(a4)
    1054:	96b2                	add	a3,a3,a2
    1056:	c394                	sw	a3,0(a5)
    1058:	c3d8                	sw	a4,4(a5)
    105a:	b77d                	j	1008 <_free_r+0x2c>
    105c:	00c47563          	bgeu	s0,a2,1066 <_free_r+0x8a>
    1060:	47b1                	li	a5,12
    1062:	c09c                	sw	a5,0(s1)
    1064:	b755                	j	1008 <_free_r+0x2c>
    1066:	4010                	lw	a2,0(s0)
    1068:	00c406b3          	add	a3,s0,a2
    106c:	00d71663          	bne	a4,a3,1078 <_free_r+0x9c>
    1070:	4314                	lw	a3,0(a4)
    1072:	4358                	lw	a4,4(a4)
    1074:	96b2                	add	a3,a3,a2
    1076:	c014                	sw	a3,0(s0)
    1078:	c058                	sw	a4,4(s0)
    107a:	c3c0                	sw	s0,4(a5)
    107c:	b771                	j	1008 <_free_r+0x2c>
    107e:	8082                	ret

00001080 <_malloc_r>:
    1080:	1101                	addi	sp,sp,-32
    1082:	ca26                	sw	s1,20(sp)
    1084:	00358493          	addi	s1,a1,3
    1088:	98f1                	andi	s1,s1,-4
    108a:	ce06                	sw	ra,28(sp)
    108c:	cc22                	sw	s0,24(sp)
    108e:	c84a                	sw	s2,16(sp)
    1090:	c64e                	sw	s3,12(sp)
    1092:	04a1                	addi	s1,s1,8
    1094:	47b1                	li	a5,12
    1096:	04f4f163          	bgeu	s1,a5,10d8 <_malloc_r+0x58>
    109a:	44b1                	li	s1,12
    109c:	04b4e063          	bltu	s1,a1,10dc <_malloc_r+0x5c>
    10a0:	892a                	mv	s2,a0
    10a2:	2a59                	jal	1238 <__malloc_lock>
    10a4:	82c18793          	addi	a5,gp,-2004 # 20000234 <__malloc_free_list>
    10a8:	4398                	lw	a4,0(a5)
    10aa:	843a                	mv	s0,a4
    10ac:	e031                	bnez	s0,10f0 <_malloc_r+0x70>
    10ae:	83018793          	addi	a5,gp,-2000 # 20000238 <__malloc_sbrk_start>
    10b2:	439c                	lw	a5,0(a5)
    10b4:	e791                	bnez	a5,10c0 <_malloc_r+0x40>
    10b6:	4581                	li	a1,0
    10b8:	854a                	mv	a0,s2
    10ba:	2851                	jal	114e <_sbrk_r>
    10bc:	82a1a823          	sw	a0,-2000(gp) # 20000238 <__malloc_sbrk_start>
    10c0:	85a6                	mv	a1,s1
    10c2:	854a                	mv	a0,s2
    10c4:	2069                	jal	114e <_sbrk_r>
    10c6:	59fd                	li	s3,-1
    10c8:	07351763          	bne	a0,s3,1136 <_malloc_r+0xb6>
    10cc:	47b1                	li	a5,12
    10ce:	00f92023          	sw	a5,0(s2)
    10d2:	854a                	mv	a0,s2
    10d4:	229d                	jal	123a <__malloc_unlock>
    10d6:	a029                	j	10e0 <_malloc_r+0x60>
    10d8:	fc04d2e3          	bgez	s1,109c <_malloc_r+0x1c>
    10dc:	47b1                	li	a5,12
    10de:	c11c                	sw	a5,0(a0)
    10e0:	4501                	li	a0,0
    10e2:	40f2                	lw	ra,28(sp)
    10e4:	4462                	lw	s0,24(sp)
    10e6:	44d2                	lw	s1,20(sp)
    10e8:	4942                	lw	s2,16(sp)
    10ea:	49b2                	lw	s3,12(sp)
    10ec:	6105                	addi	sp,sp,32
    10ee:	8082                	ret
    10f0:	401c                	lw	a5,0(s0)
    10f2:	8f85                	sub	a5,a5,s1
    10f4:	0207ce63          	bltz	a5,1130 <_malloc_r+0xb0>
    10f8:	46ad                	li	a3,11
    10fa:	00f6f663          	bgeu	a3,a5,1106 <_malloc_r+0x86>
    10fe:	c01c                	sw	a5,0(s0)
    1100:	943e                	add	s0,s0,a5
    1102:	c004                	sw	s1,0(s0)
    1104:	a031                	j	1110 <_malloc_r+0x90>
    1106:	405c                	lw	a5,4(s0)
    1108:	02871263          	bne	a4,s0,112c <_malloc_r+0xac>
    110c:	82f1a623          	sw	a5,-2004(gp) # 20000234 <__malloc_free_list>
    1110:	854a                	mv	a0,s2
    1112:	2225                	jal	123a <__malloc_unlock>
    1114:	00b40513          	addi	a0,s0,11
    1118:	00440793          	addi	a5,s0,4
    111c:	9961                	andi	a0,a0,-8
    111e:	40f50733          	sub	a4,a0,a5
    1122:	d361                	beqz	a4,10e2 <_malloc_r+0x62>
    1124:	943a                	add	s0,s0,a4
    1126:	8f89                	sub	a5,a5,a0
    1128:	c01c                	sw	a5,0(s0)
    112a:	bf65                	j	10e2 <_malloc_r+0x62>
    112c:	c35c                	sw	a5,4(a4)
    112e:	b7cd                	j	1110 <_malloc_r+0x90>
    1130:	8722                	mv	a4,s0
    1132:	4040                	lw	s0,4(s0)
    1134:	bfa5                	j	10ac <_malloc_r+0x2c>
    1136:	00350413          	addi	s0,a0,3
    113a:	9871                	andi	s0,s0,-4
    113c:	fc8503e3          	beq	a0,s0,1102 <_malloc_r+0x82>
    1140:	40a405b3          	sub	a1,s0,a0
    1144:	854a                	mv	a0,s2
    1146:	2021                	jal	114e <_sbrk_r>
    1148:	fb351de3          	bne	a0,s3,1102 <_malloc_r+0x82>
    114c:	b741                	j	10cc <_malloc_r+0x4c>

0000114e <_sbrk_r>:
    114e:	1141                	addi	sp,sp,-16
    1150:	c422                	sw	s0,8(sp)
    1152:	842a                	mv	s0,a0
    1154:	852e                	mv	a0,a1
    1156:	a201aa23          	sw	zero,-1484(gp) # 2000043c <errno>
    115a:	c606                	sw	ra,12(sp)
    115c:	3649                	jal	cde <_sbrk>
    115e:	57fd                	li	a5,-1
    1160:	00f51763          	bne	a0,a5,116e <_sbrk_r+0x20>
    1164:	a3418793          	addi	a5,gp,-1484 # 2000043c <errno>
    1168:	439c                	lw	a5,0(a5)
    116a:	c391                	beqz	a5,116e <_sbrk_r+0x20>
    116c:	c01c                	sw	a5,0(s0)
    116e:	40b2                	lw	ra,12(sp)
    1170:	4422                	lw	s0,8(sp)
    1172:	0141                	addi	sp,sp,16
    1174:	8082                	ret

00001176 <siscanf>:
    1176:	7171                	addi	sp,sp,-176
    1178:	d33e                	sw	a5,164(sp)
    117a:	20400793          	li	a5,516
    117e:	c706                	sw	ra,140(sp)
    1180:	c62e                	sw	a1,12(sp)
    1182:	cd32                	sw	a2,152(sp)
    1184:	cf36                	sw	a3,156(sp)
    1186:	d13a                	sw	a4,160(sp)
    1188:	d542                	sw	a6,168(sp)
    118a:	d746                	sw	a7,172(sp)
    118c:	02f11223          	sh	a5,36(sp)
    1190:	cc2a                	sw	a0,24(sp)
    1192:	d42a                	sw	a0,40(sp)
    1194:	281d                	jal	11ca <strlen>
    1196:	00000797          	auipc	a5,0x0
    119a:	03078793          	addi	a5,a5,48 # 11c6 <__seofread>
    119e:	de3e                	sw	a5,60(sp)
    11a0:	57fd                	li	a5,-1
    11a2:	45b2                	lw	a1,12(sp)
    11a4:	02f11323          	sh	a5,38(sp)
    11a8:	81018793          	addi	a5,gp,-2032 # 20000218 <_impure_ptr>
    11ac:	ce2a                	sw	a0,28(sp)
    11ae:	d62a                	sw	a0,44(sp)
    11b0:	4388                	lw	a0,0(a5)
    11b2:	0934                	addi	a3,sp,152
    11b4:	862e                	mv	a2,a1
    11b6:	082c                	addi	a1,sp,24
    11b8:	c682                	sw	zero,76(sp)
    11ba:	d082                	sw	zero,96(sp)
    11bc:	ca36                	sw	a3,20(sp)
    11be:	2a99                	jal	1314 <__ssvfiscanf_r>
    11c0:	40ba                	lw	ra,140(sp)
    11c2:	614d                	addi	sp,sp,176
    11c4:	8082                	ret

000011c6 <__seofread>:
    11c6:	4501                	li	a0,0
    11c8:	8082                	ret

000011ca <strlen>:
    11ca:	87aa                	mv	a5,a0
    11cc:	0785                	addi	a5,a5,1
    11ce:	fff7c703          	lbu	a4,-1(a5)
    11d2:	ff6d                	bnez	a4,11cc <strlen+0x2>
    11d4:	40a78533          	sub	a0,a5,a0
    11d8:	157d                	addi	a0,a0,-1
    11da:	8082                	ret

000011dc <strncmp>:
    11dc:	c215                	beqz	a2,1200 <strncmp+0x24>
    11de:	167d                	addi	a2,a2,-1
    11e0:	4701                	li	a4,0
    11e2:	00e507b3          	add	a5,a0,a4
    11e6:	00e586b3          	add	a3,a1,a4
    11ea:	239c                	lbu	a5,0(a5)
    11ec:	2294                	lbu	a3,0(a3)
    11ee:	00d79663          	bne	a5,a3,11fa <strncmp+0x1e>
    11f2:	00c70463          	beq	a4,a2,11fa <strncmp+0x1e>
    11f6:	0705                	addi	a4,a4,1
    11f8:	f7ed                	bnez	a5,11e2 <strncmp+0x6>
    11fa:	40d78533          	sub	a0,a5,a3
    11fe:	8082                	ret
    1200:	4501                	li	a0,0
    1202:	8082                	ret

00001204 <strstr>:
    1204:	2118                	lbu	a4,0(a0)
    1206:	219c                	lbu	a5,0(a1)
    1208:	e701                	bnez	a4,1210 <strstr+0xc>
    120a:	c391                	beqz	a5,120e <strstr+0xa>
    120c:	4501                	li	a0,0
    120e:	8082                	ret
    1210:	4781                	li	a5,0
    1212:	a039                	j	1220 <strstr+0x1c>
    1214:	311c                	lbu	a5,1(a0)
    1216:	00150713          	addi	a4,a0,1
    121a:	dbed                	beqz	a5,120c <strstr+0x8>
    121c:	4781                	li	a5,0
    121e:	853a                	mv	a0,a4
    1220:	00f58733          	add	a4,a1,a5
    1224:	2318                	lbu	a4,0(a4)
    1226:	d765                	beqz	a4,120e <strstr+0xa>
    1228:	00f506b3          	add	a3,a0,a5
    122c:	2294                	lbu	a3,0(a3)
    122e:	fee693e3          	bne	a3,a4,1214 <strstr+0x10>
    1232:	0785                	addi	a5,a5,1
    1234:	872a                	mv	a4,a0
    1236:	b7e5                	j	121e <strstr+0x1a>

00001238 <__malloc_lock>:
    1238:	8082                	ret

0000123a <__malloc_unlock>:
    123a:	8082                	ret

0000123c <_sungetc_r>:
    123c:	1141                	addi	sp,sp,-16
    123e:	c606                	sw	ra,12(sp)
    1240:	c422                	sw	s0,8(sp)
    1242:	c226                	sw	s1,4(sp)
    1244:	c04a                	sw	s2,0(sp)
    1246:	57fd                	li	a5,-1
    1248:	00f59a63          	bne	a1,a5,125c <_sungetc_r+0x20>
    124c:	54fd                	li	s1,-1
    124e:	40b2                	lw	ra,12(sp)
    1250:	4422                	lw	s0,8(sp)
    1252:	8526                	mv	a0,s1
    1254:	4902                	lw	s2,0(sp)
    1256:	4492                	lw	s1,4(sp)
    1258:	0141                	addi	sp,sp,16
    125a:	8082                	ret
    125c:	265e                	lhu	a5,12(a2)
    125e:	0ff5f913          	andi	s2,a1,255
    1262:	0ff5f493          	andi	s1,a1,255
    1266:	fdf7f793          	andi	a5,a5,-33
    126a:	a65e                	sh	a5,12(a2)
    126c:	5a5c                	lw	a5,52(a2)
    126e:	4258                	lw	a4,4(a2)
    1270:	c785                	beqz	a5,1298 <_sungetc_r+0x5c>
    1272:	5e1c                	lw	a5,56(a2)
    1274:	8432                	mv	s0,a2
    1276:	00f75c63          	bge	a4,a5,128e <_sungetc_r+0x52>
    127a:	401c                	lw	a5,0(s0)
    127c:	fff78713          	addi	a4,a5,-1
    1280:	c018                	sw	a4,0(s0)
    1282:	ff278fa3          	sb	s2,-1(a5)
    1286:	405c                	lw	a5,4(s0)
    1288:	0785                	addi	a5,a5,1
    128a:	c05c                	sw	a5,4(s0)
    128c:	b7c9                	j	124e <_sungetc_r+0x12>
    128e:	85b2                	mv	a1,a2
    1290:	26d000ef          	jal	ra,1cfc <__submore>
    1294:	d17d                	beqz	a0,127a <_sungetc_r+0x3e>
    1296:	bf5d                	j	124c <_sungetc_r+0x10>
    1298:	4a14                	lw	a3,16(a2)
    129a:	421c                	lw	a5,0(a2)
    129c:	ce81                	beqz	a3,12b4 <_sungetc_r+0x78>
    129e:	00f6fb63          	bgeu	a3,a5,12b4 <_sungetc_r+0x78>
    12a2:	fff7c683          	lbu	a3,-1(a5)
    12a6:	00969763          	bne	a3,s1,12b4 <_sungetc_r+0x78>
    12aa:	17fd                	addi	a5,a5,-1
    12ac:	0705                	addi	a4,a4,1
    12ae:	c21c                	sw	a5,0(a2)
    12b0:	c258                	sw	a4,4(a2)
    12b2:	bf71                	j	124e <_sungetc_r+0x12>
    12b4:	de5c                	sw	a5,60(a2)
    12b6:	04460793          	addi	a5,a2,68 # 40021044 <_eusrstack+0x20019044>
    12ba:	da5c                	sw	a5,52(a2)
    12bc:	478d                	li	a5,3
    12be:	de1c                	sw	a5,56(a2)
    12c0:	04660793          	addi	a5,a2,70
    12c4:	c21c                	sw	a5,0(a2)
    12c6:	4785                	li	a5,1
    12c8:	c238                	sw	a4,64(a2)
    12ca:	05260323          	sb	s2,70(a2)
    12ce:	c25c                	sw	a5,4(a2)
    12d0:	bfbd                	j	124e <_sungetc_r+0x12>

000012d2 <__ssrefill_r>:
    12d2:	1141                	addi	sp,sp,-16
    12d4:	c422                	sw	s0,8(sp)
    12d6:	842e                	mv	s0,a1
    12d8:	59cc                	lw	a1,52(a1)
    12da:	c606                	sw	ra,12(sp)
    12dc:	c195                	beqz	a1,1300 <__ssrefill_r+0x2e>
    12de:	04440793          	addi	a5,s0,68
    12e2:	00f58363          	beq	a1,a5,12e8 <__ssrefill_r+0x16>
    12e6:	39dd                	jal	fdc <_free_r>
    12e8:	403c                	lw	a5,64(s0)
    12ea:	02042a23          	sw	zero,52(s0)
    12ee:	c05c                	sw	a5,4(s0)
    12f0:	cb81                	beqz	a5,1300 <__ssrefill_r+0x2e>
    12f2:	5c5c                	lw	a5,60(s0)
    12f4:	4501                	li	a0,0
    12f6:	c01c                	sw	a5,0(s0)
    12f8:	40b2                	lw	ra,12(sp)
    12fa:	4422                	lw	s0,8(sp)
    12fc:	0141                	addi	sp,sp,16
    12fe:	8082                	ret
    1300:	481c                	lw	a5,16(s0)
    1302:	00042223          	sw	zero,4(s0)
    1306:	557d                	li	a0,-1
    1308:	c01c                	sw	a5,0(s0)
    130a:	245e                	lhu	a5,12(s0)
    130c:	0207e793          	ori	a5,a5,32
    1310:	a45e                	sh	a5,12(s0)
    1312:	b7dd                	j	12f8 <__ssrefill_r+0x26>

00001314 <__ssvfiscanf_r>:
    1314:	d2010113          	addi	sp,sp,-736
    1318:	00000797          	auipc	a5,0x0
    131c:	f2478793          	addi	a5,a5,-220 # 123c <_sungetc_r>
    1320:	2d312623          	sw	s3,716(sp)
    1324:	28f12c23          	sw	a5,664(sp)
    1328:	01c10993          	addi	s3,sp,28
    132c:	00000797          	auipc	a5,0x0
    1330:	fa678793          	addi	a5,a5,-90 # 12d2 <__ssrefill_r>
    1334:	2c812c23          	sw	s0,728(sp)
    1338:	2c912a23          	sw	s1,724(sp)
    133c:	2d212823          	sw	s2,720(sp)
    1340:	2d412423          	sw	s4,712(sp)
    1344:	2d512223          	sw	s5,708(sp)
    1348:	2d612023          	sw	s6,704(sp)
    134c:	2b712e23          	sw	s7,700(sp)
    1350:	2b812c23          	sw	s8,696(sp)
    1354:	2c112e23          	sw	ra,732(sp)
    1358:	2b912a23          	sw	s9,692(sp)
    135c:	2ba12823          	sw	s10,688(sp)
    1360:	2bb12623          	sw	s11,684(sp)
    1364:	84aa                	mv	s1,a0
    1366:	842e                	mv	s0,a1
    1368:	12012423          	sw	zero,296(sp)
    136c:	12012623          	sw	zero,300(sp)
    1370:	13312823          	sw	s3,304(sp)
    1374:	28f12e23          	sw	a5,668(sp)
    1378:	cc36                	sw	a3,24(sp)
    137a:	02500a13          	li	s4,37
    137e:	02a00b13          	li	s6,42
    1382:	4ba5                	li	s7,9
    1384:	4929                	li	s2,10
    1386:	00001a97          	auipc	s5,0x1
    138a:	b66a8a93          	addi	s5,s5,-1178 # 1eec <__sf_fake_stdout+0x20>
    138e:	fffffc17          	auipc	s8,0xfffff
    1392:	c72c0c13          	addi	s8,s8,-910 # 0 <_sinit>
    1396:	221c                	lbu	a5,0(a2)
    1398:	c632                	sw	a2,12(sp)
    139a:	30078b63          	beqz	a5,16b0 <__ssvfiscanf_r+0x39c>
    139e:	1e9000ef          	jal	ra,1d86 <__locale_ctype_ptr>
    13a2:	4632                	lw	a2,12(sp)
    13a4:	00064d83          	lbu	s11,0(a2)
    13a8:	00160c93          	addi	s9,a2,1
    13ac:	956e                	add	a0,a0,s11
    13ae:	311c                	lbu	a5,1(a0)
    13b0:	8ba1                	andi	a5,a5,8
    13b2:	c3a1                	beqz	a5,13f2 <__ssvfiscanf_r+0xde>
    13b4:	405c                	lw	a5,4(s0)
    13b6:	02f05763          	blez	a5,13e4 <__ssvfiscanf_r+0xd0>
    13ba:	1cd000ef          	jal	ra,1d86 <__locale_ctype_ptr>
    13be:	401c                	lw	a5,0(s0)
    13c0:	2398                	lbu	a4,0(a5)
    13c2:	953a                	add	a0,a0,a4
    13c4:	3118                	lbu	a4,1(a0)
    13c6:	8b21                	andi	a4,a4,8
    13c8:	e319                	bnez	a4,13ce <__ssvfiscanf_r+0xba>
    13ca:	8666                	mv	a2,s9
    13cc:	b7e9                	j	1396 <__ssvfiscanf_r+0x82>
    13ce:	12c12703          	lw	a4,300(sp)
    13d2:	0785                	addi	a5,a5,1
    13d4:	c01c                	sw	a5,0(s0)
    13d6:	0705                	addi	a4,a4,1
    13d8:	12e12623          	sw	a4,300(sp)
    13dc:	4058                	lw	a4,4(s0)
    13de:	177d                	addi	a4,a4,-1
    13e0:	c058                	sw	a4,4(s0)
    13e2:	bfc9                	j	13b4 <__ssvfiscanf_r+0xa0>
    13e4:	29c12783          	lw	a5,668(sp)
    13e8:	85a2                	mv	a1,s0
    13ea:	8526                	mv	a0,s1
    13ec:	9782                	jalr	a5
    13ee:	d571                	beqz	a0,13ba <__ssvfiscanf_r+0xa6>
    13f0:	bfe9                	j	13ca <__ssvfiscanf_r+0xb6>
    13f2:	174d9763          	bne	s11,s4,1560 <__ssvfiscanf_r+0x24c>
    13f6:	12012223          	sw	zero,292(sp)
    13fa:	10012e23          	sw	zero,284(sp)
    13fe:	321c                	lbu	a5,1(a2)
    1400:	01679763          	bne	a5,s6,140e <__ssvfiscanf_r+0xfa>
    1404:	47c1                	li	a5,16
    1406:	10f12e23          	sw	a5,284(sp)
    140a:	00260c93          	addi	s9,a2,2
    140e:	000cc583          	lbu	a1,0(s9)
    1412:	8d66                	mv	s10,s9
    1414:	0c85                	addi	s9,s9,1
    1416:	fd058793          	addi	a5,a1,-48
    141a:	06fbf063          	bgeu	s7,a5,147a <__ssvfiscanf_r+0x166>
    141e:	460d                	li	a2,3
    1420:	8556                	mv	a0,s5
    1422:	1af000ef          	jal	ra,1dd0 <memchr>
    1426:	cd01                	beqz	a0,143e <__ssvfiscanf_r+0x12a>
    1428:	41550533          	sub	a0,a0,s5
    142c:	4785                	li	a5,1
    142e:	00a797b3          	sll	a5,a5,a0
    1432:	11c12503          	lw	a0,284(sp)
    1436:	8d66                	mv	s10,s9
    1438:	8d5d                	or	a0,a0,a5
    143a:	10a12e23          	sw	a0,284(sp)
    143e:	000d4783          	lbu	a5,0(s10)
    1442:	06700713          	li	a4,103
    1446:	001d0c93          	addi	s9,s10,1
    144a:	08f76663          	bltu	a4,a5,14d6 <__ssvfiscanf_r+0x1c2>
    144e:	06500713          	li	a4,101
    1452:	1ae7fb63          	bgeu	a5,a4,1608 <__ssvfiscanf_r+0x2f4>
    1456:	04700713          	li	a4,71
    145a:	02f76a63          	bltu	a4,a5,148e <__ssvfiscanf_r+0x17a>
    145e:	04500713          	li	a4,69
    1462:	1ae7f363          	bgeu	a5,a4,1608 <__ssvfiscanf_r+0x2f4>
    1466:	12078863          	beqz	a5,1596 <__ssvfiscanf_r+0x282>
    146a:	0f478b63          	beq	a5,s4,1560 <__ssvfiscanf_r+0x24c>
    146e:	478d                	li	a5,3
    1470:	12f12a23          	sw	a5,308(sp)
    1474:	13212023          	sw	s2,288(sp)
    1478:	a061                	j	1500 <__ssvfiscanf_r+0x1ec>
    147a:	12412783          	lw	a5,292(sp)
    147e:	02f907b3          	mul	a5,s2,a5
    1482:	fd078793          	addi	a5,a5,-48
    1486:	97ae                	add	a5,a5,a1
    1488:	12f12223          	sw	a5,292(sp)
    148c:	b749                	j	140e <__ssvfiscanf_r+0xfa>
    148e:	05b00713          	li	a4,91
    1492:	12e78063          	beq	a5,a4,15b2 <__ssvfiscanf_r+0x29e>
    1496:	02f76563          	bltu	a4,a5,14c0 <__ssvfiscanf_r+0x1ac>
    149a:	05800713          	li	a4,88
    149e:	fce798e3          	bne	a5,a4,146e <__ssvfiscanf_r+0x15a>
    14a2:	11c12703          	lw	a4,284(sp)
    14a6:	20076713          	ori	a4,a4,512
    14aa:	10e12e23          	sw	a4,284(sp)
    14ae:	4741                	li	a4,16
    14b0:	12e12023          	sw	a4,288(sp)
    14b4:	06e00713          	li	a4,110
    14b8:	00f737b3          	sltu	a5,a4,a5
    14bc:	078d                	addi	a5,a5,3
    14be:	a83d                	j	14fc <__ssvfiscanf_r+0x1e8>
    14c0:	06300713          	li	a4,99
    14c4:	10e78363          	beq	a5,a4,15ca <__ssvfiscanf_r+0x2b6>
    14c8:	06400713          	li	a4,100
    14cc:	fae791e3          	bne	a5,a4,146e <__ssvfiscanf_r+0x15a>
    14d0:	13212023          	sw	s2,288(sp)
    14d4:	b7c5                	j	14b4 <__ssvfiscanf_r+0x1a0>
    14d6:	07000713          	li	a4,112
    14da:	0ce78063          	beq	a5,a4,159a <__ssvfiscanf_r+0x286>
    14de:	06f76363          	bltu	a4,a5,1544 <__ssvfiscanf_r+0x230>
    14e2:	06e00713          	li	a4,110
    14e6:	0ee78b63          	beq	a5,a4,15dc <__ssvfiscanf_r+0x2c8>
    14ea:	0af76f63          	bltu	a4,a5,15a8 <__ssvfiscanf_r+0x294>
    14ee:	06900713          	li	a4,105
    14f2:	f6e79ee3          	bne	a5,a4,146e <__ssvfiscanf_r+0x15a>
    14f6:	12012023          	sw	zero,288(sp)
    14fa:	478d                	li	a5,3
    14fc:	12f12a23          	sw	a5,308(sp)
    1500:	405c                	lw	a5,4(s0)
    1502:	10f05563          	blez	a5,160c <__ssvfiscanf_r+0x2f8>
    1506:	11c12783          	lw	a5,284(sp)
    150a:	0407f793          	andi	a5,a5,64
    150e:	eb91                	bnez	a5,1522 <__ssvfiscanf_r+0x20e>
    1510:	077000ef          	jal	ra,1d86 <__locale_ctype_ptr>
    1514:	401c                	lw	a5,0(s0)
    1516:	2398                	lbu	a4,0(a5)
    1518:	953a                	add	a0,a0,a4
    151a:	3118                	lbu	a4,1(a0)
    151c:	8b21                	andi	a4,a4,8
    151e:	0e071f63          	bnez	a4,161c <__ssvfiscanf_r+0x308>
    1522:	13412783          	lw	a5,308(sp)
    1526:	4709                	li	a4,2
    1528:	10f74f63          	blt	a4,a5,1646 <__ssvfiscanf_r+0x332>
    152c:	0834                	addi	a3,sp,24
    152e:	8622                	mv	a2,s0
    1530:	0a6c                	addi	a1,sp,284
    1532:	8526                	mv	a0,s1
    1534:	2249                	jal	16b6 <_scanf_chars>
    1536:	4785                	li	a5,1
    1538:	16f50c63          	beq	a0,a5,16b0 <__ssvfiscanf_r+0x39c>
    153c:	4789                	li	a5,2
    153e:	e8f516e3          	bne	a0,a5,13ca <__ssvfiscanf_r+0xb6>
    1542:	a0b9                	j	1590 <__ssvfiscanf_r+0x27c>
    1544:	07500713          	li	a4,117
    1548:	f8e784e3          	beq	a5,a4,14d0 <__ssvfiscanf_r+0x1bc>
    154c:	07800713          	li	a4,120
    1550:	f4e789e3          	beq	a5,a4,14a2 <__ssvfiscanf_r+0x18e>
    1554:	07300713          	li	a4,115
    1558:	f0e79be3          	bne	a5,a4,146e <__ssvfiscanf_r+0x15a>
    155c:	4789                	li	a5,2
    155e:	bf79                	j	14fc <__ssvfiscanf_r+0x1e8>
    1560:	405c                	lw	a5,4(s0)
    1562:	02f05163          	blez	a5,1584 <__ssvfiscanf_r+0x270>
    1566:	401c                	lw	a5,0(s0)
    1568:	2398                	lbu	a4,0(a5)
    156a:	15b71363          	bne	a4,s11,16b0 <__ssvfiscanf_r+0x39c>
    156e:	0785                	addi	a5,a5,1
    1570:	4058                	lw	a4,4(s0)
    1572:	c01c                	sw	a5,0(s0)
    1574:	12c12783          	lw	a5,300(sp)
    1578:	177d                	addi	a4,a4,-1
    157a:	c058                	sw	a4,4(s0)
    157c:	0785                	addi	a5,a5,1
    157e:	12f12623          	sw	a5,300(sp)
    1582:	b5a1                	j	13ca <__ssvfiscanf_r+0xb6>
    1584:	29c12783          	lw	a5,668(sp)
    1588:	85a2                	mv	a1,s0
    158a:	8526                	mv	a0,s1
    158c:	9782                	jalr	a5
    158e:	dd61                	beqz	a0,1566 <__ssvfiscanf_r+0x252>
    1590:	12812503          	lw	a0,296(sp)
    1594:	ed69                	bnez	a0,166e <__ssvfiscanf_r+0x35a>
    1596:	557d                	li	a0,-1
    1598:	a8f9                	j	1676 <__ssvfiscanf_r+0x362>
    159a:	11c12703          	lw	a4,284(sp)
    159e:	02076713          	ori	a4,a4,32
    15a2:	10e12e23          	sw	a4,284(sp)
    15a6:	bdf5                	j	14a2 <__ssvfiscanf_r+0x18e>
    15a8:	47a1                	li	a5,8
    15aa:	12f12023          	sw	a5,288(sp)
    15ae:	4791                	li	a5,4
    15b0:	b7b1                	j	14fc <__ssvfiscanf_r+0x1e8>
    15b2:	85e6                	mv	a1,s9
    15b4:	854e                	mv	a0,s3
    15b6:	291d                	jal	19ec <__sccl>
    15b8:	11c12783          	lw	a5,284(sp)
    15bc:	8caa                	mv	s9,a0
    15be:	0407e793          	ori	a5,a5,64
    15c2:	10f12e23          	sw	a5,284(sp)
    15c6:	4785                	li	a5,1
    15c8:	bf15                	j	14fc <__ssvfiscanf_r+0x1e8>
    15ca:	11c12783          	lw	a5,284(sp)
    15ce:	12012a23          	sw	zero,308(sp)
    15d2:	0407e793          	ori	a5,a5,64
    15d6:	10f12e23          	sw	a5,284(sp)
    15da:	b71d                	j	1500 <__ssvfiscanf_r+0x1ec>
    15dc:	11c12783          	lw	a5,284(sp)
    15e0:	0107f713          	andi	a4,a5,16
    15e4:	de0713e3          	bnez	a4,13ca <__ssvfiscanf_r+0xb6>
    15e8:	4762                	lw	a4,24(sp)
    15ea:	0017f593          	andi	a1,a5,1
    15ee:	12c12683          	lw	a3,300(sp)
    15f2:	00470613          	addi	a2,a4,4
    15f6:	c589                	beqz	a1,1600 <__ssvfiscanf_r+0x2ec>
    15f8:	cc32                	sw	a2,24(sp)
    15fa:	431c                	lw	a5,0(a4)
    15fc:	a396                	sh	a3,0(a5)
    15fe:	b3f1                	j	13ca <__ssvfiscanf_r+0xb6>
    1600:	cc32                	sw	a2,24(sp)
    1602:	431c                	lw	a5,0(a4)
    1604:	c394                	sw	a3,0(a5)
    1606:	b3d1                	j	13ca <__ssvfiscanf_r+0xb6>
    1608:	4795                	li	a5,5
    160a:	bdcd                	j	14fc <__ssvfiscanf_r+0x1e8>
    160c:	29c12783          	lw	a5,668(sp)
    1610:	85a2                	mv	a1,s0
    1612:	8526                	mv	a0,s1
    1614:	9782                	jalr	a5
    1616:	ee0508e3          	beqz	a0,1506 <__ssvfiscanf_r+0x1f2>
    161a:	bf9d                	j	1590 <__ssvfiscanf_r+0x27c>
    161c:	12c12703          	lw	a4,300(sp)
    1620:	0705                	addi	a4,a4,1
    1622:	12e12623          	sw	a4,300(sp)
    1626:	4058                	lw	a4,4(s0)
    1628:	177d                	addi	a4,a4,-1
    162a:	c058                	sw	a4,4(s0)
    162c:	00e05563          	blez	a4,1636 <__ssvfiscanf_r+0x322>
    1630:	0785                	addi	a5,a5,1
    1632:	c01c                	sw	a5,0(s0)
    1634:	bdf1                	j	1510 <__ssvfiscanf_r+0x1fc>
    1636:	29c12783          	lw	a5,668(sp)
    163a:	85a2                	mv	a1,s0
    163c:	8526                	mv	a0,s1
    163e:	9782                	jalr	a5
    1640:	ec0508e3          	beqz	a0,1510 <__ssvfiscanf_r+0x1fc>
    1644:	b7b1                	j	1590 <__ssvfiscanf_r+0x27c>
    1646:	4711                	li	a4,4
    1648:	00f74863          	blt	a4,a5,1658 <__ssvfiscanf_r+0x344>
    164c:	0834                	addi	a3,sp,24
    164e:	8622                	mv	a2,s0
    1650:	0a6c                	addi	a1,sp,284
    1652:	8526                	mv	a0,s1
    1654:	2289                	jal	1796 <_scanf_i>
    1656:	b5c5                	j	1536 <__ssvfiscanf_r+0x222>
    1658:	d60c09e3          	beqz	s8,13ca <__ssvfiscanf_r+0xb6>
    165c:	0834                	addi	a3,sp,24
    165e:	8622                	mv	a2,s0
    1660:	0a6c                	addi	a1,sp,284
    1662:	8526                	mv	a0,s1
    1664:	00000097          	auipc	ra,0x0
    1668:	000000e7          	jalr	zero # 0 <_sinit>
    166c:	b5e9                	j	1536 <__ssvfiscanf_r+0x222>
    166e:	245e                	lhu	a5,12(s0)
    1670:	0407f793          	andi	a5,a5,64
    1674:	f38d                	bnez	a5,1596 <__ssvfiscanf_r+0x282>
    1676:	2dc12083          	lw	ra,732(sp)
    167a:	2d812403          	lw	s0,728(sp)
    167e:	2d412483          	lw	s1,724(sp)
    1682:	2d012903          	lw	s2,720(sp)
    1686:	2cc12983          	lw	s3,716(sp)
    168a:	2c812a03          	lw	s4,712(sp)
    168e:	2c412a83          	lw	s5,708(sp)
    1692:	2c012b03          	lw	s6,704(sp)
    1696:	2bc12b83          	lw	s7,700(sp)
    169a:	2b812c03          	lw	s8,696(sp)
    169e:	2b412c83          	lw	s9,692(sp)
    16a2:	2b012d03          	lw	s10,688(sp)
    16a6:	2ac12d83          	lw	s11,684(sp)
    16aa:	2e010113          	addi	sp,sp,736
    16ae:	8082                	ret
    16b0:	12812503          	lw	a0,296(sp)
    16b4:	b7c9                	j	1676 <__ssvfiscanf_r+0x362>

000016b6 <_scanf_chars>:
    16b6:	459c                	lw	a5,8(a1)
    16b8:	1101                	addi	sp,sp,-32
    16ba:	cc22                	sw	s0,24(sp)
    16bc:	ca26                	sw	s1,20(sp)
    16be:	c452                	sw	s4,8(sp)
    16c0:	ce06                	sw	ra,28(sp)
    16c2:	c84a                	sw	s2,16(sp)
    16c4:	c64e                	sw	s3,12(sp)
    16c6:	c256                	sw	s5,4(sp)
    16c8:	c05a                	sw	s6,0(sp)
    16ca:	8a2a                	mv	s4,a0
    16cc:	842e                	mv	s0,a1
    16ce:	84b2                	mv	s1,a2
    16d0:	e791                	bnez	a5,16dc <_scanf_chars+0x26>
    16d2:	4d98                	lw	a4,24(a1)
    16d4:	4785                	li	a5,1
    16d6:	c311                	beqz	a4,16da <_scanf_chars+0x24>
    16d8:	57fd                	li	a5,-1
    16da:	c41c                	sw	a5,8(s0)
    16dc:	401c                	lw	a5,0(s0)
    16de:	8bc1                	andi	a5,a5,16
    16e0:	e799                	bnez	a5,16ee <_scanf_chars+0x38>
    16e2:	429c                	lw	a5,0(a3)
    16e4:	00478713          	addi	a4,a5,4
    16e8:	c298                	sw	a4,0(a3)
    16ea:	0007a983          	lw	s3,0(a5)
    16ee:	4901                	li	s2,0
    16f0:	4a85                	li	s5,1
    16f2:	4b09                	li	s6,2
    16f4:	4c08                	lw	a0,24(s0)
    16f6:	c50d                	beqz	a0,1720 <_scanf_chars+0x6a>
    16f8:	07551f63          	bne	a0,s5,1776 <_scanf_chars+0xc0>
    16fc:	409c                	lw	a5,0(s1)
    16fe:	2398                	lbu	a4,0(a5)
    1700:	485c                	lw	a5,20(s0)
    1702:	97ba                	add	a5,a5,a4
    1704:	239c                	lbu	a5,0(a5)
    1706:	ef89                	bnez	a5,1720 <_scanf_chars+0x6a>
    1708:	04091763          	bnez	s2,1756 <_scanf_chars+0xa0>
    170c:	40f2                	lw	ra,28(sp)
    170e:	4462                	lw	s0,24(sp)
    1710:	44d2                	lw	s1,20(sp)
    1712:	4942                	lw	s2,16(sp)
    1714:	49b2                	lw	s3,12(sp)
    1716:	4a22                	lw	s4,8(sp)
    1718:	4a92                	lw	s5,4(sp)
    171a:	4b02                	lw	s6,0(sp)
    171c:	6105                	addi	sp,sp,32
    171e:	8082                	ret
    1720:	401c                	lw	a5,0(s0)
    1722:	0905                	addi	s2,s2,1
    1724:	8bc1                	andi	a5,a5,16
    1726:	e791                	bnez	a5,1732 <_scanf_chars+0x7c>
    1728:	409c                	lw	a5,0(s1)
    172a:	0985                	addi	s3,s3,1
    172c:	239c                	lbu	a5,0(a5)
    172e:	fef98fa3          	sb	a5,-1(s3)
    1732:	4098                	lw	a4,0(s1)
    1734:	40dc                	lw	a5,4(s1)
    1736:	0705                	addi	a4,a4,1
    1738:	c098                	sw	a4,0(s1)
    173a:	4418                	lw	a4,8(s0)
    173c:	17fd                	addi	a5,a5,-1
    173e:	c0dc                	sw	a5,4(s1)
    1740:	177d                	addi	a4,a4,-1
    1742:	c418                	sw	a4,8(s0)
    1744:	cb09                	beqz	a4,1756 <_scanf_chars+0xa0>
    1746:	faf047e3          	bgtz	a5,16f4 <_scanf_chars+0x3e>
    174a:	18042783          	lw	a5,384(s0)
    174e:	85a6                	mv	a1,s1
    1750:	8552                	mv	a0,s4
    1752:	9782                	jalr	a5
    1754:	d145                	beqz	a0,16f4 <_scanf_chars+0x3e>
    1756:	401c                	lw	a5,0(s0)
    1758:	8bc1                	andi	a5,a5,16
    175a:	eb81                	bnez	a5,176a <_scanf_chars+0xb4>
    175c:	445c                	lw	a5,12(s0)
    175e:	0785                	addi	a5,a5,1
    1760:	c45c                	sw	a5,12(s0)
    1762:	4c1c                	lw	a5,24(s0)
    1764:	c399                	beqz	a5,176a <_scanf_chars+0xb4>
    1766:	00098023          	sb	zero,0(s3)
    176a:	481c                	lw	a5,16(s0)
    176c:	4501                	li	a0,0
    176e:	993e                	add	s2,s2,a5
    1770:	01242823          	sw	s2,16(s0)
    1774:	bf61                	j	170c <_scanf_chars+0x56>
    1776:	01651963          	bne	a0,s6,1788 <_scanf_chars+0xd2>
    177a:	2531                	jal	1d86 <__locale_ctype_ptr>
    177c:	409c                	lw	a5,0(s1)
    177e:	239c                	lbu	a5,0(a5)
    1780:	953e                	add	a0,a0,a5
    1782:	311c                	lbu	a5,1(a0)
    1784:	8ba1                	andi	a5,a5,8
    1786:	dfc9                	beqz	a5,1720 <_scanf_chars+0x6a>
    1788:	fc0917e3          	bnez	s2,1756 <_scanf_chars+0xa0>
    178c:	4c08                	lw	a0,24(s0)
    178e:	4785                	li	a5,1
    1790:	fcf513e3          	bne	a0,a5,1756 <_scanf_chars+0xa0>
    1794:	bfa5                	j	170c <_scanf_chars+0x56>

00001796 <_scanf_i>:
    1796:	715d                	addi	sp,sp,-80
    1798:	00000797          	auipc	a5,0x0
    179c:	76c78793          	addi	a5,a5,1900 # 1f04 <__sf_fake_stdout+0x38>
    17a0:	c0ca                	sw	s2,64(sp)
    17a2:	c23e                	sw	a5,4(sp)
    17a4:	8936                	mv	s2,a3
    17a6:	00000797          	auipc	a5,0x0
    17aa:	76278793          	addi	a5,a5,1890 # 1f08 <__sf_fake_stdout+0x3c>
    17ae:	4d94                	lw	a3,24(a1)
    17b0:	c43e                	sw	a5,8(sp)
    17b2:	00000797          	auipc	a5,0x0
    17b6:	75a78793          	addi	a5,a5,1882 # 1f0c <__sf_fake_stdout+0x40>
    17ba:	c4a2                	sw	s0,72(sp)
    17bc:	de4e                	sw	s3,60(sp)
    17be:	d85a                	sw	s6,48(sp)
    17c0:	ce6e                	sw	s11,28(sp)
    17c2:	c63e                	sw	a5,12(sp)
    17c4:	c686                	sw	ra,76(sp)
    17c6:	c2a6                	sw	s1,68(sp)
    17c8:	dc52                	sw	s4,56(sp)
    17ca:	da56                	sw	s5,52(sp)
    17cc:	d65e                	sw	s7,44(sp)
    17ce:	d462                	sw	s8,40(sp)
    17d0:	d266                	sw	s9,36(sp)
    17d2:	d06a                	sw	s10,32(sp)
    17d4:	478d                	li	a5,3
    17d6:	89aa                	mv	s3,a0
    17d8:	842e                	mv	s0,a1
    17da:	8db2                	mv	s11,a2
    17dc:	00000b17          	auipc	s6,0x0
    17e0:	50cb0b13          	addi	s6,s6,1292 # 1ce8 <_strtoul_r>
    17e4:	00f69663          	bne	a3,a5,17f0 <_scanf_i+0x5a>
    17e8:	00000b17          	auipc	s6,0x0
    17ec:	3beb0b13          	addi	s6,s6,958 # 1ba6 <_strtol_r>
    17f0:	441c                	lw	a5,8(s0)
    17f2:	15c00693          	li	a3,348
    17f6:	4a01                	li	s4,0
    17f8:	fff78613          	addi	a2,a5,-1
    17fc:	00c6f763          	bgeu	a3,a2,180a <_scanf_i+0x74>
    1800:	ea378a13          	addi	s4,a5,-349
    1804:	15d00793          	li	a5,349
    1808:	c41c                	sw	a5,8(s0)
    180a:	401c                	lw	a5,0(s0)
    180c:	6685                	lui	a3,0x1
    180e:	d0068693          	addi	a3,a3,-768 # d00 <_sbrk+0x22>
    1812:	01c40493          	addi	s1,s0,28
    1816:	8fd5                	or	a5,a5,a3
    1818:	c01c                	sw	a5,0(s0)
    181a:	00410c13          	addi	s8,sp,4
    181e:	8aa6                	mv	s5,s1
    1820:	4b81                	li	s7,0
    1822:	20000c93          	li	s9,512
    1826:	000da783          	lw	a5,0(s11)
    182a:	000c2503          	lw	a0,0(s8)
    182e:	4609                	li	a2,2
    1830:	238c                	lbu	a1,0(a5)
    1832:	2b79                	jal	1dd0 <memchr>
    1834:	cd39                	beqz	a0,1892 <_scanf_i+0xfc>
    1836:	4785                	li	a5,1
    1838:	0cfb9e63          	bne	s7,a5,1914 <_scanf_i+0x17e>
    183c:	405c                	lw	a5,4(s0)
    183e:	e799                	bnez	a5,184c <_scanf_i+0xb6>
    1840:	401c                	lw	a5,0(s0)
    1842:	4721                	li	a4,8
    1844:	c058                	sw	a4,4(s0)
    1846:	2007e793          	ori	a5,a5,512
    184a:	c01c                	sw	a5,0(s0)
    184c:	401c                	lw	a5,0(s0)
    184e:	aff7f793          	andi	a5,a5,-1281
    1852:	c01c                	sw	a5,0(s0)
    1854:	441c                	lw	a5,8(s0)
    1856:	fff78613          	addi	a2,a5,-1
    185a:	c410                	sw	a2,8(s0)
    185c:	cb9d                	beqz	a5,1892 <_scanf_i+0xfc>
    185e:	000da603          	lw	a2,0(s11)
    1862:	001a8d13          	addi	s10,s5,1
    1866:	00160793          	addi	a5,a2,1
    186a:	00fda023          	sw	a5,0(s11)
    186e:	2210                	lbu	a2,0(a2)
    1870:	00ca8023          	sb	a2,0(s5)
    1874:	004da603          	lw	a2,4(s11)
    1878:	8aea                	mv	s5,s10
    187a:	167d                	addi	a2,a2,-1
    187c:	00cda223          	sw	a2,4(s11)
    1880:	00c04963          	bgtz	a2,1892 <_scanf_i+0xfc>
    1884:	18042603          	lw	a2,384(s0)
    1888:	85ee                	mv	a1,s11
    188a:	854e                	mv	a0,s3
    188c:	9602                	jalr	a2
    188e:	10051863          	bnez	a0,199e <_scanf_i+0x208>
    1892:	0b85                	addi	s7,s7,1
    1894:	478d                	li	a5,3
    1896:	0c11                	addi	s8,s8,4
    1898:	f8fb97e3          	bne	s7,a5,1826 <_scanf_i+0x90>
    189c:	405c                	lw	a5,4(s0)
    189e:	e399                	bnez	a5,18a4 <_scanf_i+0x10e>
    18a0:	47a9                	li	a5,10
    18a2:	c05c                	sw	a5,4(s0)
    18a4:	405c                	lw	a5,4(s0)
    18a6:	4848                	lw	a0,20(s0)
    18a8:	00000597          	auipc	a1,0x0
    18ac:	65858593          	addi	a1,a1,1624 # 1f00 <__sf_fake_stdout+0x34>
    18b0:	8d9d                	sub	a1,a1,a5
    18b2:	7bfd                	lui	s7,0xfffff
    18b4:	6c05                	lui	s8,0x1
    18b6:	8d56                	mv	s10,s5
    18b8:	2a15                	jal	19ec <__sccl>
    18ba:	4a81                	li	s5,0
    18bc:	03000c93          	li	s9,48
    18c0:	6ffb8b93          	addi	s7,s7,1791 # fffff6ff <_eusrstack+0xdfff76ff>
    18c4:	800c0c13          	addi	s8,s8,-2048 # 800 <__stack_size>
    18c8:	4414                	lw	a3,8(s0)
    18ca:	4010                	lw	a2,0(s0)
    18cc:	cebd                	beqz	a3,194a <_scanf_i+0x1b4>
    18ce:	000da503          	lw	a0,0(s11)
    18d2:	484c                	lw	a1,20(s0)
    18d4:	00054803          	lbu	a6,0(a0)
    18d8:	95c2                	add	a1,a1,a6
    18da:	218c                	lbu	a1,0(a1)
    18dc:	c5bd                	beqz	a1,194a <_scanf_i+0x1b4>
    18de:	05981863          	bne	a6,s9,192e <_scanf_i+0x198>
    18e2:	018675b3          	and	a1,a2,s8
    18e6:	c5a1                	beqz	a1,192e <_scanf_i+0x198>
    18e8:	0a85                	addi	s5,s5,1
    18ea:	000a0563          	beqz	s4,18f4 <_scanf_i+0x15e>
    18ee:	0685                	addi	a3,a3,1
    18f0:	1a7d                	addi	s4,s4,-1
    18f2:	c414                	sw	a3,8(s0)
    18f4:	004da683          	lw	a3,4(s11)
    18f8:	16fd                	addi	a3,a3,-1
    18fa:	00dda223          	sw	a3,4(s11)
    18fe:	04d05063          	blez	a3,193e <_scanf_i+0x1a8>
    1902:	000da683          	lw	a3,0(s11)
    1906:	0685                	addi	a3,a3,1
    1908:	00dda023          	sw	a3,0(s11)
    190c:	4414                	lw	a3,8(s0)
    190e:	16fd                	addi	a3,a3,-1
    1910:	c414                	sw	a3,8(s0)
    1912:	bf5d                	j	18c8 <_scanf_i+0x132>
    1914:	4789                	li	a5,2
    1916:	f2fb9fe3          	bne	s7,a5,1854 <_scanf_i+0xbe>
    191a:	401c                	lw	a5,0(s0)
    191c:	6007f613          	andi	a2,a5,1536
    1920:	f79619e3          	bne	a2,s9,1892 <_scanf_i+0xfc>
    1924:	4741                	li	a4,16
    1926:	c058                	sw	a4,4(s0)
    1928:	1007e793          	ori	a5,a5,256
    192c:	b71d                	j	1852 <_scanf_i+0xbc>
    192e:	01767633          	and	a2,a2,s7
    1932:	c010                	sw	a2,0(s0)
    1934:	2114                	lbu	a3,0(a0)
    1936:	0d05                	addi	s10,s10,1
    1938:	fedd0fa3          	sb	a3,-1(s10)
    193c:	bf65                	j	18f4 <_scanf_i+0x15e>
    193e:	18042683          	lw	a3,384(s0)
    1942:	85ee                	mv	a1,s11
    1944:	854e                	mv	a0,s3
    1946:	9682                	jalr	a3
    1948:	d171                	beqz	a0,190c <_scanf_i+0x176>
    194a:	4014                	lw	a3,0(s0)
    194c:	1006f693          	andi	a3,a3,256
    1950:	c285                	beqz	a3,1970 <_scanf_i+0x1da>
    1952:	01a4fc63          	bgeu	s1,s10,196a <_scanf_i+0x1d4>
    1956:	fffd4583          	lbu	a1,-1(s10)
    195a:	17c42683          	lw	a3,380(s0)
    195e:	fffd0a13          	addi	s4,s10,-1
    1962:	866e                	mv	a2,s11
    1964:	854e                	mv	a0,s3
    1966:	9682                	jalr	a3
    1968:	8d52                	mv	s10,s4
    196a:	4505                	li	a0,1
    196c:	049d0e63          	beq	s10,s1,19c8 <_scanf_i+0x232>
    1970:	4018                	lw	a4,0(s0)
    1972:	8b41                	andi	a4,a4,16
    1974:	e339                	bnez	a4,19ba <_scanf_i+0x224>
    1976:	000d0023          	sb	zero,0(s10)
    197a:	4054                	lw	a3,4(s0)
    197c:	4601                	li	a2,0
    197e:	85a6                	mv	a1,s1
    1980:	854e                	mv	a0,s3
    1982:	9b02                	jalr	s6
    1984:	4014                	lw	a3,0(s0)
    1986:	00092703          	lw	a4,0(s2)
    198a:	0206f613          	andi	a2,a3,32
    198e:	ca11                	beqz	a2,19a2 <_scanf_i+0x20c>
    1990:	00470693          	addi	a3,a4,4
    1994:	00d92023          	sw	a3,0(s2)
    1998:	4318                	lw	a4,0(a4)
    199a:	c308                	sw	a0,0(a4)
    199c:	a821                	j	19b4 <_scanf_i+0x21e>
    199e:	4a81                	li	s5,0
    19a0:	b76d                	j	194a <_scanf_i+0x1b4>
    19a2:	0016f593          	andi	a1,a3,1
    19a6:	00470613          	addi	a2,a4,4
    19aa:	cd95                	beqz	a1,19e6 <_scanf_i+0x250>
    19ac:	00c92023          	sw	a2,0(s2)
    19b0:	4318                	lw	a4,0(a4)
    19b2:	a30a                	sh	a0,0(a4)
    19b4:	4458                	lw	a4,12(s0)
    19b6:	0705                	addi	a4,a4,1
    19b8:	c458                	sw	a4,12(s0)
    19ba:	481c                	lw	a5,16(s0)
    19bc:	409d04b3          	sub	s1,s10,s1
    19c0:	94d6                	add	s1,s1,s5
    19c2:	94be                	add	s1,s1,a5
    19c4:	c804                	sw	s1,16(s0)
    19c6:	4501                	li	a0,0
    19c8:	40b6                	lw	ra,76(sp)
    19ca:	4426                	lw	s0,72(sp)
    19cc:	4496                	lw	s1,68(sp)
    19ce:	4906                	lw	s2,64(sp)
    19d0:	59f2                	lw	s3,60(sp)
    19d2:	5a62                	lw	s4,56(sp)
    19d4:	5ad2                	lw	s5,52(sp)
    19d6:	5b42                	lw	s6,48(sp)
    19d8:	5bb2                	lw	s7,44(sp)
    19da:	5c22                	lw	s8,40(sp)
    19dc:	5c92                	lw	s9,36(sp)
    19de:	5d02                	lw	s10,32(sp)
    19e0:	4df2                	lw	s11,28(sp)
    19e2:	6161                	addi	sp,sp,80
    19e4:	8082                	ret
    19e6:	00c92023          	sw	a2,0(s2)
    19ea:	b77d                	j	1998 <_scanf_i+0x202>

000019ec <__sccl>:
    19ec:	219c                	lbu	a5,0(a1)
    19ee:	05e00713          	li	a4,94
    19f2:	04e78663          	beq	a5,a4,1a3e <__sccl+0x52>
    19f6:	00158713          	addi	a4,a1,1
    19fa:	4681                	li	a3,0
    19fc:	4601                	li	a2,0
    19fe:	10000593          	li	a1,256
    1a02:	00c50833          	add	a6,a0,a2
    1a06:	00d80023          	sb	a3,0(a6)
    1a0a:	0605                	addi	a2,a2,1
    1a0c:	feb61be3          	bne	a2,a1,1a02 <__sccl+0x16>
    1a10:	fff70613          	addi	a2,a4,-1
    1a14:	c39d                	beqz	a5,1a3a <__sccl+0x4e>
    1a16:	0016c693          	xori	a3,a3,1
    1a1a:	02d00893          	li	a7,45
    1a1e:	05d00813          	li	a6,93
    1a22:	00f50633          	add	a2,a0,a5
    1a26:	a214                	sb	a3,0(a2)
    1a28:	230c                	lbu	a1,0(a4)
    1a2a:	00170613          	addi	a2,a4,1
    1a2e:	03158363          	beq	a1,a7,1a54 <__sccl+0x68>
    1a32:	01058463          	beq	a1,a6,1a3a <__sccl+0x4e>
    1a36:	e989                	bnez	a1,1a48 <__sccl+0x5c>
    1a38:	863a                	mv	a2,a4
    1a3a:	8532                	mv	a0,a2
    1a3c:	8082                	ret
    1a3e:	00258713          	addi	a4,a1,2
    1a42:	319c                	lbu	a5,1(a1)
    1a44:	4685                	li	a3,1
    1a46:	bf5d                	j	19fc <__sccl+0x10>
    1a48:	87ae                	mv	a5,a1
    1a4a:	8732                	mv	a4,a2
    1a4c:	bfd9                	j	1a22 <__sccl+0x36>
    1a4e:	02d00793          	li	a5,45
    1a52:	bfe5                	j	1a4a <__sccl+0x5e>
    1a54:	330c                	lbu	a1,1(a4)
    1a56:	ff058ce3          	beq	a1,a6,1a4e <__sccl+0x62>
    1a5a:	fef5cae3          	blt	a1,a5,1a4e <__sccl+0x62>
    1a5e:	0709                	addi	a4,a4,2
    1a60:	0785                	addi	a5,a5,1
    1a62:	00f50633          	add	a2,a0,a5
    1a66:	a214                	sb	a3,0(a2)
    1a68:	feb7cce3          	blt	a5,a1,1a60 <__sccl+0x74>
    1a6c:	bf75                	j	1a28 <__sccl+0x3c>

00001a6e <_strtol_l.isra.0>:
    1a6e:	7179                	addi	sp,sp,-48
    1a70:	d04a                	sw	s2,32(sp)
    1a72:	ce4e                	sw	s3,28(sp)
    1a74:	d606                	sw	ra,44(sp)
    1a76:	d422                	sw	s0,40(sp)
    1a78:	d226                	sw	s1,36(sp)
    1a7a:	89aa                	mv	s3,a0
    1a7c:	892e                	mv	s2,a1
    1a7e:	00190493          	addi	s1,s2,1
    1a82:	fff4c403          	lbu	s0,-1(s1)
    1a86:	853a                	mv	a0,a4
    1a88:	c636                	sw	a3,12(sp)
    1a8a:	c432                	sw	a2,8(sp)
    1a8c:	c22e                	sw	a1,4(sp)
    1a8e:	c03a                	sw	a4,0(sp)
    1a90:	2cc5                	jal	1d80 <__locale_ctype_ptr_l>
    1a92:	9522                	add	a0,a0,s0
    1a94:	311c                	lbu	a5,1(a0)
    1a96:	4702                	lw	a4,0(sp)
    1a98:	4592                	lw	a1,4(sp)
    1a9a:	8ba1                	andi	a5,a5,8
    1a9c:	4622                	lw	a2,8(sp)
    1a9e:	46b2                	lw	a3,12(sp)
    1aa0:	e3d1                	bnez	a5,1b24 <_strtol_l.isra.0+0xb6>
    1aa2:	02d00793          	li	a5,45
    1aa6:	08f41163          	bne	s0,a5,1b28 <_strtol_l.isra.0+0xba>
    1aaa:	209c                	lbu	a5,0(s1)
    1aac:	4305                	li	t1,1
    1aae:	00290493          	addi	s1,s2,2
    1ab2:	c6e5                	beqz	a3,1b9a <_strtol_l.isra.0+0x12c>
    1ab4:	4741                	li	a4,16
    1ab6:	02e69063          	bne	a3,a4,1ad6 <_strtol_l.isra.0+0x68>
    1aba:	03000713          	li	a4,48
    1abe:	00e79c63          	bne	a5,a4,1ad6 <_strtol_l.isra.0+0x68>
    1ac2:	209c                	lbu	a5,0(s1)
    1ac4:	05800713          	li	a4,88
    1ac8:	0df7f793          	andi	a5,a5,223
    1acc:	0ce79263          	bne	a5,a4,1b90 <_strtol_l.isra.0+0x122>
    1ad0:	309c                	lbu	a5,1(s1)
    1ad2:	46c1                	li	a3,16
    1ad4:	0489                	addi	s1,s1,2
    1ad6:	800008b7          	lui	a7,0x80000
    1ada:	00031463          	bnez	t1,1ae2 <_strtol_l.isra.0+0x74>
    1ade:	fff8c893          	not	a7,a7
    1ae2:	02d8ff33          	remu	t5,a7,a3
    1ae6:	4801                	li	a6,0
    1ae8:	4501                	li	a0,0
    1aea:	4fa5                	li	t6,9
    1aec:	4ee5                	li	t4,25
    1aee:	52fd                	li	t0,-1
    1af0:	02d8de33          	divu	t3,a7,a3
    1af4:	fd078713          	addi	a4,a5,-48
    1af8:	04efe363          	bltu	t6,a4,1b3e <_strtol_l.isra.0+0xd0>
    1afc:	87ba                	mv	a5,a4
    1afe:	04d7de63          	bge	a5,a3,1b5a <_strtol_l.isra.0+0xec>
    1b02:	00580d63          	beq	a6,t0,1b1c <_strtol_l.isra.0+0xae>
    1b06:	587d                	li	a6,-1
    1b08:	00ae6a63          	bltu	t3,a0,1b1c <_strtol_l.isra.0+0xae>
    1b0c:	00ae1463          	bne	t3,a0,1b14 <_strtol_l.isra.0+0xa6>
    1b10:	00ff4663          	blt	t5,a5,1b1c <_strtol_l.isra.0+0xae>
    1b14:	4805                	li	a6,1
    1b16:	02a68533          	mul	a0,a3,a0
    1b1a:	953e                	add	a0,a0,a5
    1b1c:	0485                	addi	s1,s1,1
    1b1e:	fff4c783          	lbu	a5,-1(s1)
    1b22:	bfc9                	j	1af4 <_strtol_l.isra.0+0x86>
    1b24:	8926                	mv	s2,s1
    1b26:	bfa1                	j	1a7e <_strtol_l.isra.0+0x10>
    1b28:	02b00793          	li	a5,43
    1b2c:	00f40563          	beq	s0,a5,1b36 <_strtol_l.isra.0+0xc8>
    1b30:	87a2                	mv	a5,s0
    1b32:	4301                	li	t1,0
    1b34:	bfbd                	j	1ab2 <_strtol_l.isra.0+0x44>
    1b36:	209c                	lbu	a5,0(s1)
    1b38:	00290493          	addi	s1,s2,2
    1b3c:	bfdd                	j	1b32 <_strtol_l.isra.0+0xc4>
    1b3e:	fbf78713          	addi	a4,a5,-65
    1b42:	00eee563          	bltu	t4,a4,1b4c <_strtol_l.isra.0+0xde>
    1b46:	fc978793          	addi	a5,a5,-55
    1b4a:	bf55                	j	1afe <_strtol_l.isra.0+0x90>
    1b4c:	f9f78713          	addi	a4,a5,-97
    1b50:	00eee563          	bltu	t4,a4,1b5a <_strtol_l.isra.0+0xec>
    1b54:	fa978793          	addi	a5,a5,-87
    1b58:	b75d                	j	1afe <_strtol_l.isra.0+0x90>
    1b5a:	57fd                	li	a5,-1
    1b5c:	00f81f63          	bne	a6,a5,1b7a <_strtol_l.isra.0+0x10c>
    1b60:	02200793          	li	a5,34
    1b64:	00f9a023          	sw	a5,0(s3)
    1b68:	8546                	mv	a0,a7
    1b6a:	ee19                	bnez	a2,1b88 <_strtol_l.isra.0+0x11a>
    1b6c:	50b2                	lw	ra,44(sp)
    1b6e:	5422                	lw	s0,40(sp)
    1b70:	5492                	lw	s1,36(sp)
    1b72:	5902                	lw	s2,32(sp)
    1b74:	49f2                	lw	s3,28(sp)
    1b76:	6145                	addi	sp,sp,48
    1b78:	8082                	ret
    1b7a:	00030463          	beqz	t1,1b82 <_strtol_l.isra.0+0x114>
    1b7e:	40a00533          	neg	a0,a0
    1b82:	d66d                	beqz	a2,1b6c <_strtol_l.isra.0+0xfe>
    1b84:	00080463          	beqz	a6,1b8c <_strtol_l.isra.0+0x11e>
    1b88:	fff48593          	addi	a1,s1,-1
    1b8c:	c20c                	sw	a1,0(a2)
    1b8e:	bff9                	j	1b6c <_strtol_l.isra.0+0xfe>
    1b90:	03000793          	li	a5,48
    1b94:	f2a9                	bnez	a3,1ad6 <_strtol_l.isra.0+0x68>
    1b96:	46a1                	li	a3,8
    1b98:	bf3d                	j	1ad6 <_strtol_l.isra.0+0x68>
    1b9a:	03000713          	li	a4,48
    1b9e:	f2e782e3          	beq	a5,a4,1ac2 <_strtol_l.isra.0+0x54>
    1ba2:	46a9                	li	a3,10
    1ba4:	bf0d                	j	1ad6 <_strtol_l.isra.0+0x68>

00001ba6 <_strtol_r>:
    1ba6:	81018793          	addi	a5,gp,-2032 # 20000218 <_impure_ptr>
    1baa:	439c                	lw	a5,0(a5)
    1bac:	5398                	lw	a4,32(a5)
    1bae:	e709                	bnez	a4,1bb8 <_strtol_r+0x12>
    1bb0:	1fffe717          	auipc	a4,0x1fffe
    1bb4:	4e870713          	addi	a4,a4,1256 # 20000098 <__global_locale>
    1bb8:	bd5d                	j	1a6e <_strtol_l.isra.0>

00001bba <_strtoul_l.isra.0>:
    1bba:	7179                	addi	sp,sp,-48
    1bbc:	d04a                	sw	s2,32(sp)
    1bbe:	ce4e                	sw	s3,28(sp)
    1bc0:	d606                	sw	ra,44(sp)
    1bc2:	d422                	sw	s0,40(sp)
    1bc4:	d226                	sw	s1,36(sp)
    1bc6:	89aa                	mv	s3,a0
    1bc8:	892e                	mv	s2,a1
    1bca:	00190493          	addi	s1,s2,1
    1bce:	fff4c403          	lbu	s0,-1(s1)
    1bd2:	853a                	mv	a0,a4
    1bd4:	c636                	sw	a3,12(sp)
    1bd6:	c432                	sw	a2,8(sp)
    1bd8:	c22e                	sw	a1,4(sp)
    1bda:	c03a                	sw	a4,0(sp)
    1bdc:	2255                	jal	1d80 <__locale_ctype_ptr_l>
    1bde:	9522                	add	a0,a0,s0
    1be0:	311c                	lbu	a5,1(a0)
    1be2:	4702                	lw	a4,0(sp)
    1be4:	4592                	lw	a1,4(sp)
    1be6:	8ba1                	andi	a5,a5,8
    1be8:	4622                	lw	a2,8(sp)
    1bea:	46b2                	lw	a3,12(sp)
    1bec:	efa5                	bnez	a5,1c64 <_strtoul_l.isra.0+0xaa>
    1bee:	02d00793          	li	a5,45
    1bf2:	06f41b63          	bne	s0,a5,1c68 <_strtoul_l.isra.0+0xae>
    1bf6:	209c                	lbu	a5,0(s1)
    1bf8:	4305                	li	t1,1
    1bfa:	00290493          	addi	s1,s2,2
    1bfe:	cef9                	beqz	a3,1cdc <_strtoul_l.isra.0+0x122>
    1c00:	4741                	li	a4,16
    1c02:	02e69063          	bne	a3,a4,1c22 <_strtoul_l.isra.0+0x68>
    1c06:	03000713          	li	a4,48
    1c0a:	00e79c63          	bne	a5,a4,1c22 <_strtoul_l.isra.0+0x68>
    1c0e:	209c                	lbu	a5,0(s1)
    1c10:	05800713          	li	a4,88
    1c14:	0df7f793          	andi	a5,a5,223
    1c18:	0ae79d63          	bne	a5,a4,1cd2 <_strtoul_l.isra.0+0x118>
    1c1c:	309c                	lbu	a5,1(s1)
    1c1e:	46c1                	li	a3,16
    1c20:	0489                	addi	s1,s1,2
    1c22:	577d                	li	a4,-1
    1c24:	02d75e33          	divu	t3,a4,a3
    1c28:	4801                	li	a6,0
    1c2a:	4501                	li	a0,0
    1c2c:	4f25                	li	t5,9
    1c2e:	4ee5                	li	t4,25
    1c30:	02d778b3          	remu	a7,a4,a3
    1c34:	fd078713          	addi	a4,a5,-48
    1c38:	04ef6363          	bltu	t5,a4,1c7e <_strtoul_l.isra.0+0xc4>
    1c3c:	87ba                	mv	a5,a4
    1c3e:	06d7d063          	bge	a5,a3,1c9e <_strtoul_l.isra.0+0xe4>
    1c42:	04084c63          	bltz	a6,1c9a <_strtoul_l.isra.0+0xe0>
    1c46:	587d                	li	a6,-1
    1c48:	00ae6a63          	bltu	t3,a0,1c5c <_strtoul_l.isra.0+0xa2>
    1c4c:	00ae1463          	bne	t3,a0,1c54 <_strtoul_l.isra.0+0x9a>
    1c50:	00f8c663          	blt	a7,a5,1c5c <_strtoul_l.isra.0+0xa2>
    1c54:	4805                	li	a6,1
    1c56:	02a68533          	mul	a0,a3,a0
    1c5a:	953e                	add	a0,a0,a5
    1c5c:	0485                	addi	s1,s1,1
    1c5e:	fff4c783          	lbu	a5,-1(s1)
    1c62:	bfc9                	j	1c34 <_strtoul_l.isra.0+0x7a>
    1c64:	8926                	mv	s2,s1
    1c66:	b795                	j	1bca <_strtoul_l.isra.0+0x10>
    1c68:	02b00793          	li	a5,43
    1c6c:	00f40563          	beq	s0,a5,1c76 <_strtoul_l.isra.0+0xbc>
    1c70:	87a2                	mv	a5,s0
    1c72:	4301                	li	t1,0
    1c74:	b769                	j	1bfe <_strtoul_l.isra.0+0x44>
    1c76:	209c                	lbu	a5,0(s1)
    1c78:	00290493          	addi	s1,s2,2
    1c7c:	bfdd                	j	1c72 <_strtoul_l.isra.0+0xb8>
    1c7e:	fbf78713          	addi	a4,a5,-65
    1c82:	00eee563          	bltu	t4,a4,1c8c <_strtoul_l.isra.0+0xd2>
    1c86:	fc978793          	addi	a5,a5,-55
    1c8a:	bf55                	j	1c3e <_strtoul_l.isra.0+0x84>
    1c8c:	f9f78713          	addi	a4,a5,-97
    1c90:	00eee763          	bltu	t4,a4,1c9e <_strtoul_l.isra.0+0xe4>
    1c94:	fa978793          	addi	a5,a5,-87
    1c98:	b75d                	j	1c3e <_strtoul_l.isra.0+0x84>
    1c9a:	587d                	li	a6,-1
    1c9c:	b7c1                	j	1c5c <_strtoul_l.isra.0+0xa2>
    1c9e:	00085f63          	bgez	a6,1cbc <_strtoul_l.isra.0+0x102>
    1ca2:	02200793          	li	a5,34
    1ca6:	00f9a023          	sw	a5,0(s3)
    1caa:	557d                	li	a0,-1
    1cac:	ee19                	bnez	a2,1cca <_strtoul_l.isra.0+0x110>
    1cae:	50b2                	lw	ra,44(sp)
    1cb0:	5422                	lw	s0,40(sp)
    1cb2:	5492                	lw	s1,36(sp)
    1cb4:	5902                	lw	s2,32(sp)
    1cb6:	49f2                	lw	s3,28(sp)
    1cb8:	6145                	addi	sp,sp,48
    1cba:	8082                	ret
    1cbc:	00030463          	beqz	t1,1cc4 <_strtoul_l.isra.0+0x10a>
    1cc0:	40a00533          	neg	a0,a0
    1cc4:	d66d                	beqz	a2,1cae <_strtoul_l.isra.0+0xf4>
    1cc6:	00080463          	beqz	a6,1cce <_strtoul_l.isra.0+0x114>
    1cca:	fff48593          	addi	a1,s1,-1
    1cce:	c20c                	sw	a1,0(a2)
    1cd0:	bff9                	j	1cae <_strtoul_l.isra.0+0xf4>
    1cd2:	03000793          	li	a5,48
    1cd6:	f6b1                	bnez	a3,1c22 <_strtoul_l.isra.0+0x68>
    1cd8:	46a1                	li	a3,8
    1cda:	b7a1                	j	1c22 <_strtoul_l.isra.0+0x68>
    1cdc:	03000713          	li	a4,48
    1ce0:	f2e787e3          	beq	a5,a4,1c0e <_strtoul_l.isra.0+0x54>
    1ce4:	46a9                	li	a3,10
    1ce6:	bf35                	j	1c22 <_strtoul_l.isra.0+0x68>

00001ce8 <_strtoul_r>:
    1ce8:	81018793          	addi	a5,gp,-2032 # 20000218 <_impure_ptr>
    1cec:	439c                	lw	a5,0(a5)
    1cee:	5398                	lw	a4,32(a5)
    1cf0:	e709                	bnez	a4,1cfa <_strtoul_r+0x12>
    1cf2:	1fffe717          	auipc	a4,0x1fffe
    1cf6:	3a670713          	addi	a4,a4,934 # 20000098 <__global_locale>
    1cfa:	b5c1                	j	1bba <_strtoul_l.isra.0>

00001cfc <__submore>:
    1cfc:	1101                	addi	sp,sp,-32
    1cfe:	cc22                	sw	s0,24(sp)
    1d00:	842e                	mv	s0,a1
    1d02:	59cc                	lw	a1,52(a1)
    1d04:	ce06                	sw	ra,28(sp)
    1d06:	ca26                	sw	s1,20(sp)
    1d08:	c84a                	sw	s2,16(sp)
    1d0a:	c64e                	sw	s3,12(sp)
    1d0c:	c452                	sw	s4,8(sp)
    1d0e:	04440793          	addi	a5,s0,68
    1d12:	04f59263          	bne	a1,a5,1d56 <__submore+0x5a>
    1d16:	40000593          	li	a1,1024
    1d1a:	b66ff0ef          	jal	ra,1080 <_malloc_r>
    1d1e:	e911                	bnez	a0,1d32 <__submore+0x36>
    1d20:	557d                	li	a0,-1
    1d22:	40f2                	lw	ra,28(sp)
    1d24:	4462                	lw	s0,24(sp)
    1d26:	44d2                	lw	s1,20(sp)
    1d28:	4942                	lw	s2,16(sp)
    1d2a:	49b2                	lw	s3,12(sp)
    1d2c:	4a22                	lw	s4,8(sp)
    1d2e:	6105                	addi	sp,sp,32
    1d30:	8082                	ret
    1d32:	40000793          	li	a5,1024
    1d36:	dc1c                	sw	a5,56(s0)
    1d38:	04644783          	lbu	a5,70(s0)
    1d3c:	d848                	sw	a0,52(s0)
    1d3e:	3fd50513          	addi	a0,a0,1021
    1d42:	a13c                	sb	a5,2(a0)
    1d44:	04544783          	lbu	a5,69(s0)
    1d48:	b11c                	sb	a5,1(a0)
    1d4a:	04444783          	lbu	a5,68(s0)
    1d4e:	a11c                	sb	a5,0(a0)
    1d50:	c008                	sw	a0,0(s0)
    1d52:	4501                	li	a0,0
    1d54:	b7f9                	j	1d22 <__submore+0x26>
    1d56:	03842903          	lw	s2,56(s0)
    1d5a:	00191993          	slli	s3,s2,0x1
    1d5e:	864e                	mv	a2,s3
    1d60:	2061                	jal	1de8 <_realloc_r>
    1d62:	84aa                	mv	s1,a0
    1d64:	dd55                	beqz	a0,1d20 <__submore+0x24>
    1d66:	01250a33          	add	s4,a0,s2
    1d6a:	85aa                	mv	a1,a0
    1d6c:	864a                	mv	a2,s2
    1d6e:	8552                	mv	a0,s4
    1d70:	9baff0ef          	jal	ra,f2a <memcpy>
    1d74:	01442023          	sw	s4,0(s0)
    1d78:	d844                	sw	s1,52(s0)
    1d7a:	03342c23          	sw	s3,56(s0)
    1d7e:	bfd1                	j	1d52 <__submore+0x56>

00001d80 <__locale_ctype_ptr_l>:
    1d80:	0ec52503          	lw	a0,236(a0)
    1d84:	8082                	ret

00001d86 <__locale_ctype_ptr>:
    1d86:	81018793          	addi	a5,gp,-2032 # 20000218 <_impure_ptr>
    1d8a:	439c                	lw	a5,0(a5)
    1d8c:	539c                	lw	a5,32(a5)
    1d8e:	e789                	bnez	a5,1d98 <__locale_ctype_ptr+0x12>
    1d90:	1fffe797          	auipc	a5,0x1fffe
    1d94:	30878793          	addi	a5,a5,776 # 20000098 <__global_locale>
    1d98:	0ec7a503          	lw	a0,236(a5)
    1d9c:	8082                	ret

00001d9e <__ascii_mbtowc>:
    1d9e:	ed91                	bnez	a1,1dba <__ascii_mbtowc+0x1c>
    1da0:	1141                	addi	sp,sp,-16
    1da2:	006c                	addi	a1,sp,12
    1da4:	4501                	li	a0,0
    1da6:	ca01                	beqz	a2,1db6 <__ascii_mbtowc+0x18>
    1da8:	5579                	li	a0,-2
    1daa:	c691                	beqz	a3,1db6 <__ascii_mbtowc+0x18>
    1dac:	221c                	lbu	a5,0(a2)
    1dae:	c19c                	sw	a5,0(a1)
    1db0:	2208                	lbu	a0,0(a2)
    1db2:	00a03533          	snez	a0,a0
    1db6:	0141                	addi	sp,sp,16
    1db8:	8082                	ret
    1dba:	4501                	li	a0,0
    1dbc:	ca09                	beqz	a2,1dce <__ascii_mbtowc+0x30>
    1dbe:	5579                	li	a0,-2
    1dc0:	c699                	beqz	a3,1dce <__ascii_mbtowc+0x30>
    1dc2:	221c                	lbu	a5,0(a2)
    1dc4:	c19c                	sw	a5,0(a1)
    1dc6:	2208                	lbu	a0,0(a2)
    1dc8:	00a03533          	snez	a0,a0
    1dcc:	8082                	ret
    1dce:	8082                	ret

00001dd0 <memchr>:
    1dd0:	0ff5f593          	andi	a1,a1,255
    1dd4:	962a                	add	a2,a2,a0
    1dd6:	00c51463          	bne	a0,a2,1dde <memchr+0xe>
    1dda:	4501                	li	a0,0
    1ddc:	8082                	ret
    1dde:	211c                	lbu	a5,0(a0)
    1de0:	feb78ee3          	beq	a5,a1,1ddc <memchr+0xc>
    1de4:	0505                	addi	a0,a0,1
    1de6:	bfc5                	j	1dd6 <memchr+0x6>

00001de8 <_realloc_r>:
    1de8:	e581                	bnez	a1,1df0 <_realloc_r+0x8>
    1dea:	85b2                	mv	a1,a2
    1dec:	a94ff06f          	j	1080 <_malloc_r>
    1df0:	1101                	addi	sp,sp,-32
    1df2:	cc22                	sw	s0,24(sp)
    1df4:	ce06                	sw	ra,28(sp)
    1df6:	ca26                	sw	s1,20(sp)
    1df8:	c84a                	sw	s2,16(sp)
    1dfa:	c64e                	sw	s3,12(sp)
    1dfc:	8432                	mv	s0,a2
    1dfe:	ee01                	bnez	a2,1e16 <_realloc_r+0x2e>
    1e00:	9dcff0ef          	jal	ra,fdc <_free_r>
    1e04:	4901                	li	s2,0
    1e06:	40f2                	lw	ra,28(sp)
    1e08:	4462                	lw	s0,24(sp)
    1e0a:	854a                	mv	a0,s2
    1e0c:	44d2                	lw	s1,20(sp)
    1e0e:	4942                	lw	s2,16(sp)
    1e10:	49b2                	lw	s3,12(sp)
    1e12:	6105                	addi	sp,sp,32
    1e14:	8082                	ret
    1e16:	84ae                	mv	s1,a1
    1e18:	89aa                	mv	s3,a0
    1e1a:	2091                	jal	1e5e <_malloc_usable_size_r>
    1e1c:	8926                	mv	s2,s1
    1e1e:	fe8574e3          	bgeu	a0,s0,1e06 <_realloc_r+0x1e>
    1e22:	85a2                	mv	a1,s0
    1e24:	854e                	mv	a0,s3
    1e26:	a5aff0ef          	jal	ra,1080 <_malloc_r>
    1e2a:	892a                	mv	s2,a0
    1e2c:	dd69                	beqz	a0,1e06 <_realloc_r+0x1e>
    1e2e:	85a6                	mv	a1,s1
    1e30:	8622                	mv	a2,s0
    1e32:	8f8ff0ef          	jal	ra,f2a <memcpy>
    1e36:	85a6                	mv	a1,s1
    1e38:	854e                	mv	a0,s3
    1e3a:	9a2ff0ef          	jal	ra,fdc <_free_r>
    1e3e:	b7e1                	j	1e06 <_realloc_r+0x1e>

00001e40 <__ascii_wctomb>:
    1e40:	cd89                	beqz	a1,1e5a <__ascii_wctomb+0x1a>
    1e42:	0ff00793          	li	a5,255
    1e46:	00c7f763          	bgeu	a5,a2,1e54 <__ascii_wctomb+0x14>
    1e4a:	08a00793          	li	a5,138
    1e4e:	c11c                	sw	a5,0(a0)
    1e50:	557d                	li	a0,-1
    1e52:	8082                	ret
    1e54:	a190                	sb	a2,0(a1)
    1e56:	4505                	li	a0,1
    1e58:	8082                	ret
    1e5a:	4501                	li	a0,0
    1e5c:	8082                	ret

00001e5e <_malloc_usable_size_r>:
    1e5e:	ffc5a783          	lw	a5,-4(a1)
    1e62:	ffc78513          	addi	a0,a5,-4
    1e66:	0007d563          	bgez	a5,1e70 <_malloc_usable_size_r+0x12>
    1e6a:	95aa                	add	a1,a1,a0
    1e6c:	419c                	lw	a5,0(a1)
    1e6e:	953e                	add	a0,a0,a5
    1e70:	8082                	ret
    1e72:	0000                	unimp
    1e74:	7061                	c.lui	zero,0xffff8
    1e76:	6b70                	flw	fa2,84(a4)
    1e78:	7965                	lui	s2,0xffff9
    1e7a:	0000                	unimp
    1e7c:	7061                	c.lui	zero,0xffff8
    1e7e:	6b70                	flw	fa2,84(a4)
    1e80:	7965                	lui	s2,0xffff9
    1e82:	3a22                	lhu	s0,50(a2)
    1e84:	6425                	lui	s0,0x9
    1e86:	0000                	unimp
    1e88:	6161                	addi	sp,sp,80
	...

00001e8c <__sf_fake_stderr>:
	...

00001eac <__sf_fake_stdin>:
	...

00001ecc <__sf_fake_stdout>:
	...
    1eec:	6c68 004c 2d41 6146 662d 3938 3130 3332     hlL.A-Fa-f890123
    1efc:	3534 3736 005d 0000 2d2b 0000 3030 0000     4567]...+-..00..
    1f0c:	5878 0000 0043 0000 4f50 4953 0058 0000     xX..C...POSIX...
    1f1c:	0000 0000 002e 0000                         ........

00001f24 <_ctype_>:
    1f24:	2000 2020 2020 2020 2020 2828 2828 2028     .         ((((( 
    1f34:	2020 2020 2020 2020 2020 2020 2020 2020                     
    1f44:	8820 1010 1010 1010 1010 1010 1010 1010      ...............
    1f54:	0410 0404 0404 0404 0404 1004 1010 1010     ................
    1f64:	1010 4141 4141 4141 0101 0101 0101 0101     ..AAAAAA........
    1f74:	0101 0101 0101 0101 0101 0101 1010 1010     ................
    1f84:	1010 4242 4242 4242 0202 0202 0202 0202     ..BBBBBB........
    1f94:	0202 0202 0202 0202 0202 0202 1010 1010     ................
    1fa4:	0020 0000 0000 0000 0000 0000 0000 0000      ...............
	...
