# ESP8266联网问题调试指南

## 问题描述
ESP8266在使用AT指令时可以联网，但在工程代码中使用串口发送AT指令无法实现联网。

## 🔍 最新发现的问题
根据您的串口输出，问题已经定位：
- ESP8266硬件连接正常
- AT指令格式正确
- **主要问题：WiFi密码错误（错误代码+CWJAP:4）**

## 🚨 紧急解决方案

### 方案1：确认WiFi密码
请确认WiFi "ttt" 的正确密码，然后修改 `APP/esp8266.c` 第10行：
```c
u8 string4[] = "AT+CWJAP=\"ttt\",\"正确的密码\"\r\n";
```

### 方案2：使用WiFi扫描功能
在main.c中临时替换初始化函数：
```c
// 替换这行：
onenet_init_debug();
// 为：
esp8266_scan_wifi();
```
这将显示所有可用的WiFi网络，确认"ttt"网络是否存在。

### 方案3：使用连接测试功能
```c
// 使用这个函数测试WiFi连接：
esp8266_test_wifi_connection();
```

## 已修复的问题

### 1. 中文乱码问题
- ✅ 已修复main.c文件中的中文注释乱码
- ✅ 已修复esp8266.c文件中的中文注释乱码
- ✅ 已修复uart.c文件中的中文注释乱码

### 2. 代码优化
- ✅ 修复了UART8初始化中的错误（UART6 -> UART8）
- ✅ 优化了ESP8266初始化时序，增加了合适的延时
- ✅ 添加了调试版本的初始化函数

## ESP8266联网问题分析

### 可能的原因：

1. **硬件连接问题**
   - 检查UART6的引脚连接：PC0(TX), PC1(RX)
   - 确认ESP8266的波特率设置为115200
   - 检查电源供电是否稳定（ESP8266需要3.3V）

2. **时序问题**
   - AT指令之间的延时不够
   - 复位后等待时间不足
   - WiFi连接需要更长时间

3. **AT指令格式问题**
   - 确认WiFi名称和密码正确
   - 检查AT指令格式是否正确

## 调试步骤

### 步骤1：使用调试版本初始化
在main.c中将：
```c
onenet_init();
```
替换为：
```c
onenet_init_debug();
```

### 步骤2：检查串口输出
通过USART1查看调试信息，确认每个步骤的执行情况。

### 步骤3：检查WiFi参数
确认以下参数正确：
- WiFi名称："ttt"
- WiFi密码："20051121"

### 步骤4：硬件检查
1. 确认ESP8266模块正常工作
2. 检查串口连接：
   - CH32的PC0连接ESP8266的RX
   - CH32的PC1连接ESP8266的TX
   - 共地连接

### 步骤5：波特率检查
ESP8266默认波特率可能是：
- 115200（当前设置）
- 9600
- 57600

如果115200不工作，尝试修改uart.c中的波特率：
```c
USART_InitStructure.USART_BaudRate = 9600; // 或其他波特率
```

## 常见问题解决

### 问题1：ESP8266无响应
- 检查电源和复位
- 确认波特率匹配
- 检查硬件连接

### 问题2：WiFi连接失败
- 确认WiFi名称和密码
- 检查WiFi信号强度
- 尝试连接其他WiFi网络

### 问题3：MQTT连接失败
- 确认网络连接正常
- 检查MQTT服务器地址和端口
- 验证MQTT认证信息

## 建议的测试流程

1. 首先使用调试版本初始化
2. 通过串口监控查看每步执行结果
3. 如果某步失败，增加该步骤的延时
4. 逐步排查硬件和软件问题

## 联系信息
如果问题仍然存在，请提供：
1. 串口调试输出信息
2. 硬件连接图
3. ESP8266模块型号
