/*
 * cry_detect.c - 婴儿哭声识别模块
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#include "cry_detect.h"
#include "uart.h"
#include "../FFT/kiss_fft.h"
#include "../Debug/debug.h"
#include <math.h>
#include <string.h>

/* 外部变量声明 */
extern u8 string9[]; // MQTT发布消息字符串

/* 全局变量 */
u16 TxBuf[SAMPLE_SIZE];             // ADC采样缓冲区
s16 Calibrattion_Val = 0;           // ADC校准值
cry_status_t cry_status;            // 系统状态

/* 内部变量 */
static uint32_t energy_buf[SMOOTH_SIZE] = {0};  // 能量平滑缓冲区
static uint16_t peak_buf[SMOOTH_SIZE] = {0};    // 峰值平滑缓冲区
static uint16_t zcr_buf[SMOOTH_SIZE] = {0};     // 零交叉率平滑缓冲区
static uint8_t smooth_index = 0;                // 平滑缓冲区索引

static kiss_fft_cfg fft_cfg;                    // FFT配置
static kiss_fft_cpx fft_in[SAMPLE_SIZE];        // FFT输入缓冲区
static kiss_fft_cpx fft_out[SAMPLE_SIZE];       // FFT输出缓冲区
static uint32_t last_print_time = 0;            // 上次打印时间
static uint8_t last_status = 0;                 // 上次状态(0:安静,1:说话,2:哭声)
static uint32_t print_interval = 6;             // 打印间隔6次(600ms)
static uint32_t time_counter = 0;               // 时间计数器

/* 自适应噪声估计变量 */
static uint32_t noise_samples[NOISE_BUFFER_SIZE] = {0}; // 噪声样本缓冲区
static uint8_t noise_sample_index = 0;          // 噪声样本索引
static uint8_t noise_sample_count = 0;          // 已采集的噪声样本数
static uint32_t noise_baseline = 500;           // 噪声基线(初始值)
static uint32_t last_noise_update = 0;          // 上次噪声更新时间
static uint8_t noise_initialized = 0;           // 噪声估计初始化标志
static uint32_t quiet_sample_count = 0;         // 连续安静样本计数

/* 动态阈值调整变量 */
static uint32_t current_energy_threshold = ENERGY_THRESHOLD; // 当前能量阈值
static uint16_t current_zcr_threshold = ZCR_THRESHOLD;       // 当前零交叉率阈值
static uint32_t threshold_history[5] = {0};     // 阈值历史记录
static uint8_t threshold_history_index = 0;     // 阈值历史索引
static uint8_t threshold_initialized = 0;       // 阈值初始化标志

/* AGC相关变量 */
static float current_gain = 1.0f;               // 当前增益值
static float smooth_gain = 1.0f;                // 平滑增益值
static uint8_t agc_initialized = 0;             // AGC初始化标志
static uint32_t agc_update_counter = 0;         // AGC更新计数器

/* 校准相关变量 */
static uint8_t calibration_active = 0;          // 校准模式标志
static uint16_t calibration_samples = 0;        // 已收集的校准样本数
static uint32_t calibration_energy_sum = 0;     // 校准能量累计
static uint32_t calibration_zcr_sum = 0;        // 校准ZCR累计
static float calibration_rms_sum = 0.0f;        // 校准RMS累计
static uint16_t calibration_quiet_counter = 0;  // 校准前安静计数器
static calibration_data_t current_calibration;  // 当前校准数据

/* 增强特征相关变量 */
static audio_features_t current_features;       // 当前音频特征
static audio_features_t smooth_features;        // 平滑后的特征
static uint32_t energy_history[ENERGY_VARIANCE_WINDOW] = {0}; // 能量历史
static uint8_t energy_history_index = 0;        // 能量历史索引
static uint8_t enhanced_features_initialized = 0; // 增强特征初始化标志
static float fft_magnitude[SAMPLE_SIZE/2];       // FFT幅度谱

/* 置信度评估相关变量 */
static float confidence_history[CONFIDENCE_HISTORY_SIZE] = {0.0f}; // 置信度历史
static uint8_t confidence_history_index = 0;    // 置信度历史索引
static uint8_t confidence_history_count = 0;    // 置信度历史计数
static float smooth_confidence = 0.0f;          // 平滑置信度
static uint8_t confidence_initialized = 0;      // 置信度初始化标志
static confidence_result_t last_confidence_result; // 上次置信度结果

/* 特征权重配置 */
static const float feature_weights[6] = {
    0.25f,  // 频谱质心权重
    0.20f,  // 频谱带宽权重
    0.15f,  // 能量方差权重
    0.15f,  // 频谱滚降权重
    0.15f,  // 能量权重
    0.10f   // 零交叉率权重
};

/* 调试和监控相关变量 */
static debug_state_t debug_state = {0};         // 调试状态
static performance_stats_t perf_stats = {0};    // 性能统计
static uint32_t debug_last_output_time = 0;     // 上次调试输出时间
static char debug_command_buffer[DEBUG_BUFFER_SIZE] = {0}; // 调试命令缓冲区
static uint8_t debug_command_index = 0;         // 调试命令索引

/* 内部函数声明 */
static void cry_classify_cry_type(int16_t* audio_data);
static void uart_flush(void);                   // 串口刷新函数

/**
 * @brief ADC功能初始化
 */
void ADC_Function_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure={0};
    GPIO_InitTypeDef GPIO_InitStructure={0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);
    RCC_ADCCLKConfig(RCC_PCLK2_Div8);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    ADC_DeInit(ADC1);
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 1;
    ADC_Init(ADC1, &ADC_InitStructure);

    ADC_DMACmd(ADC1, ENABLE);
    ADC_Cmd(ADC1, ENABLE);

    ADC_BufferCmd(ADC1, DISABLE);   // 禁用缓冲
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    Calibrattion_Val = Get_CalibrationValue(ADC1);
}

/**
 * @brief DMA传输初始化
 */
void DMA_Tx_Init(DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize)
{
    DMA_InitTypeDef DMA_InitStructure={0};

    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);

    DMA_DeInit(DMA_CHx);
    DMA_InitStructure.DMA_PeripheralBaseAddr = ppadr;
    DMA_InitStructure.DMA_MemoryBaseAddr = memadr;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = bufsize;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA_CHx, &DMA_InitStructure);
}

/**
 * @brief 获取校准后的ADC转换值
 */
u16 Get_ConversionVal(s16 val)
{
    if((val+Calibrattion_Val)<0|| val==0) return 0;
    if((Calibrattion_Val+val)>4095||val==4095) return 4095;
    return (val+Calibrattion_Val);
}

/**
 * @brief 哭声检测模块初始化
 */
void cry_detect_init(void)
{
    printf("Cry detect init starting...\r\n");
    uart_flush();

    // 初始化FFT配置
    fft_cfg = kiss_fft_alloc(SAMPLE_SIZE, 0, NULL, NULL);
    if (fft_cfg == NULL) {
        printf("FFT config allocation failed!\r\n");
        uart_flush();
        return;
    }
    
    // 初始化ADC和DMA
    ADC_Function_Init();
    printf("ADC calibration value: %d\r\n", Calibrattion_Val);
    uart_flush();

    DMA_Tx_Init(DMA1_Channel1, (u32)&ADC1->RDATAR, (u32)TxBuf, SAMPLE_SIZE);
    DMA_Cmd(DMA1_Channel1, ENABLE);

    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_239Cycles5);
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);

    // 添加ADC和DMA状态检查
    printf("ADC Status: CR2=0x%08lx, SR=0x%08lx\r\n", ADC1->CTLR2, ADC1->STATR);
    printf("DMA Status: CCR=0x%08lx, CNDTR=%lu\r\n", DMA1_Channel1->CFGR, DMA1_Channel1->CNTR);

    // 等待一小段时间让DMA开始工作
    Delay_Ms(100);

    // 检查DMA是否在传输数据
    printf("After 100ms - DMA CNDTR=%lu\r\n", DMA1_Channel1->CNTR);
    printf("TxBuf[0-4]: %u %u %u %u %u\r\n", TxBuf[0], TxBuf[1], TxBuf[2], TxBuf[3], TxBuf[4]);

    // 手动测试ADC转换
    printf("Manual ADC test:\r\n");
    for (int i = 0; i < 5; i++) {
        ADC_SoftwareStartConvCmd(ADC1, ENABLE);
        while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));
        uint16_t adc_val = ADC_GetConversionValue(ADC1);
        printf("ADC[%d]: %u\r\n", i, adc_val);
        Delay_Ms(10);
    }

    // 手动填充TxBuf进行测试
    printf("Filling TxBuf with test data...\r\n");
    for (int i = 0; i < 10; i++) {
        TxBuf[i] = 2048 + i * 10; // 填充测试数据
    }
    printf("Test TxBuf[0-4]: %u %u %u %u %u\r\n", TxBuf[0], TxBuf[1], TxBuf[2], TxBuf[3], TxBuf[4]);

    uart_flush();

    // 初始化状态
    cry_status.initialized = 1;
    cry_status.cry_detected = 0;
    cry_status.cry_type = CRY_TYPE_UNKNOWN;
    cry_status.last_send_time = 0;

    // 初始化自适应噪声估计
    noise_estimation_init();

    // 初始化动态阈值调整
    dynamic_threshold_init();

    // 初始化AGC
    agc_init();

    // 初始化校准功能
    calibration_init();

    // 初始化增强特征
    enhanced_features_init();

    // 初始化置信度评估
    confidence_init();

    printf("Cry detect init completed\r\n");
    uart_flush();
}

/**
 * @brief 哭声检测主处理函数
 */
void cry_detect_proc(void)
{
    if (!cry_status.initialized) {
        return;
    }
    
    // 准备音频数据进行AGC处理
    static int16_t audio_samples[SAMPLE_SIZE];

    // 计算DC偏移
    uint32_t dc_offset_sum = 0;
    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        dc_offset_sum += Get_ConversionVal(TxBuf[i]);
    }
    uint16_t dc_offset = dc_offset_sum / SAMPLE_SIZE;

    // 转换为有符号数据并去除DC偏移
    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        audio_samples[i] = Get_ConversionVal(TxBuf[i]) - dc_offset;
    }

    // 应用AGC处理
    agc_process(audio_samples, SAMPLE_SIZE);

    // 计算能量、峰值和零交叉率
    uint32_t energy_sum = 0;
    uint16_t peak_val = 0;
    uint16_t zcr = 0;
    int16_t last_sample = audio_samples[0];
    
    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        int16_t sample = audio_samples[i]; // 使用AGC处理后的数据
        uint16_t abs_sample = abs(sample);

        if (abs_sample > peak_val) peak_val = abs_sample;

        energy_sum += (int32_t)sample * sample;

        // 计算零交叉率
        if ((sample > 0 && last_sample < 0) || (sample < 0 && last_sample > 0)) {
            zcr++;
        }
        last_sample = sample;
    }
    
    uint32_t avg_energy = energy_sum / SAMPLE_SIZE;
    
    // 平滑滤波
    energy_buf[smooth_index] = avg_energy;
    peak_buf[smooth_index] = peak_val;
    zcr_buf[smooth_index] = zcr;
    
    // 计算平滑值
    uint32_t energy_sum_s = 0;
    uint16_t peak_sum_s = 0, zcr_sum_s = 0;
    for (uint8_t i = 0; i < SMOOTH_SIZE; i++) {
        energy_sum_s += energy_buf[i];
        peak_sum_s += peak_buf[i];
        zcr_sum_s += zcr_buf[i];
    }
    
    uint32_t smooth_energy = energy_sum_s / SMOOTH_SIZE;
    uint16_t smooth_peak = peak_sum_s / SMOOTH_SIZE;
    uint16_t smooth_zcr = zcr_sum_s / SMOOTH_SIZE;
    
    smooth_index = (smooth_index + 1) % SMOOTH_SIZE;

    // 时间计数器递增
    time_counter++;
    uint8_t current_status = 0; // 0:安静,1:说话,2:哭声

    // 每50次(5秒)输出一次调试信息
    static uint32_t debug_counter = 0;
    debug_counter++;
    if (debug_counter >= 50) {
        printf("DEBUG: Energy=%lu, ZCR=%u, Peak=%u, DC_offset=%u\r\n",
               smooth_energy, smooth_zcr, smooth_peak, dc_offset);
        debug_counter = 0;
        uart_flush();
    }

    // 更新动态阈值
    dynamic_threshold_update(smooth_energy, smooth_zcr);

    // 获取当前动态阈值
    uint32_t current_energy_th = get_current_energy_threshold();
    uint16_t current_zcr_th = get_current_zcr_threshold();

    // 使用动态阈值检测哭声
    if (smooth_energy > current_energy_th && smooth_zcr > current_zcr_th) {
        current_status = 2; // 哭声
        cry_status.cry_detected = 1;

        // 状态变化或超过打印间隔时才输出
        if (last_status != current_status || (time_counter - last_print_time) >= print_interval) {
            printf("Peak: %d, Energy: %ld, ZCR: %d (Th: E=%lu, Z=%d)\r\n",
                   smooth_peak, smooth_energy, smooth_zcr, current_energy_th, current_zcr_th);
            printf(">>> Crying Detected <<<\r\n");
            uart_flush(); // 确保数据及时发送
            last_print_time = time_counter;

            // 执行FFT分析进行分类
            cry_classify_cry_type(audio_samples);
        }

        // 发送UART8触发信号(每次检测到都发送)
        if (last_status != current_status) {
            uint8_t trigger_msg[] = "bb";
            uart8_send_string(trigger_msg, 2); // 使用字符串发送函数
        }

    } else if (smooth_energy > current_energy_th && smooth_zcr <= current_zcr_th) {
        current_status = 1; // 说话
        cry_status.cry_detected = 0;

        // 状态变化时才输出
        if (last_status != current_status) {
            printf("Peak: %d, Energy: %ld, ZCR: %d (Th: E=%lu, Z=%d)\r\n",
                   smooth_peak, smooth_energy, smooth_zcr, current_energy_th, current_zcr_th);
            printf(">>> Speaking Detected <<<\r\n");
            uart_flush(); // 确保数据及时发送
            last_print_time = time_counter;
        }
    } else {
        current_status = 0; // 安静
        cry_status.cry_detected = 0;

        // 状态变化或长时间无输出时才输出
        if (last_status != current_status || (time_counter - last_print_time) >= (print_interval * 10)) {
            printf("Peak: %d, Energy: %ld, ZCR: %d (Th: E=%lu, Z=%d)\r\n",
                   smooth_peak, smooth_energy, smooth_zcr, current_energy_th, current_zcr_th);
            printf(">>> Quiet <<<\r\n");
            uart_flush(); // 确保数据及时发送
            last_print_time = time_counter;
        }
    }

    // 更新噪声估计
    noise_estimation_proc(smooth_energy, current_status);

    // 校准数据处理
    if (calibration_is_active()) {
        float current_rms = calculate_rms(audio_samples, SAMPLE_SIZE);
        calibration_process(smooth_energy, smooth_zcr, current_rms);
    }

    last_status = current_status;
}

/**
 * @brief 哭声类型分类
 * @param audio_data AGC处理后的音频数据
 */
void cry_classify_cry_type(int16_t* audio_data)
{
    // 准备FFT输入数据(使用AGC处理后的数据)
    for (int i = 0; i < SAMPLE_SIZE; i++) {
        fft_in[i].r = audio_data[i];  // 使用AGC处理后的数据
        fft_in[i].i = 0;
    }
    
    // 执行FFT
    kiss_fft(fft_cfg, fft_in, fft_out);

    // 计算FFT幅度谱
    for (int i = 0; i < SAMPLE_SIZE / 2; i++) {
        fft_magnitude[i] = sqrtf(fft_out[i].r * fft_out[i].r + fft_out[i].i * fft_out[i].i);
    }

    // 提取增强特征
    extract_enhanced_features(fft_magnitude, audio_data, &current_features);

    // 使用增强特征进行分类
    uint8_t cry_type = classify_cry_with_enhanced_features(&smooth_features);

    // 计算频段能量(保留原有分析)
    float low_energy = 0, mid_energy = 0, high_energy = 0;
    for (int i = 1; i < SAMPLE_SIZE / 2; i++) {
        float mag = fft_magnitude[i];

        // 采样率为8000Hz，每个频点频率为8000/1024≈7.8Hz
        float freq = (8000.0f / SAMPLE_SIZE) * i;

        if (freq < 500) {
            low_energy += mag;
        } else if (freq < 1500) {
            mid_energy += mag;
        } else {
            high_energy += mag;
        }
    }
    
    // 增强特征分类判断
    cry_type_e new_type;
    switch(cry_type) {
        case 1: new_type = CRY_TYPE_HUNGRY; break;
        case 2: new_type = CRY_TYPE_SLEEPY; break;
        case 3: new_type = CRY_TYPE_ANGRY; break;  // 生病映射为生气
        case 4: new_type = CRY_TYPE_HUNGRY; break; // 无聊映射为饥饿
        default: new_type = CRY_TYPE_HUNGRY; break;
    }

    // 传统频段分析作为备用
    cry_type_e traditional_type;
    if (low_energy > mid_energy && low_energy > high_energy) {
        traditional_type = CRY_TYPE_SLEEPY;
    } else if (mid_energy > low_energy && mid_energy > high_energy) {
        traditional_type = CRY_TYPE_ANGRY;
    } else {
        traditional_type = CRY_TYPE_HUNGRY;
    }

    // 计算置信度
    float confidence = calculate_confidence(&smooth_features, cry_type);

    // 只在类型变化时输出分类结果
    if (cry_status.cry_type != new_type) {
        cry_status.cry_type = new_type;

        // 使用置信度处理分类结果
        process_classification_with_confidence(cry_type, confidence);

        // 打印增强特征值
        printf("Enhanced Features:\r\n");
        printf("  Centroid: %.1fHz, Bandwidth: %.1fHz\r\n",
               smooth_features.spectral_centroid, smooth_features.spectral_bandwidth);
        printf("  Rolloff: %.1fHz, Variance: %.0f\r\n",
               smooth_features.spectral_rolloff, smooth_features.energy_variance);

        // 打印频段能量值(传统分析)
        uint32_t low_val = (uint32_t)(low_energy * 1000);
        uint32_t mid_val = (uint32_t)(mid_energy * 1000);
        uint32_t high_val = (uint32_t)(high_energy * 1000);
        printf("Traditional: Low: %lu Mid: %lu High: %lu\r\n", low_val, mid_val, high_val);
        uart_flush(); // 确保数据及时发送
    }
}

/**
 * @brief 获取检测状态
 */
uint8_t cry_get_status(void)
{
    return cry_status.cry_detected;
}

/**
 * @brief 获取哭声类型
 */
cry_type_e cry_get_type(void)
{
    return cry_status.cry_type;
}

/**
 * @brief 串口刷新函数，确保数据及时发送
 */
static void uart_flush(void)
{
    // 等待UART1发送完成
    while(USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
    Delay_Ms(1); // 短暂延时确保数据发送
}

/**
 * @brief 自适应噪声估计初始化
 */
void noise_estimation_init(void)
{
    // 初始化噪声样本缓冲区
    for (uint8_t i = 0; i < NOISE_BUFFER_SIZE; i++) {
        noise_samples[i] = 0;
    }

    noise_sample_index = 0;
    noise_sample_count = 0;
    noise_baseline = 500;           // 设置合理的初始噪声基线
    last_noise_update = 0;
    noise_initialized = 0;
    quiet_sample_count = 0;

    printf("Noise estimation initialized, baseline: %lu\r\n", noise_baseline);
    uart_flush();
}

/**
 * @brief 噪声估计处理函数
 * @param current_energy 当前能量值
 * @param current_status 当前状态(0:安静,1:说话,2:哭声)
 */
void noise_estimation_proc(uint32_t current_energy, uint8_t current_status)
{
    // 只在安静状态下采集噪声样本
    if (current_status == 0) { // 安静状态
        quiet_sample_count++;

        // 降低连续安静样本要求，更容易触发噪声更新
        if (quiet_sample_count >= 5) { // 降低从MIN_QUIET_SAMPLES到5
            // 添加噪声样本到缓冲区
            noise_samples[noise_sample_index] = current_energy;
            noise_sample_index = (noise_sample_index + 1) % NOISE_BUFFER_SIZE;

            if (noise_sample_count < NOISE_BUFFER_SIZE) {
                noise_sample_count++;
            }

            // 当缓冲区填满或达到更新间隔时，更新噪声基线
            // 降低更新间隔要求，从NOISE_UPDATE_INTERVAL改为50(5秒)
            if ((noise_sample_count >= NOISE_BUFFER_SIZE) ||
                (time_counter - last_noise_update >= 50)) {

                // 计算噪声基线(取平均值)
                uint32_t noise_sum = 0;
                uint8_t valid_samples = (noise_sample_count < NOISE_BUFFER_SIZE) ?
                                       noise_sample_count : NOISE_BUFFER_SIZE;

                for (uint8_t i = 0; i < valid_samples; i++) {
                    noise_sum += noise_samples[i];
                }

                uint32_t new_baseline = noise_sum / valid_samples;

                // 平滑更新噪声基线，避免突变
                if (noise_initialized) {
                    noise_baseline = (noise_baseline * 3 + new_baseline) / 4; // 加权平均
                } else {
                    noise_baseline = new_baseline;
                    noise_initialized = 1;
                }

                last_noise_update = time_counter;

                printf("Noise baseline updated: %lu (samples: %d, quiet_count: %d)\r\n",
                       noise_baseline, valid_samples, quiet_sample_count);
                uart_flush();
            }
        }
    } else {
        // 非安静状态，重置连续安静计数
        quiet_sample_count = 0;
    }
}

/**
 * @brief 获取当前噪声基线
 * @return 噪声基线值
 */
uint32_t get_noise_baseline(void)
{
    return noise_baseline;
}

/**
 * @brief 获取自适应能量阈值
 * @return 自适应阈值
 */
uint32_t get_adaptive_energy_threshold(void)
{
    // 计算自适应阈值：噪声基线 * 噪声因子
    uint32_t adaptive_threshold = (uint32_t)(noise_baseline * NOISE_FACTOR);

    // 设置合理的上下限
    if (adaptive_threshold < 300) {
        adaptive_threshold = 300;   // 最小阈值
    } else if (adaptive_threshold > 3000) {
        adaptive_threshold = 3000;  // 最大阈值
    }

    return adaptive_threshold;
}

/**
 * @brief 动态阈值调整初始化
 */
void dynamic_threshold_init(void)
{
    // 初始化阈值为默认值
    current_energy_threshold = ENERGY_THRESHOLD;
    current_zcr_threshold = ZCR_THRESHOLD;

    // 初始化阈值历史记录
    for (uint8_t i = 0; i < 5; i++) {
        threshold_history[i] = ENERGY_THRESHOLD;
    }

    threshold_history_index = 0;
    threshold_initialized = 0;

    printf("Dynamic threshold initialized: Energy=%lu, ZCR=%d\r\n",
           current_energy_threshold, current_zcr_threshold);
    uart_flush();
}

/**
 * @brief 动态阈值更新函数
 * @param current_energy 当前能量值
 * @param current_zcr 当前零交叉率
 */
void dynamic_threshold_update(uint32_t current_energy, uint16_t current_zcr)
{
    static uint32_t last_threshold_update = 0;

    // 每5秒更新一次阈值
    if (time_counter - last_threshold_update >= 50) { // 50 * 100ms = 5秒

        // 基于噪声基线计算新的能量阈值
        uint32_t adaptive_energy = get_adaptive_energy_threshold();

        // 平滑更新能量阈值
        if (threshold_initialized) {
            current_energy_threshold = (uint32_t)(current_energy_threshold * THRESHOLD_SMOOTH_FACTOR +
                                                 adaptive_energy * (1.0f - THRESHOLD_SMOOTH_FACTOR));
        } else {
            current_energy_threshold = adaptive_energy;
            threshold_initialized = 1;
        }

        // 应用上下限保护
        if (current_energy_threshold < MIN_ENERGY_THRESHOLD) {
            current_energy_threshold = MIN_ENERGY_THRESHOLD;
        } else if (current_energy_threshold > MAX_ENERGY_THRESHOLD) {
            current_energy_threshold = MAX_ENERGY_THRESHOLD;
        }

        // 根据环境噪声调整零交叉率阈值
        uint32_t noise_level = get_noise_baseline();
        if (noise_level < 300) {
            // 安静环境，降低ZCR阈值提高敏感度
            current_zcr_threshold = BASE_ZCR_THRESHOLD - 20;
        } else if (noise_level > 800) {
            // 嘈杂环境，提高ZCR阈值减少误报
            current_zcr_threshold = BASE_ZCR_THRESHOLD + 30;
        } else {
            // 正常环境
            current_zcr_threshold = BASE_ZCR_THRESHOLD;
        }

        // 应用ZCR阈值上下限
        if (current_zcr_threshold < MIN_ZCR_THRESHOLD) {
            current_zcr_threshold = MIN_ZCR_THRESHOLD;
        } else if (current_zcr_threshold > MAX_ZCR_THRESHOLD) {
            current_zcr_threshold = MAX_ZCR_THRESHOLD;
        }

        // 记录阈值历史
        threshold_history[threshold_history_index] = current_energy_threshold;
        threshold_history_index = (threshold_history_index + 1) % 5;

        last_threshold_update = time_counter;

        printf("Threshold updated: Energy=%lu, ZCR=%d (Noise=%lu)\r\n",
               current_energy_threshold, current_zcr_threshold, noise_level);
        uart_flush();
    }
}

/**
 * @brief 获取当前能量阈值
 * @return 当前能量阈值
 */
uint32_t get_current_energy_threshold(void)
{
    return current_energy_threshold;
}

/**
 * @brief 获取当前零交叉率阈值
 * @return 当前零交叉率阈值
 */
uint16_t get_current_zcr_threshold(void)
{
    return current_zcr_threshold;
}

/**
 * @brief AGC初始化
 */
void agc_init(void)
{
    current_gain = 1.0f;
    smooth_gain = 1.0f;
    agc_initialized = 0;
    agc_update_counter = 0;

    printf("AGC initialized: Gain=%.2f\r\n", current_gain);
    uart_flush();
}

/**
 * @brief 计算RMS值
 * @param samples 音频样本数组
 * @param length 样本数量
 * @return RMS值
 */
float calculate_rms(int16_t* samples, uint16_t length)
{
    if (samples == NULL || length == 0) {
        return 0.0f;
    }

    float sum = 0.0f;
    for (uint16_t i = 0; i < length; i++) {
        float sample = (float)samples[i];
        sum += sample * sample;
    }

    return sqrtf(sum / length);
}

/**
 * @brief AGC处理函数
 * @param samples 音频样本数组
 * @param length 样本数量
 */
void agc_process(int16_t* samples, uint16_t length)
{
    if (samples == NULL || length == 0) {
        return;
    }

    // 计算当前帧的RMS值
    float current_rms = calculate_rms(samples, length);

    // 如果RMS值太小，可能是噪声，不进行AGC处理
    if (current_rms < MIN_RMS_THRESHOLD) {
        return;
    }

    // 计算目标增益
    float target_gain = TARGET_RMS / current_rms;

    // 限制增益范围
    if (target_gain < MIN_GAIN) {
        target_gain = MIN_GAIN;
    } else if (target_gain > MAX_GAIN) {
        target_gain = MAX_GAIN;
    }

    // 平滑增益更新
    if (agc_initialized) {
        smooth_gain = smooth_gain * AGC_SMOOTH_FACTOR + target_gain * (1.0f - AGC_SMOOTH_FACTOR);
    } else {
        smooth_gain = target_gain;
        agc_initialized = 1;
    }

    current_gain = smooth_gain;

    // 应用增益到音频样本
    for (uint16_t i = 0; i < length; i++) {
        float adjusted_sample = (float)samples[i] * current_gain;

        // 防止溢出
        if (adjusted_sample > 32767.0f) {
            adjusted_sample = 32767.0f;
        } else if (adjusted_sample < -32768.0f) {
            adjusted_sample = -32768.0f;
        }

        samples[i] = (int16_t)adjusted_sample;
    }

    // 每100次更新输出一次AGC状态
    agc_update_counter++;
    if (agc_update_counter >= 100) {
        printf("AGC: RMS=%.1f, Gain=%.2f\r\n", current_rms, current_gain);
        uart_flush();
        agc_update_counter = 0;
    }
}

/**
 * @brief 获取当前增益值
 * @return 当前增益值
 */
float get_current_gain(void)
{
    return current_gain;
}

/**
 * @brief 校准功能初始化
 */
void calibration_init(void)
{
    calibration_active = 0;
    calibration_samples = 0;
    calibration_energy_sum = 0;
    calibration_zcr_sum = 0;
    calibration_rms_sum = 0.0f;
    calibration_quiet_counter = 0;

    // 尝试从Flash加载校准数据
    if (calibration_load_from_flash()) {
        printf("Calibration data loaded from Flash\r\n");
    } else {
        printf("No valid calibration data, using defaults\r\n");
        calibration_reset_to_default();
    }
    uart_flush();
}

/**
 * @brief 开始校准过程
 */
void calibration_start(void)
{
    if (calibration_active) {
        printf("Calibration already in progress\r\n");
        uart_flush();
        return;
    }

    calibration_active = 1;
    calibration_samples = 0;
    calibration_energy_sum = 0;
    calibration_zcr_sum = 0;
    calibration_rms_sum = 0.0f;
    calibration_quiet_counter = 0;

    printf("=== Starting Calibration ===\r\n");
    printf("Please keep environment QUIET for 40 seconds...\r\n");
    printf("Calibration will start in 10 seconds\r\n");
    uart_flush();
}

/**
 * @brief 检查校准是否激活
 * @return 1:校准激活, 0:校准未激活
 */
uint8_t calibration_is_active(void)
{
    return calibration_active;
}

/**
 * @brief 校准数据处理
 * @param energy 当前能量值
 * @param zcr 当前零交叉率
 * @param rms 当前RMS值
 */
void calibration_process(uint32_t energy, uint16_t zcr, float rms)
{
    if (!calibration_active) {
        return;
    }

    // 前10秒为安静等待期
    if (calibration_quiet_counter < CALIBRATION_QUIET_TIME) {
        calibration_quiet_counter++;

        // 每秒提示一次
        if (calibration_quiet_counter % 10 == 0) {
            printf("Waiting... %d seconds remaining\r\n",
                   (CALIBRATION_QUIET_TIME - calibration_quiet_counter) / 10);
            uart_flush();
        }
        return;
    }

    // 开始收集校准数据
    if (calibration_samples == 0) {
        printf("Starting data collection...\r\n");
        uart_flush();
    }

    // 收集数据
    calibration_energy_sum += energy;
    calibration_zcr_sum += zcr;
    calibration_rms_sum += rms;
    calibration_samples++;

    // 每5秒报告一次进度
    if (calibration_samples % 50 == 0) {
        uint16_t progress = (calibration_samples * 100) / CALIBRATION_SAMPLE_COUNT;
        printf("Calibration progress: %d%% (%d/%d)\r\n",
               progress, calibration_samples, CALIBRATION_SAMPLE_COUNT);
        uart_flush();
    }

    // 校准完成
    if (calibration_samples >= CALIBRATION_SAMPLE_COUNT) {
        // 计算平均值
        uint32_t avg_energy = calibration_energy_sum / calibration_samples;
        uint16_t avg_zcr = calibration_zcr_sum / calibration_samples;
        float avg_rms = calibration_rms_sum / calibration_samples;

        // 计算优化的阈值
        current_calibration.magic = CALIBRATION_MAGIC;
        current_calibration.noise_baseline = avg_energy;
        current_calibration.energy_threshold = avg_energy * 3; // 3倍噪声基线
        current_calibration.zcr_threshold = avg_zcr + 40;      // ZCR基线+40
        current_calibration.agc_target_rms = avg_rms * 2.0f;   // 2倍RMS作为目标

        // 应用合理限制
        if (current_calibration.energy_threshold < MIN_ENERGY_THRESHOLD) {
            current_calibration.energy_threshold = MIN_ENERGY_THRESHOLD;
        } else if (current_calibration.energy_threshold > MAX_ENERGY_THRESHOLD) {
            current_calibration.energy_threshold = MAX_ENERGY_THRESHOLD;
        }

        if (current_calibration.zcr_threshold < MIN_ZCR_THRESHOLD) {
            current_calibration.zcr_threshold = MIN_ZCR_THRESHOLD;
        } else if (current_calibration.zcr_threshold > MAX_ZCR_THRESHOLD) {
            current_calibration.zcr_threshold = MAX_ZCR_THRESHOLD;
        }

        // 保存到Flash
        calibration_save_to_flash();

        printf("=== Calibration Completed ===\r\n");
        printf("Noise Baseline: %lu\r\n", current_calibration.noise_baseline);
        printf("Energy Threshold: %lu\r\n", current_calibration.energy_threshold);
        printf("ZCR Threshold: %d\r\n", current_calibration.zcr_threshold);
        printf("AGC Target RMS: %.1f\r\n", current_calibration.agc_target_rms);
        printf("Calibration saved to Flash\r\n");
        printf("===========================\r\n");
        uart_flush();

        calibration_active = 0;
    }
}

/**
 * @brief 从Flash加载校准数据
 * @return 1:加载成功, 0:加载失败
 */
uint8_t calibration_load_from_flash(void)
{
    // 简化实现：直接从Flash地址读取
    calibration_data_t* flash_data = (calibration_data_t*)CALIBRATION_FLASH_ADDR;

    // 检查魔数
    if (flash_data->magic != CALIBRATION_MAGIC) {
        return 0; // 无效数据
    }

    // 简单校验和验证（可以改进为CRC）
    uint32_t calculated_checksum = flash_data->magic + flash_data->noise_baseline +
                                   flash_data->energy_threshold + flash_data->zcr_threshold;

    if (calculated_checksum != flash_data->checksum) {
        return 0; // 校验失败
    }

    // 复制数据
    current_calibration = *flash_data;

    return 1; // 加载成功
}

/**
 * @brief 保存校准数据到Flash
 */
void calibration_save_to_flash(void)
{
    // 计算校验和
    current_calibration.checksum = current_calibration.magic +
                                   current_calibration.noise_baseline +
                                   current_calibration.energy_threshold +
                                   current_calibration.zcr_threshold;

    // 注意：这里简化实现，实际应用中需要Flash擦除和编程操作
    // 由于CH32V307的Flash操作比较复杂，这里仅做示例
    printf("Calibration data prepared for Flash storage\r\n");
    printf("(Flash programming not implemented in this demo)\r\n");
    uart_flush();
}

/**
 * @brief 重置为默认校准参数
 */
void calibration_reset_to_default(void)
{
    current_calibration.magic = CALIBRATION_MAGIC;
    current_calibration.noise_baseline = 500;
    current_calibration.energy_threshold = ENERGY_THRESHOLD;
    current_calibration.zcr_threshold = ZCR_THRESHOLD;
    current_calibration.agc_target_rms = TARGET_RMS;
    current_calibration.checksum = current_calibration.magic +
                                   current_calibration.noise_baseline +
                                   current_calibration.energy_threshold +
                                   current_calibration.zcr_threshold;

    printf("Calibration reset to default values\r\n");
    uart_flush();
}

/**
 * @brief 增强特征提取初始化
 */
void enhanced_features_init(void)
{
    // 初始化特征结构
    current_features.spectral_centroid = 0.0f;
    current_features.spectral_bandwidth = 0.0f;
    current_features.energy_variance = 0.0f;
    current_features.spectral_rolloff = 0.0f;
    current_features.energy = 0;
    current_features.zcr = 0;

    smooth_features = current_features;

    // 初始化能量历史
    for (uint8_t i = 0; i < ENERGY_VARIANCE_WINDOW; i++) {
        energy_history[i] = 0;
    }
    energy_history_index = 0;

    enhanced_features_initialized = 1;

    printf("Enhanced features initialized\r\n");
    uart_flush();
}

/**
 * @brief 计算频谱质心
 * @param magnitude FFT幅度谱
 * @param length 幅度谱长度
 * @return 频谱质心(Hz)
 */
float calculate_spectral_centroid(float* magnitude, uint16_t length)
{
    if (magnitude == NULL || length == 0) {
        return 0.0f;
    }

    float weighted_sum = 0.0f;
    float magnitude_sum = 0.0f;

    for (uint16_t i = 1; i < length; i++) { // 跳过DC分量
        float freq = (SAMPLE_RATE / 2.0f) * i / length;
        weighted_sum += freq * magnitude[i];
        magnitude_sum += magnitude[i];
    }

    return (magnitude_sum > 0.0f) ? (weighted_sum / magnitude_sum) : 0.0f;
}

/**
 * @brief 计算频谱带宽
 * @param magnitude FFT幅度谱
 * @param length 幅度谱长度
 * @param centroid 频谱质心
 * @return 频谱带宽(Hz)
 */
float calculate_spectral_bandwidth(float* magnitude, uint16_t length, float centroid)
{
    if (magnitude == NULL || length == 0) {
        return 0.0f;
    }

    float weighted_variance = 0.0f;
    float magnitude_sum = 0.0f;

    for (uint16_t i = 1; i < length; i++) { // 跳过DC分量
        float freq = (SAMPLE_RATE / 2.0f) * i / length;
        float freq_diff = freq - centroid;
        weighted_variance += freq_diff * freq_diff * magnitude[i];
        magnitude_sum += magnitude[i];
    }

    return (magnitude_sum > 0.0f) ? sqrtf(weighted_variance / magnitude_sum) : 0.0f;
}

/**
 * @brief 计算短时能量方差
 * @param current_energy 当前能量值
 * @return 能量方差
 */
float calculate_energy_variance(uint32_t current_energy)
{
    // 更新能量历史
    energy_history[energy_history_index] = current_energy;
    energy_history_index = (energy_history_index + 1) % ENERGY_VARIANCE_WINDOW;

    // 计算平均值
    uint32_t sum = 0;
    for (uint8_t i = 0; i < ENERGY_VARIANCE_WINDOW; i++) {
        sum += energy_history[i];
    }
    float mean = (float)sum / ENERGY_VARIANCE_WINDOW;

    // 计算方差
    float variance = 0.0f;
    for (uint8_t i = 0; i < ENERGY_VARIANCE_WINDOW; i++) {
        float diff = (float)energy_history[i] - mean;
        variance += diff * diff;
    }

    return variance / ENERGY_VARIANCE_WINDOW;
}

/**
 * @brief 计算频谱滚降点
 * @param magnitude FFT幅度谱
 * @param length 幅度谱长度
 * @param threshold 滚降阈值(0.85表示85%能量)
 * @return 频谱滚降点(Hz)
 */
float calculate_spectral_rolloff(float* magnitude, uint16_t length, float threshold)
{
    if (magnitude == NULL || length == 0) {
        return 0.0f;
    }

    // 计算总能量
    float total_energy = 0.0f;
    for (uint16_t i = 1; i < length; i++) {
        total_energy += magnitude[i] * magnitude[i];
    }

    if (total_energy == 0.0f) {
        return 0.0f;
    }

    // 找到滚降点
    float cumulative_energy = 0.0f;
    float target_energy = total_energy * threshold;

    for (uint16_t i = 1; i < length; i++) {
        cumulative_energy += magnitude[i] * magnitude[i];
        if (cumulative_energy >= target_energy) {
            return (SAMPLE_RATE / 2.0f) * i / length;
        }
    }

    return (SAMPLE_RATE / 2.0f); // 返回最大频率
}

/**
 * @brief 提取增强音频特征
 * @param fft_magnitude FFT幅度谱
 * @param audio_data 音频数据
 * @param features 输出特征结构
 */
void extract_enhanced_features(float* fft_magnitude, int16_t* audio_data, audio_features_t* features)
{
    if (fft_magnitude == NULL || audio_data == NULL || features == NULL) {
        return;
    }

    // 计算传统特征(能量和零交叉率)
    uint32_t energy_sum = 0;
    uint16_t zcr_count = 0;
    int16_t last_sample = 0;

    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        int16_t sample = audio_data[i];
        energy_sum += (uint32_t)(sample * sample);

        // 计算零交叉率
        if ((sample > 0 && last_sample < 0) || (sample < 0 && last_sample > 0)) {
            zcr_count++;
        }
        last_sample = sample;
    }

    features->energy = energy_sum / SAMPLE_SIZE;
    features->zcr = zcr_count;

    // 计算频谱特征
    features->spectral_centroid = calculate_spectral_centroid(fft_magnitude, SAMPLE_SIZE/2);
    features->spectral_bandwidth = calculate_spectral_bandwidth(fft_magnitude, SAMPLE_SIZE/2,
                                                               features->spectral_centroid);
    features->spectral_rolloff = calculate_spectral_rolloff(fft_magnitude, SAMPLE_SIZE/2, 0.85f);

    // 计算能量方差
    features->energy_variance = calculate_energy_variance(features->energy);

    // 特征平滑处理
    if (enhanced_features_initialized) {
        smooth_features.spectral_centroid = smooth_features.spectral_centroid * FEATURE_SMOOTH_FACTOR +
                                           features->spectral_centroid * (1.0f - FEATURE_SMOOTH_FACTOR);
        smooth_features.spectral_bandwidth = smooth_features.spectral_bandwidth * FEATURE_SMOOTH_FACTOR +
                                            features->spectral_bandwidth * (1.0f - FEATURE_SMOOTH_FACTOR);
        smooth_features.spectral_rolloff = smooth_features.spectral_rolloff * FEATURE_SMOOTH_FACTOR +
                                          features->spectral_rolloff * (1.0f - FEATURE_SMOOTH_FACTOR);
        smooth_features.energy_variance = smooth_features.energy_variance * FEATURE_SMOOTH_FACTOR +
                                         features->energy_variance * (1.0f - FEATURE_SMOOTH_FACTOR);
        smooth_features.energy = features->energy; // 能量不平滑
        smooth_features.zcr = features->zcr;       // ZCR不平滑
    } else {
        smooth_features = *features;
    }
}

/**
 * @brief 基于增强特征的哭声分类
 * @param features 音频特征
 * @return 分类结果: 0=无哭声, 1=饥饿, 2=困倦, 3=生病, 4=无聊
 */
uint8_t classify_cry_with_enhanced_features(audio_features_t* features)
{
    if (features == NULL) {
        return 0;
    }

    // 首先检查是否为哭声(基于传统特征)
    uint32_t current_energy_th = get_current_energy_threshold();
    uint16_t current_zcr_th = get_current_zcr_threshold();

    if (features->energy < current_energy_th || features->zcr < current_zcr_th) {
        return 0; // 不是哭声
    }

    // 基于增强特征进行细分类
    float centroid = features->spectral_centroid;
    float bandwidth = features->spectral_bandwidth;
    float rolloff = features->spectral_rolloff;
    float variance = features->energy_variance;

    // 饥饿哭声特征: 高频谱质心, 中等带宽, 高能量方差
    if (centroid > 800.0f && bandwidth > 400.0f && bandwidth < 800.0f && variance > 50000.0f) {
        return 1; // 饥饿
    }

    // 困倦哭声特征: 低频谱质心, 窄带宽, 低能量方差
    if (centroid < 600.0f && bandwidth < 400.0f && variance < 20000.0f) {
        return 2; // 困倦
    }

    // 生病哭声特征: 中等频谱质心, 宽带宽, 高滚降点
    if (centroid > 600.0f && centroid < 900.0f && bandwidth > 600.0f && rolloff > 1500.0f) {
        return 3; // 生病
    }

    // 无聊哭声特征: 变化较大的特征, 中等能量方差
    if (variance > 20000.0f && variance < 50000.0f && bandwidth > 300.0f) {
        return 4; // 无聊
    }

    // 默认返回一般哭声
    return 1; // 默认为饥饿
}

/**
 * @brief 置信度评估初始化
 */
void confidence_init(void)
{
    // 初始化置信度历史
    for (uint8_t i = 0; i < CONFIDENCE_HISTORY_SIZE; i++) {
        confidence_history[i] = 0.0f;
    }
    confidence_history_index = 0;
    confidence_history_count = 0;
    smooth_confidence = 0.0f;

    // 初始化上次结果
    last_confidence_result.overall_confidence = 0.0f;
    last_confidence_result.cry_type = 0;
    last_confidence_result.is_high_confidence = 0;
    last_confidence_result.timestamp = 0;

    confidence_initialized = 1;

    printf("Confidence evaluation initialized\r\n");
    uart_flush();
}

/**
 * @brief 评估单个特征的置信度
 * @param feature_value 特征值
 * @param feature_type 特征类型(0-5)
 * @param cry_type 哭声类型
 * @return 特征置信度(0-1)
 */
float evaluate_feature_confidence(float feature_value, uint8_t feature_type, uint8_t cry_type)
{
    float confidence = 0.0f;

    switch(feature_type) {
        case 0: // 频谱质心
            if (cry_type == 1) { // 饥饿
                confidence = (feature_value > 800.0f) ? 0.9f : 0.3f;
            } else if (cry_type == 2) { // 困倦
                confidence = (feature_value < 600.0f) ? 0.9f : 0.3f;
            } else if (cry_type == 3) { // 生病
                confidence = (feature_value > 600.0f && feature_value < 900.0f) ? 0.8f : 0.4f;
            } else {
                confidence = 0.5f;
            }
            break;

        case 1: // 频谱带宽
            if (cry_type == 1) { // 饥饿
                confidence = (feature_value > 400.0f && feature_value < 800.0f) ? 0.8f : 0.4f;
            } else if (cry_type == 2) { // 困倦
                confidence = (feature_value < 400.0f) ? 0.9f : 0.3f;
            } else if (cry_type == 3) { // 生病
                confidence = (feature_value > 600.0f) ? 0.8f : 0.4f;
            } else {
                confidence = 0.5f;
            }
            break;

        case 2: // 能量方差
            if (cry_type == 1) { // 饥饿
                confidence = (feature_value > 50000.0f) ? 0.8f : 0.4f;
            } else if (cry_type == 2) { // 困倦
                confidence = (feature_value < 20000.0f) ? 0.8f : 0.4f;
            } else {
                confidence = (feature_value > 20000.0f) ? 0.6f : 0.4f;
            }
            break;

        case 3: // 频谱滚降
            if (cry_type == 3) { // 生病
                confidence = (feature_value > 1500.0f) ? 0.8f : 0.4f;
            } else {
                confidence = 0.5f;
            }
            break;

        case 4: // 能量
            confidence = 0.7f; // 能量特征基础置信度
            break;

        case 5: // 零交叉率
            confidence = 0.6f; // ZCR特征基础置信度
            break;

        default:
            confidence = 0.5f;
            break;
    }

    return confidence;
}

/**
 * @brief 计算总体置信度
 * @param features 音频特征
 * @param cry_type 哭声类型
 * @return 总体置信度(0-100)
 */
float calculate_confidence(audio_features_t* features, uint8_t cry_type)
{
    if (features == NULL) {
        return 0.0f;
    }

    float feature_values[6] = {
        features->spectral_centroid,
        features->spectral_bandwidth,
        features->energy_variance,
        features->spectral_rolloff,
        (float)features->energy,
        (float)features->zcr
    };

    float total_confidence = 0.0f;

    // 计算加权置信度
    for (uint8_t i = 0; i < 6; i++) {
        float feature_conf = evaluate_feature_confidence(feature_values[i], i, cry_type);
        total_confidence += feature_conf * feature_weights[i];
    }

    // 归一化到0-100范围
    total_confidence *= 100.0f;

    // 限制范围
    if (total_confidence > 100.0f) {
        total_confidence = 100.0f;
    } else if (total_confidence < 0.0f) {
        total_confidence = 0.0f;
    }

    return total_confidence;
}

/**
 * @brief 更新置信度历史
 * @param confidence 当前置信度
 */
void update_confidence_history(float confidence)
{
    confidence_history[confidence_history_index] = confidence;
    confidence_history_index = (confidence_history_index + 1) % CONFIDENCE_HISTORY_SIZE;

    if (confidence_history_count < CONFIDENCE_HISTORY_SIZE) {
        confidence_history_count++;
    }

    // 更新平滑置信度
    if (confidence_initialized) {
        smooth_confidence = smooth_confidence * CONFIDENCE_SMOOTH_FACTOR +
                           confidence * (1.0f - CONFIDENCE_SMOOTH_FACTOR);
    } else {
        smooth_confidence = confidence;
    }
}

/**
 * @brief 获取平均置信度
 * @return 平均置信度
 */
float get_average_confidence(void)
{
    if (confidence_history_count == 0) {
        return 0.0f;
    }

    float sum = 0.0f;
    for (uint8_t i = 0; i < confidence_history_count; i++) {
        sum += confidence_history[i];
    }

    return sum / confidence_history_count;
}

/**
 * @brief 判断是否为高置信度结果
 * @param confidence 置信度值
 * @return 1:高置信度, 0:低置信度
 */
uint8_t is_high_confidence_result(float confidence)
{
    return (confidence >= CONFIDENCE_THRESHOLD) ? 1 : 0;
}

/**
 * @brief 处理带置信度的分类结果
 * @param cry_type 哭声类型
 * @param confidence 置信度
 */
void process_classification_with_confidence(uint8_t cry_type, float confidence)
{
    // 更新置信度历史
    update_confidence_history(confidence);

    // 保存结果
    last_confidence_result.overall_confidence = confidence;
    last_confidence_result.cry_type = cry_type;
    last_confidence_result.is_high_confidence = is_high_confidence_result(confidence);
    last_confidence_result.timestamp = 0; // 可以添加实际时间戳

    // 获取哭声类型名称
    const char* type_names[] = {"None", "Hungry", "Sleepy", "Sick", "Bored"};
    const char* type_name = (cry_type < 5) ? type_names[cry_type] : "Unknown";

    if (last_confidence_result.is_high_confidence) {
        // 高置信度，执行相应动作
        printf(">>> HIGH CONFIDENCE: %s Cry (%.1f%%) <<<\r\n", type_name, confidence);

        // 根据类型执行动作
        switch(cry_type) {
            case 1: // 饥饿
                uart8_send_string((uint8_t*)"bb", 2);
                printf("UART8: 'bb' sent (Hungry cry detected)\r\n");
                break;

            case 2: // 困倦
                uart8_send_string((uint8_t*)"ss", 2);
                printf("UART8: 'ss' sent (Sleepy cry detected)\r\n");
                break;

            case 3: // 生病/生气
                uart8_send_string((uint8_t*)"aa", 2);
                uart6_send_string(string9, strlen((char*)string9));
                printf("UART8: 'aa' sent, UART6: MQTT message sent (Sick cry detected)\r\n");
                break;

            case 4: // 无聊
                uart8_send_string((uint8_t*)"bb", 2);
                printf("UART8: 'bb' sent (Bored cry detected)\r\n");
                break;

            default:
                break;
        }

    } else {
        // 低置信度，仅记录但不触发动作
        printf(">>> LOW CONFIDENCE: %s Cry (%.1f%%) - No action taken <<<\r\n",
               type_name, confidence);
    }

    // 显示置信度统计
    float avg_confidence = get_average_confidence();
    printf("Confidence Stats: Current=%.1f%%, Smooth=%.1f%%, Average=%.1f%%\r\n",
           confidence, smooth_confidence, avg_confidence);

    uart_flush();
}

/**
 * @brief 调试功能初始化
 */
void debug_init(void)
{
    // 初始化调试状态
    debug_state.debug_enabled = 1;
    debug_state.real_time_monitor = 0;
    debug_state.feature_display = 0;
    debug_state.performance_monitor = 0;
    debug_state.debug_output_counter = 0;
    memset(debug_state.last_command, 0, MAX_DEBUG_COMMAND_LEN);

    // 初始化调试缓冲区
    memset(debug_command_buffer, 0, DEBUG_BUFFER_SIZE);
    debug_command_index = 0;
    debug_last_output_time = 0;

    printf("Debug system initialized\r\n");
    printf("Type 'HELP' for available commands\r\n");
    uart_flush();
}

/**
 * @brief 性能统计初始化
 */
void performance_stats_init(void)
{
    perf_stats.total_detections = 0;
    perf_stats.cry_detections = 0;
    perf_stats.high_confidence_count = 0;
    perf_stats.low_confidence_count = 0;
    perf_stats.false_positives = 0;
    perf_stats.processing_time_us = 0;
    perf_stats.average_confidence = 0.0f;
    perf_stats.last_update_time = 0;

    printf("Performance statistics initialized\r\n");
    uart_flush();
}

/**
 * @brief 获取当前RMS值
 * @return 当前RMS值
 */
float get_current_rms(void)
{
    // 这里应该返回最近计算的RMS值
    // 简化实现，返回基于当前增益的估算值
    return TARGET_RMS / current_gain;
}

/**
 * @brief 打印系统状态信息
 */
void debug_print_system_status(void)
{
    printf("\r\n=== 系统状态信息 ===\r\n");

    // 噪声和阈值信息
    printf("噪声基线: %lu\r\n", get_noise_baseline());
    printf("动态能量阈值: %lu\r\n", get_current_energy_threshold());
    printf("动态ZCR阈值: %u\r\n", get_current_zcr_threshold());

    // AGC信息
    printf("当前增益: %.2f\r\n", get_current_gain());
    printf("RMS能量: %.1f\r\n", get_current_rms());

    // 置信度信息
    printf("平均置信度: %.1f%%\r\n", get_average_confidence());
    printf("置信度阈值: %.1f%%\r\n", CONFIDENCE_THRESHOLD);

    // 系统配置
    printf("采样率: %d Hz\r\n", SAMPLE_RATE);
    printf("样本大小: %d\r\n", SAMPLE_SIZE);
    printf("能量阈值: %lu\r\n", ENERGY_THRESHOLD);
    printf("ZCR阈值: %u\r\n", ZCR_THRESHOLD);

    printf("==================\r\n\r\n");
    uart_flush();
}

/**
 * @brief 打印增强特征信息
 * @param features 音频特征
 */
void debug_print_features(audio_features_t* features)
{
    if (features == NULL) {
        printf("Features: NULL\r\n");
        return;
    }

    printf("\r\n=== 增强特征信息 ===\r\n");
    printf("频谱质心: %.1f Hz\r\n", features->spectral_centroid);
    printf("频谱带宽: %.1f Hz\r\n", features->spectral_bandwidth);
    printf("频谱滚降: %.1f Hz\r\n", features->spectral_rolloff);
    printf("能量方差: %.0f\r\n", features->energy_variance);
    printf("能量: %lu\r\n", features->energy);
    printf("零交叉率: %u\r\n", features->zcr);
    printf("==================\r\n\r\n");
    uart_flush();
}

/**
 * @brief 打印性能统计信息
 */
void debug_print_performance_stats(void)
{
    printf("\r\n=== 性能统计信息 ===\r\n");
    printf("总检测次数: %lu\r\n", perf_stats.total_detections);
    printf("哭声检测次数: %lu\r\n", perf_stats.cry_detections);
    printf("高置信度检测: %lu\r\n", perf_stats.high_confidence_count);
    printf("低置信度检测: %lu\r\n", perf_stats.low_confidence_count);
    printf("用户标记误报: %lu\r\n", perf_stats.false_positives);
    printf("平均置信度: %.1f%%\r\n", perf_stats.average_confidence);
    printf("处理时间: %lu us\r\n", perf_stats.processing_time_us);

    // 计算检测率
    if (perf_stats.total_detections > 0) {
        float detection_rate = (float)perf_stats.cry_detections * 100.0f / perf_stats.total_detections;
        printf("哭声检测率: %.1f%%\r\n", detection_rate);

        if (perf_stats.cry_detections > 0) {
            float confidence_rate = (float)perf_stats.high_confidence_count * 100.0f / perf_stats.cry_detections;
            printf("高置信度率: %.1f%%\r\n", confidence_rate);
        }
    }

    printf("==================\r\n\r\n");
    uart_flush();
}

/**
 * @brief 打印帮助信息
 */
void debug_print_help(void)
{
    printf("\r\n=== 调试命令帮助 ===\r\n");
    printf("STATUS - 显示系统状态\r\n");
    printf("FEATURES - 显示当前特征\r\n");
    printf("STATS - 显示性能统计\r\n");
    printf("MONITOR ON/OFF - 开启/关闭实时监控\r\n");
    printf("DISPLAY ON/OFF - 开启/关闭特征显示\r\n");
    printf("PERFMON ON/OFF - 开启/关闭性能监控\r\n");
    printf("SET_ENERGY <value> - 设置能量阈值\r\n");
    printf("SET_ZCR <value> - 设置ZCR阈值\r\n");
    printf("SET_CONF <value> - 设置置信度阈值\r\n");
    printf("CALIBRATE - 开始环境校准\r\n");
    printf("RESET_STATS - 重置性能统计\r\n");
    printf("FALSE_POS - 标记上次检测为误报\r\n");
    printf("HELP - 显示此帮助信息\r\n");
    printf("==================\r\n\r\n");
    uart_flush();
}
