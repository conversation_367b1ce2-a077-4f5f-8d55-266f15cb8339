/*
 * cry_detect.c
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#include "cry_detect.h"
#include "ch32v30x.h"
#include "kiss_fft.h"
#include <math.h>
#include "uart.h"
#include "audio.h"

/* 特征历史缓冲区结构 */
typedef struct {
    float spectral_centroid[CRY_HISTORY_FRAMES];    // 频谱重心历史
    float energy_level[CRY_HISTORY_FRAMES];         // 能量水平历史
    uint8_t frame_index;                            // 当前帧索引
    uint8_t frames_filled;                          // 已填充帧数
} cry_history_t;

/* 全局变量 */
static uint16_t audio_buffer[CRY_BUFFER_SIZE];      // 音频采样缓冲区
static volatile uint8_t buffer_ready = 0;           // 缓冲区就绪标志
static volatile uint16_t buffer_index = 0;          // 当前缓冲区索引
static cry_config_t cry_config;                     // 配置参数
cry_status_t cry_status;                            // 系统状态(全局可访问)
static cry_feature_t cry_feature;                   // 特征数据
static cry_history_t cry_history;                   // 特征历史缓冲区

/* 哭声类型名称数组 */
static const char* cry_type_names[] = {
    "UNKNOWN",      // CRY_TYPE_UNKNOWN = 0
    "HUNGRY",       // CRY_TYPE_HUNGRY = 1
    "SICK",         // CRY_TYPE_SICK = 2
    "SLEEPY",       // CRY_TYPE_SLEEPY = 3
    "BORED"         // CRY_TYPE_BORED = 4
};

/* FFT相关变量 */
static kiss_fft_cfg fft_cfg;                        // FFT配置
static kiss_fft_cpx fft_input[CRY_FFT_SIZE];        // FFT输入缓冲区
static kiss_fft_cpx fft_output[CRY_FFT_SIZE];       // FFT输出缓冲区
static float magnitude[CRY_FFT_SIZE/2];             // 幅度谱缓冲区

/* 外部变量声明 */
extern unsigned long int uwtick;                    // 系统时钟
extern u8 string9[];                                // MQTT消息模板

/* MQTT消息模板 */
static u8 cry_mqtt_msg[] = "AT+MQTTPUB=0,\"$sys/W2PeVM9f4z/ch32/thing/property/post\",\"{\\\"id\\\":\\\"123\\\"\\,\\\"params\\\":{\\\"cry\\\":{\\\"value\\\":1\\}}}\",0,0\r\n";
static u8 cry_stop_mqtt_msg[] = "AT+MQTTPUB=0,\"$sys/W2PeVM9f4z/ch32/thing/property/post\",\"{\\\"id\\\":\\\"124\\\"\\,\\\"params\\\":{\\\"cry\\\":{\\\"value\\\":0\\}}}\",0,0\r\n";

/* 静态函数声明 */
static uint8_t cry_fft_process(uint16_t* samples, uint16_t sample_count);
static uint8_t cry_extract_features(float* magnitude_data);
static uint8_t cry_detect_decision(void);
static void cry_handle_detection_result(uint8_t current_status);
static float cry_calculate_zero_crossing_rate(uint16_t* samples, uint16_t sample_count);
static void cry_update_history(cry_history_t* history, cry_feature_t* current);
static float cry_calculate_energy_trend(cry_history_t* history);
static float cry_calculate_feature_variance(float* data, uint8_t count);
static uint8_t cry_classify_type(cry_feature_t* features, cry_history_t* history);
static uint8_t cry_calculate_confidence(cry_feature_t* features, cry_history_t* history);
static void cry_print_classification_result(cry_feature_t* features);
static void cry_get_classification_stats(uint8_t* total_count, uint8_t* last_confidence);
static void cry_print_features_debug_internal(cry_feature_t* features);
static void cry_print_history_debug_internal(cry_history_t* history);

/*********************************************************************
 * @fn      cry_adc_gpio_init
 *
 * @brief   初始化ADC GPIO引脚(PA1)
 *
 * @return  none
 */
static void cry_adc_gpio_init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);   // 使能GPIOA时钟
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;               // PA1引脚
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;           // 模拟输入模式
    GPIO_Init(GPIOA, &GPIO_InitStructure);
}

/*********************************************************************
 * @fn      cry_adc_init
 *
 * @brief   初始化ADC1用于音频采集(基于CH32V307官方代码)
 *
 * @return  none
 */
static void cry_adc_init(void)
{
    ADC_InitTypeDef ADC_InitStructure = {0};
    u16 Calibrattion_Val = 0; // 校准值

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);    // 使能ADC1时钟
    RCC_ADCCLKConfig(RCC_PCLK2_Div8);                       // ADC时钟分频(96MHz/8=12MHz)

    ADC_DeInit(ADC1);
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;       // 独立模式
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;           // 单通道模式
    ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;      // 连续转换模式
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None; // 软件触发
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;  // 右对齐
    ADC_InitStructure.ADC_NbrOfChannel = 1;                 // 1个通道
    ADC_Init(ADC1, &ADC_InitStructure);

    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_7Cycles5); // 配置PA1为ADC1_CH1
    ADC_DMACmd(ADC1, ENABLE);                               // 使能ADC DMA
    ADC_Cmd(ADC1, ENABLE);                                  // 使能ADC1

    // ADC校准(基于官方代码)
    ADC_BufferCmd(ADC1, DISABLE);                           // 禁用buffer
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    Calibrattion_Val = Get_CalibrationValue(ADC1);          // 获取校准值
}

/*********************************************************************
 * @fn      cry_dma_init
 *
 * @brief   初始化DMA1_Channel1用于ADC数据传输(基于CH32V307官方代码)
 *
 * @return  none
 */
static void cry_dma_init(void)
{
    DMA_InitTypeDef DMA_InitStructure = {0};
    NVIC_InitTypeDef NVIC_InitStructure = {0};

    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);      // 使能DMA1时钟

    DMA_DeInit(DMA1_Channel1);
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC1->RDATAR;     // ADC数据寄存器地址
    DMA_InitStructure.DMA_MemoryBaseAddr = (uint32_t)audio_buffer;          // 内存缓冲区地址
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;                      // 外设到内存
    DMA_InitStructure.DMA_BufferSize = CRY_BUFFER_SIZE;                     // 缓冲区大小
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;        // 外设地址不递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 // 内存地址递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord; // 16位数据
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;     // 16位数据
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;                         // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;                 // 极高优先级(基于官方代码)
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;                           // 非内存到内存
    DMA_Init(DMA1_Channel1, &DMA_InitStructure);

    // 配置DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    DMA_ITConfig(DMA1_Channel1, DMA_IT_TC | DMA_IT_HT, ENABLE);             // 使能传输完成和半传输中断
    DMA_Cmd(DMA1_Channel1, ENABLE);                                         // 使能DMA通道
}

/*********************************************************************
 * @fn      cry_timer_init
 *
 * @brief   初始化定时器用于8kHz采样率控制
 *
 * @return  none
 */
static void cry_timer_init(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure = {0};
    
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4, ENABLE);    // 使能TIM4时钟
    
    // 配置TIM4用于ADC触发，8kHz采样率
    // 96MHz / (11999+1) / (1+1) = 8kHz
    TIM_TimeBaseStructure.TIM_Period = 1;                   // 自动重装载值
    TIM_TimeBaseStructure.TIM_Prescaler = 11999;            // 预分频器
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM4, &TIM_TimeBaseStructure);
    
    TIM_SelectOutputTrigger(TIM4, TIM_TRGOSource_Update);   // 更新事件作为触发源
    TIM_Cmd(TIM4, ENABLE);                                  // 使能定时器
}

/*********************************************************************
 * @fn      cry_detect_init
 *
 * @brief   初始化哭声检测模块
 *
 * @return  none
 */
void cry_detect_init(void)
{
    // 加载默认配置参数
    cry_load_default_config();
    
    // 初始化状态
    cry_status.initialized = 0;
    cry_status.audio_ready = 0;
    cry_status.fft_busy = 0;
    cry_status.last_send_time = 0;
    
    // 初始化特征数据
    cry_feature.base_freq_ratio = 0.0f;
    cry_feature.high_freq_ratio = 0.0f;
    cry_feature.spectral_centroid = 0.0f;
    cry_feature.spectral_rolloff = 0.0f;
    cry_feature.energy_variance = 0.0f;
    cry_feature.zero_crossing_rate = 0.0f;
    cry_feature.cry_detected = 0;
    cry_feature.detect_count = 0;
    cry_feature.cry_type = CRY_TYPE_UNKNOWN;
    cry_feature.confidence = 0;

    // 初始化历史缓冲区
    cry_history.frame_index = 0;
    cry_history.frames_filled = 0;
    for (uint8_t i = 0; i < CRY_HISTORY_FRAMES; i++) {
        cry_history.spectral_centroid[i] = 0.0f;
        cry_history.energy_level[i] = 0.0f;
    }
    
    // 初始化硬件
    cry_adc_gpio_init();    // 初始化GPIO
    cry_dma_init();         // 初始化DMA
    cry_adc_init();         // 初始化ADC
    cry_timer_init();       // 初始化定时器
    
    // 初始化FFT配置
    fft_cfg = kiss_fft_alloc(CRY_FFT_SIZE, 0, NULL, NULL);
    if (fft_cfg == NULL) {
        // FFT初始化失败
        return;
    }

    // 启动ADC转换
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);

    cry_status.initialized = 1;
}

/*********************************************************************
 * @fn      cry_detect_proc
 *
 * @brief   哭声检测主处理函数
 *
 * @return  none
 */
void cry_detect_proc(void)
{
    if (!cry_config.enable || !cry_status.initialized) {
        return;
    }

    // 检查音频数据是否就绪
    if (cry_status.audio_ready) {
        uint32_t start_time = uwtick;   // 性能监控开始时间
        cry_status.audio_ready = 0;

        // 执行FFT分析
        if (cry_fft_process(audio_buffer, CRY_FFT_SIZE) == 0) {
            // FFT处理成功，执行特征提取(包含分类)
            if (cry_extract_features(magnitude) == 0) {
                // 特征提取成功，执行哭声检测判决
                uint8_t detection_result = cry_detect_decision();
                // 处理检测结果，执行通信和播放
                cry_handle_detection_result(detection_result);

                // 性能监控：检查处理时间
                uint32_t process_time = uwtick - start_time;
                if (cry_config.debug_mode && process_time > 30) {
                    printf("Warning: Process time %dms exceeds 30ms limit\r\n", process_time);
                }
            }
        }
    }
}

/*********************************************************************
 * @fn      cry_get_status
 *
 * @brief   获取哭声检测状态
 *
 * @return  检测状态(0:未检测到, 1:检测到哭声)
 */
uint8_t cry_get_status(void)
{
    return cry_feature.cry_detected;
}

/*********************************************************************
 * @fn      cry_set_config
 *
 * @brief   设置配置参数
 *
 * @param   config - 配置参数指针
 *
 * @return  none
 */
void cry_set_config(cry_config_t* config)
{
    if (cry_validate_config(config) == 0) {
        cry_config = *config;
    }
}

/*********************************************************************
 * @fn      cry_get_config
 *
 * @brief   获取配置参数
 *
 * @return  配置参数指针
 */
cry_config_t* cry_get_config(void)
{
    return &cry_config;
}

/*********************************************************************
 * @fn      cry_reset_status
 *
 * @brief   重置检测状态
 *
 * @return  none
 */
void cry_reset_status(void)
{
    cry_feature.cry_detected = 0;
    cry_feature.detect_count = 0;
    cry_status.audio_ready = 0;
}

/*********************************************************************
 * @fn      cry_enable
 *
 * @brief   使能/禁用检测功能
 *
 * @param   enable - 使能标志(0:禁用, 1:使能)
 *
 * @return  none
 */
void cry_enable(uint8_t enable)
{
    cry_config.enable = enable;
    if (!enable) {
        cry_reset_status();
    }
}

/*********************************************************************
 * @fn      cry_fft_process
 *
 * @brief   执行FFT分析处理
 *
 * @param   samples - 音频采样数据指针
 * @param   sample_count - 采样数据数量
 *
 * @return  0:成功, 1:失败
 */
static uint8_t cry_fft_process(uint16_t* samples, uint16_t sample_count)
{
    uint16_t i;

    if (sample_count < CRY_FFT_SIZE) {
        return 1;   // 数据不足
    }

    cry_status.fft_busy = 1;

    // 准备FFT输入数据：将ADC采样值转换为复数格式
    for (i = 0; i < CRY_FFT_SIZE; i++) {
        // ADC采样值范围0-4095，转换为-2048到2047的有符号数
        fft_input[i].r = (float)(samples[i] - 2048);
        fft_input[i].i = 0.0f;  // 虚部为0
    }

    // 执行FFT变换
    kiss_fft(fft_cfg, fft_input, fft_output);

    // 计算幅度谱：sqrt(real^2 + imag^2)
    for (i = 0; i < CRY_FFT_SIZE/2; i++) {
        magnitude[i] = sqrtf(fft_output[i].r * fft_output[i].r +
                            fft_output[i].i * fft_output[i].i);
    }

    cry_status.fft_busy = 0;
    return 0;   // 成功
}

/*********************************************************************
 * @fn      cry_extract_features
 *
 * @brief   提取婴儿哭声特征
 *
 * @param   magnitude_data - 幅度谱数据
 *
 * @return  0:成功, 1:失败
 */
static uint8_t cry_extract_features(float* magnitude_data)
{
    uint16_t i;
    float base_energy = 0.0f;       // 基频段能量
    float high_energy = 0.0f;       // 高频段能量
    float total_energy = 0.0f;      // 总能量
    float weighted_sum = 0.0f;      // 频谱重心计算用
    float cumulative_energy = 0.0f; // 频谱滚降点计算用

    // 计算各频段能量和频谱重心
    for (i = 0; i < CRY_FFT_SIZE/2; i++) {
        total_energy += magnitude_data[i];

        // 频谱重心计算(跳过DC分量)
        if (i > 0) {
            float freq = i * CRY_FREQ_RESOLUTION;
            weighted_sum += freq * magnitude_data[i];
        }

        // 基频段：300-600Hz (bin 19-38)
        if (i >= CRY_BASE_FREQ_MIN_BIN && i <= CRY_BASE_FREQ_MAX_BIN) {
            base_energy += magnitude_data[i];
        }

        // 高频段：1-3kHz (bin 64-192)
        if (i >= CRY_HIGH_FREQ_MIN_BIN && i <= CRY_HIGH_FREQ_MAX_BIN) {
            high_energy += magnitude_data[i];
        }
    }

    // 避免除零错误
    if (total_energy < 1.0f) {
        cry_feature.base_freq_ratio = 0.0f;
        cry_feature.high_freq_ratio = 0.0f;
        cry_feature.spectral_centroid = 0.0f;
        cry_feature.spectral_rolloff = 0.0f;
        cry_feature.energy_variance = 0.0f;
        cry_feature.zero_crossing_rate = 0.0f;
        return 1;
    }

    // 计算能量比
    cry_feature.base_freq_ratio = base_energy / total_energy;
    cry_feature.high_freq_ratio = high_energy / total_energy;

    // 计算频谱重心
    cry_feature.spectral_centroid = weighted_sum / total_energy;

    // 计算频谱滚降点(85%能量点)
    float energy_85 = total_energy * 0.85f;
    cumulative_energy = 0.0f;
    cry_feature.spectral_rolloff = 0.0f;
    for (i = 1; i < CRY_FFT_SIZE/2; i++) {
        cumulative_energy += magnitude_data[i];
        if (cumulative_energy >= energy_85) {
            cry_feature.spectral_rolloff = i * CRY_FREQ_RESOLUTION;
            break;
        }
    }

    // 计算零交叉率
    cry_feature.zero_crossing_rate = cry_calculate_zero_crossing_rate(audio_buffer, CRY_FFT_SIZE);

    // 更新历史缓冲区
    cry_update_history(&cry_history, &cry_feature);

    // 计算能量方差(基于历史数据)
    cry_feature.energy_variance = cry_calculate_feature_variance(cry_history.energy_level, cry_history.frames_filled);

    // 执行哭声分类
    cry_classify_type(&cry_feature, &cry_history);

    // 计算分类置信度
    cry_feature.confidence = cry_calculate_confidence(&cry_feature, &cry_history);

    // 调试输出(仅在调试模式下)
    if (cry_config.debug_mode) {
        static uint8_t debug_counter = 0;
        debug_counter++;
        // 每10次输出一次详细调试信息
        if (debug_counter % 10 == 0) {
            cry_print_features_debug_internal(&cry_feature);
            cry_print_history_debug_internal(&cry_history);
        }
    }

    return 0;
}

/*********************************************************************
 * @fn      cry_detect_decision
 *
 * @brief   哭声检测判决算法
 *
 * @return  0:未检测到, 1:检测到哭声
 */
static uint8_t cry_detect_decision(void)
{
    uint8_t current_detection = 0;

    // 双阈值判决(原有逻辑)
    if ((cry_feature.base_freq_ratio > cry_config.base_threshold) &&
        (cry_feature.high_freq_ratio > cry_config.high_threshold)) {
        current_detection = 1;
    }

    // 增强判决：结合分类置信度
    if (current_detection && cry_feature.cry_type != CRY_TYPE_UNKNOWN &&
        cry_feature.confidence >= CRY_CONFIDENCE_THRESHOLD) {
        // 分类置信度高，增强检测信心
        current_detection = 1;
    } else if (!current_detection && cry_feature.cry_type != CRY_TYPE_UNKNOWN &&
               cry_feature.confidence >= (CRY_CONFIDENCE_THRESHOLD + 10)) {
        // 即使基础检测未通过，但分类置信度很高，也认为检测到
        current_detection = 1;
    }

    // 滤波处理：连续检测机制
    if (current_detection) {
        cry_feature.detect_count++;
        if (cry_feature.detect_count >= CRY_DETECT_COUNT_THRESHOLD) {
            cry_feature.cry_detected = 1;
            cry_feature.detect_count = CRY_DETECT_COUNT_THRESHOLD; // 防止溢出
        }
    } else {
        // 未检测到时，逐渐减少计数
        if (cry_feature.detect_count > 0) {
            cry_feature.detect_count--;
        }
        if (cry_feature.detect_count == 0) {
            cry_feature.cry_detected = 0;
        }
    }

    return cry_feature.cry_detected;
}

/*********************************************************************
 * @fn      cry_get_magnitude_spectrum
 *
 * @brief   获取幅度谱数据
 *
 * @return  幅度谱数组指针
 */
float* cry_get_magnitude_spectrum(void)
{
    return magnitude;
}

/*********************************************************************
 * @fn      cry_send_uart8_alert
 *
 * @brief   通过串口8发送"bb"字符串
 *
 * @return  none
 */
static void cry_send_uart8_alert(void)
{
    uint8_t alert_msg[] = "bb";
    USART_SendData(UART8, alert_msg[0]); // 发送'b'
    while(USART_GetFlagStatus(UART8, USART_FLAG_TC) == RESET);
    USART_SendData(UART8, alert_msg[1]); // 发送'b'
    while(USART_GetFlagStatus(UART8, USART_FLAG_TC) == RESET);
}

/*********************************************************************
 * @fn      cry_send_mqtt_message
 *
 * @brief   发送哭声检测MQTT消息
 *
 * @param   cry_detected - 哭声检测状态(0:未检测到, 1:检测到)
 *
 * @return  none
 */
static void cry_send_mqtt_message(uint8_t cry_detected)
{
    if (cry_detected) {
        uart6_send_string(cry_mqtt_msg, sizeof(cry_mqtt_msg)-1);
    } else {
        uart6_send_string(cry_stop_mqtt_msg, sizeof(cry_stop_mqtt_msg)-1);
    }
}

/*********************************************************************
 * @fn      cry_handle_detection_result
 *
 * @brief   处理哭声检测结果，执行通信和播放
 *
 * @param   current_status - 当前检测状态
 *
 * @return  none
 */
static void cry_handle_detection_result(uint8_t current_status)
{
    static uint8_t last_status = 0;     // 上次检测状态
    unsigned long int current_time = uwtick;

    // 检测到哭声且状态发生变化
    if (current_status && !last_status) {
        // 检查发送间隔，避免频繁发送
        if ((current_time - cry_status.last_send_time) > CRY_SEND_INTERVAL_MS) {
            cry_send_mqtt_message(1);           // 发送哭声检测消息
            cry_send_uart8_alert();             // 通过串口8发送"bb"
            audio_play(CRY_ALERT_SOUND_ID);     // 播放提示音
            cry_status.last_send_time = current_time;
        }

        // 输出分类结果到串口1
        cry_print_classification_result(&cry_feature);
    }
    // 哭声停止且状态发生变化
    else if (!current_status && last_status) {
        // 发送停止消息
        if ((current_time - cry_status.last_send_time) > CRY_SEND_INTERVAL_MS) {
            cry_send_mqtt_message(0);           // 发送哭声停止消息
            cry_status.last_send_time = current_time;
        }
    }

    last_status = current_status;
}

/*********************************************************************
 * @fn      cry_get_feature_data
 *
 * @brief   获取特征数据
 *
 * @return  特征数据指针
 */
cry_feature_t* cry_get_feature_data(void)
{
    return &cry_feature;
}

/*********************************************************************
 * @fn      cry_load_default_config
 *
 * @brief   加载默认配置参数
 *
 * @return  none
 */
void cry_load_default_config(void)
{
    cry_config.base_threshold = CRY_BASE_FREQ_THRESHOLD;
    cry_config.high_threshold = CRY_HIGH_FREQ_THRESHOLD;
    cry_config.enable = 1;
    cry_config.debug_mode = 0;

    // 分类功能默认配置
    cry_config.classification_enable = 1;
    cry_config.confidence_threshold = CRY_CONFIDENCE_THRESHOLD;
    cry_config.sick_centroid_threshold = 700.0f;
    cry_config.hungry_centroid_min = 400.0f;
    cry_config.hungry_centroid_max = 650.0f;
    cry_config.sleepy_centroid_max = 450.0f;
    cry_config.variance_threshold = 0.25f;
}

/*********************************************************************
 * @fn      cry_validate_config
 *
 * @brief   验证配置参数有效性
 *
 * @param   config - 配置参数指针
 *
 * @return  0:有效, 1:无效
 */
uint8_t cry_validate_config(cry_config_t* config)
{
    if (config == NULL) {
        return 1;
    }

    // 检查阈值范围
    if (config->base_threshold < 0.1f || config->base_threshold > 1.0f) {
        return 1;
    }

    if (config->high_threshold < 0.1f || config->high_threshold > 1.0f) {
        return 1;
    }

    // 检查使能标志
    if (config->enable > 1 || config->debug_mode > 1) {
        return 1;
    }

    return 0;   // 配置有效
}

/*********************************************************************
 * @fn      cry_calculate_zero_crossing_rate
 *
 * @brief   计算零交叉率
 *
 * @param   samples - 音频采样数据
 * @param   sample_count - 采样数据数量
 *
 * @return  零交叉率(0.0-1.0)
 */
static float cry_calculate_zero_crossing_rate(uint16_t* samples, uint16_t sample_count)
{
    uint16_t i;
    uint16_t zero_crossings = 0;
    int16_t prev_sample, curr_sample;

    if (sample_count < 2) {
        return 0.0f;
    }

    // 将ADC值转换为有符号数(减去中点2048)
    prev_sample = (int16_t)(samples[0] - 2048);

    for (i = 1; i < sample_count; i++) {
        curr_sample = (int16_t)(samples[i] - 2048);

        // 检测符号变化(零交叉)
        if ((prev_sample >= 0 && curr_sample < 0) ||
            (prev_sample < 0 && curr_sample >= 0)) {
            zero_crossings++;
        }

        prev_sample = curr_sample;
    }

    // 返回归一化的零交叉率
    return (float)zero_crossings / (float)(sample_count - 1);
}

/*********************************************************************
 * @fn      cry_update_history
 *
 * @brief   更新特征历史缓冲区
 *
 * @param   history - 历史缓冲区指针
 * @param   current - 当前特征数据
 *
 * @return  none
 */
static void cry_update_history(cry_history_t* history, cry_feature_t* current)
{
    // 计算当前总能量(用于历史记录)
    float current_energy = current->base_freq_ratio + current->high_freq_ratio;

    // 更新频谱重心历史
    history->spectral_centroid[history->frame_index] = current->spectral_centroid;

    // 更新能量水平历史
    history->energy_level[history->frame_index] = current_energy;

    // 更新索引(环形缓冲区)
    history->frame_index = (history->frame_index + 1) % CRY_HISTORY_FRAMES;

    // 更新已填充帧数
    if (history->frames_filled < CRY_HISTORY_FRAMES) {
        history->frames_filled++;
    }
}

/*********************************************************************
 * @fn      cry_calculate_energy_trend
 *
 * @brief   计算能量变化趋势
 *
 * @param   history - 历史缓冲区指针
 *
 * @return  能量趋势值(正值表示递增，负值表示递减)
 */
static float cry_calculate_energy_trend(cry_history_t* history)
{
    uint8_t i;
    float sum_x = 0.0f, sum_y = 0.0f, sum_xy = 0.0f, sum_x2 = 0.0f;
    float trend = 0.0f;
    uint8_t count = history->frames_filled;

    if (count < 2) {
        return 0.0f;    // 数据不足，无法计算趋势
    }

    // 使用最小二乘法计算线性趋势
    for (i = 0; i < count; i++) {
        float x = (float)i;
        float y = history->energy_level[i];

        sum_x += x;
        sum_y += y;
        sum_xy += x * y;
        sum_x2 += x * x;
    }

    // 计算斜率(趋势)
    float denominator = count * sum_x2 - sum_x * sum_x;
    if (denominator != 0.0f) {
        trend = (count * sum_xy - sum_x * sum_y) / denominator;
    }

    return trend;
}

/*********************************************************************
 * @fn      cry_calculate_feature_variance
 *
 * @brief   计算特征方差
 *
 * @param   data - 特征数据数组
 * @param   count - 数据数量
 *
 * @return  方差值
 */
static float cry_calculate_feature_variance(float* data, uint8_t count)
{
    uint8_t i;
    float mean = 0.0f, variance = 0.0f;

    if (count < 2) {
        return 0.0f;    // 数据不足，无法计算方差
    }

    // 计算均值
    for (i = 0; i < count; i++) {
        mean += data[i];
    }
    mean /= count;

    // 计算方差
    for (i = 0; i < count; i++) {
        float diff = data[i] - mean;
        variance += diff * diff;
    }
    variance /= count;

    return variance;
}

/*********************************************************************
 * @fn      cry_classify_type
 *
 * @brief   哭声类型分类决策
 *
 * @param   features - 特征数据指针
 * @param   history - 历史数据指针
 *
 * @return  分类结果(cry_type_e)
 */
static uint8_t cry_classify_type(cry_feature_t* features, cry_history_t* history)
{
    // 检查分类功能是否启用
    if (!cry_config.classification_enable) {
        features->cry_type = CRY_TYPE_UNKNOWN;
        return CRY_TYPE_UNKNOWN;
    }

    float centroid = features->spectral_centroid;
    float rolloff = features->spectral_rolloff;
    float energy_trend = cry_calculate_energy_trend(history);
    float variance = features->energy_variance;
    float zcr = features->zero_crossing_rate;

    // 决策树分类算法(基于配置参数)

    // 生病哭声：高频谱重心，低能量方差，持续性强
    if (centroid > cry_config.sick_centroid_threshold && variance < 0.15f && zcr < 0.3f) {
        features->cry_type = CRY_TYPE_SICK;
        return CRY_TYPE_SICK;
    }

    // 饥饿哭声：中等频谱重心，能量递增趋势，规律性强
    if (centroid > cry_config.hungry_centroid_min && centroid < cry_config.hungry_centroid_max &&
        energy_trend > 0.05f && variance < cry_config.variance_threshold) {
        features->cry_type = CRY_TYPE_HUNGRY;
        return CRY_TYPE_HUNGRY;
    }

    // 困倦哭声：低频谱重心，能量递减趋势，间歇性
    if (centroid < cry_config.sleepy_centroid_max && energy_trend < -0.03f && zcr > 0.4f) {
        features->cry_type = CRY_TYPE_SLEEPY;
        return CRY_TYPE_SLEEPY;
    }

    // 无聊哭声：中等频谱重心，高能量方差，不规律
    if (centroid > 350.0f && centroid < 600.0f && variance > 0.3f && zcr > 0.35f) {
        features->cry_type = CRY_TYPE_BORED;
        return CRY_TYPE_BORED;
    }

    // 默认未知类型
    features->cry_type = CRY_TYPE_UNKNOWN;
    return CRY_TYPE_UNKNOWN;
}

/*********************************************************************
 * @fn      cry_calculate_confidence
 *
 * @brief   计算分类置信度
 *
 * @param   features - 特征数据指针
 * @param   history - 历史数据指针
 *
 * @return  置信度(0-100)
 */
static uint8_t cry_calculate_confidence(cry_feature_t* features, cry_history_t* history)
{
    float confidence = 50.0f;   // 基础置信度
    float centroid = features->spectral_centroid;
    float variance = features->energy_variance;
    float zcr = features->zero_crossing_rate;
    uint8_t cry_type = features->cry_type;

    // 根据特征稳定性调整置信度
    if (history->frames_filled >= 3) {
        // 计算频谱重心的稳定性
        float centroid_variance = cry_calculate_feature_variance(history->spectral_centroid, history->frames_filled);
        if (centroid_variance < 50.0f) {
            confidence += 15.0f;    // 频谱重心稳定
        }

        // 计算能量趋势的一致性
        float energy_trend = cry_calculate_energy_trend(history);
        if (energy_trend > 0.1f || energy_trend < -0.1f) {
            confidence += 10.0f;    // 明显的能量趋势
        }
    }

    // 根据分类类型调整置信度
    switch (cry_type) {
        case CRY_TYPE_SICK:
            if (centroid > 750.0f && variance < 0.1f) {
                confidence += 20.0f;    // 强特征匹配
            }
            break;

        case CRY_TYPE_HUNGRY:
            if (centroid > 450.0f && centroid < 600.0f && variance < 0.2f) {
                confidence += 15.0f;    // 中等特征匹配
            }
            break;

        case CRY_TYPE_SLEEPY:
            if (centroid < 400.0f && zcr > 0.45f) {
                confidence += 15.0f;    // 中等特征匹配
            }
            break;

        case CRY_TYPE_BORED:
            if (variance > 0.35f && zcr > 0.4f) {
                confidence += 10.0f;    // 弱特征匹配
            }
            break;

        default:
            confidence = 30.0f;         // 未知类型低置信度
            break;
    }

    // 限制置信度范围
    if (confidence > 95.0f) confidence = 95.0f;
    if (confidence < 20.0f) confidence = 20.0f;

    return (uint8_t)confidence;
}

/*********************************************************************
 * @fn      cry_print_classification_result
 *
 * @brief   通过串口输出哭声分类结果
 *
 * @param   features - 特征数据指针
 *
 * @return  none
 */
static void cry_print_classification_result(cry_feature_t* features)
{
    static uint8_t last_type = CRY_TYPE_UNKNOWN;
    static uint32_t last_output_time = 0;
    static uint8_t output_count = 0;
    uint32_t current_time = uwtick;

    // 控制输出频率：只在类型变化、置信度足够高或间隔足够长时输出
    uint8_t should_output = 0;

    // 类型变化且置信度达标
    if (features->cry_type != last_type && features->confidence >= CRY_CONFIDENCE_THRESHOLD) {
        should_output = 1;
    }
    // 或者间隔时间达到分类输出间隔
    else if ((current_time - last_output_time) >= CRY_CLASSIFICATION_INTERVAL_MS &&
             features->cry_detected && features->confidence >= CRY_CONFIDENCE_THRESHOLD) {
        should_output = 1;
    }

    if (should_output) {
        // 输出分类结果到串口1
        printf("CryType: %s, Confidence: %d%%, Features: [%.1f,%.1f,%.3f]\r\n",
               cry_type_names[features->cry_type],
               features->confidence,
               features->spectral_centroid,
               features->spectral_rolloff,
               features->energy_variance);

        // 输出声音状态信息
        if (features->cry_detected) {
            printf("Sound Status: CRY_DETECTED, Type: %s\r\n", cry_type_names[features->cry_type]);
        } else {
            printf("Sound Status: MONITORING\r\n");
        }

        // 更新状态
        last_type = features->cry_type;
        last_output_time = current_time;
        output_count++;

        // 每10次输出显示一次统计信息
        if (output_count % 10 == 0) {
            printf("Classification Stats: Total=%d, Current_ZCR=%.3f\r\n",
                   output_count, features->zero_crossing_rate);
        }
    }
}

/*********************************************************************
 * @fn      cry_get_classification_stats
 *
 * @brief   获取分类统计信息
 *
 * @param   total_count - 总分类次数指针
 * @param   last_confidence - 最后置信度指针
 *
 * @return  none
 */
static void cry_get_classification_stats(uint8_t* total_count, uint8_t* last_confidence)
{
    static uint8_t classification_count = 0;

    if (cry_feature.cry_type != CRY_TYPE_UNKNOWN) {
        classification_count++;
    }

    if (total_count != NULL) {
        *total_count = classification_count;
    }

    if (last_confidence != NULL) {
        *last_confidence = cry_feature.confidence;
    }
}

/*********************************************************************
 * @fn      cry_get_classification_type
 *
 * @brief   获取当前分类类型
 *
 * @return  哭声类型(cry_type_e)
 */
uint8_t cry_get_classification_type(void)
{
    return cry_feature.cry_type;
}

/*********************************************************************
 * @fn      cry_get_classification_confidence
 *
 * @brief   获取分类置信度
 *
 * @return  置信度(0-100)
 */
uint8_t cry_get_classification_confidence(void)
{
    return cry_feature.confidence;
}

/*********************************************************************
 * @fn      cry_print_features_debug_internal
 *
 * @brief   内部调试输出特征信息
 *
 * @param   features - 特征数据指针
 *
 * @return  none
 */
static void cry_print_features_debug_internal(cry_feature_t* features)
{
    if (!cry_config.debug_mode) return;

    printf("=== CRY FEATURES DEBUG ===\r\n");
    printf("Base Freq Ratio: %.3f\r\n", features->base_freq_ratio);
    printf("High Freq Ratio: %.3f\r\n", features->high_freq_ratio);
    printf("Spectral Centroid: %.1f Hz\r\n", features->spectral_centroid);
    printf("Spectral Rolloff: %.1f Hz\r\n", features->spectral_rolloff);
    printf("Energy Variance: %.3f\r\n", features->energy_variance);
    printf("Zero Crossing Rate: %.3f\r\n", features->zero_crossing_rate);
    printf("Cry Type: %s (%d)\r\n", cry_type_names[features->cry_type], features->cry_type);
    printf("Confidence: %d%%\r\n", features->confidence);
    printf("Detected: %s, Count: %d\r\n", features->cry_detected ? "YES" : "NO", features->detect_count);
    printf("========================\r\n");
}

/*********************************************************************
 * @fn      cry_print_history_debug_internal
 *
 * @brief   内部调试输出历史信息
 *
 * @param   history - 历史数据指针
 *
 * @return  none
 */
static void cry_print_history_debug_internal(cry_history_t* history)
{
    if (!cry_config.debug_mode) return;

    printf("=== CRY HISTORY DEBUG ===\r\n");
    printf("Frame Index: %d, Filled: %d\r\n", history->frame_index, history->frames_filled);
    printf("Spectral Centroid History: ");
    for (uint8_t i = 0; i < history->frames_filled && i < 5; i++) {
        printf("%.1f ", history->spectral_centroid[i]);
    }
    printf("\r\nEnergy Level History: ");
    for (uint8_t i = 0; i < history->frames_filled && i < 5; i++) {
        printf("%.3f ", history->energy_level[i]);
    }
    printf("\r\n========================\r\n");
}

/*********************************************************************
 * @fn      cry_print_features_debug
 *
 * @brief   调试输出特征信息(公共接口)
 *
 * @return  none
 */
void cry_print_features_debug(void)
{
    cry_print_features_debug_internal(&cry_feature);
}

/*********************************************************************
 * @fn      cry_print_history_debug
 *
 * @brief   调试输出历史信息(公共接口)
 *
 * @return  none
 */
void cry_print_history_debug(void)
{
    cry_print_history_debug_internal(&cry_history);
}

/*********************************************************************
 * @fn      cry_set_classification_config
 *
 * @brief   设置分类配置参数
 *
 * @param   config - 配置参数指针
 *
 * @return  none
 */
void cry_set_classification_config(cry_config_t* config)
{
    if (config == NULL) return;

    // 验证并设置分类配置
    if (config->confidence_threshold >= 50 && config->confidence_threshold <= 95) {
        cry_config.confidence_threshold = config->confidence_threshold;
    }

    if (config->sick_centroid_threshold >= 500.0f && config->sick_centroid_threshold <= 1000.0f) {
        cry_config.sick_centroid_threshold = config->sick_centroid_threshold;
    }

    if (config->hungry_centroid_min >= 300.0f && config->hungry_centroid_min <= 500.0f) {
        cry_config.hungry_centroid_min = config->hungry_centroid_min;
    }

    if (config->hungry_centroid_max >= 600.0f && config->hungry_centroid_max <= 800.0f) {
        cry_config.hungry_centroid_max = config->hungry_centroid_max;
    }

    if (config->sleepy_centroid_max >= 300.0f && config->sleepy_centroid_max <= 500.0f) {
        cry_config.sleepy_centroid_max = config->sleepy_centroid_max;
    }

    if (config->variance_threshold >= 0.1f && config->variance_threshold <= 0.5f) {
        cry_config.variance_threshold = config->variance_threshold;
    }

    cry_config.classification_enable = config->classification_enable;
    cry_config.debug_mode = config->debug_mode;

    if (cry_config.debug_mode) {
        printf("Classification config updated successfully\r\n");
    }
}

/*********************************************************************
 * @fn      cry_get_classification_config
 *
 * @brief   获取分类配置参数
 *
 * @return  配置参数指针
 */
cry_config_t* cry_get_classification_config(void)
{
    return &cry_config;
}
