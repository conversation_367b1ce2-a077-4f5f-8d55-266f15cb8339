/*
 * cry_detect.c - 婴儿哭声识别模块
 *
 *  Created on: 2025年6月30日
 *      Author: AI Assistant
 */

#include "cry_detect.h"
#include "uart.h"
#include "../FFT/kiss_fft.h"
#include <math.h>
#include <string.h>

/* 全局变量 */
u16 TxBuf[SAMPLE_SIZE];             // ADC采样缓冲区
s16 Calibrattion_Val = 0;           // ADC校准值
cry_status_t cry_status;            // 系统状态

/* 内部变量 */
static uint32_t energy_buf[SMOOTH_SIZE] = {0};  // 能量平滑缓冲区
static uint16_t peak_buf[SMOOTH_SIZE] = {0};    // 峰值平滑缓冲区
static uint16_t zcr_buf[SMOOTH_SIZE] = {0};     // 零交叉率平滑缓冲区
static uint8_t smooth_index = 0;                // 平滑缓冲区索引

static kiss_fft_cfg fft_cfg;                    // FFT配置
static kiss_fft_cpx fft_in[SAMPLE_SIZE];        // FFT输入缓冲区
static kiss_fft_cpx fft_out[SAMPLE_SIZE];       // FFT输出缓冲区

/**
 * @brief ADC功能初始化
 */
void ADC_Function_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure={0};
    GPIO_InitTypeDef GPIO_InitStructure={0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);
    RCC_ADCCLKConfig(RCC_PCLK2_Div8);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    ADC_DeInit(ADC1);
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 1;
    ADC_Init(ADC1, &ADC_InitStructure);

    ADC_DMACmd(ADC1, ENABLE);
    ADC_Cmd(ADC1, ENABLE);

    ADC_BufferCmd(ADC1, DISABLE);   // 禁用缓冲
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    Calibrattion_Val = Get_CalibrationValue(ADC1);
}

/**
 * @brief DMA传输初始化
 */
void DMA_Tx_Init(DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize)
{
    DMA_InitTypeDef DMA_InitStructure={0};

    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);

    DMA_DeInit(DMA_CHx);
    DMA_InitStructure.DMA_PeripheralBaseAddr = ppadr;
    DMA_InitStructure.DMA_MemoryBaseAddr = memadr;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = bufsize;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA_CHx, &DMA_InitStructure);
}

/**
 * @brief 获取校准后的ADC转换值
 */
u16 Get_ConversionVal(s16 val)
{
    if((val+Calibrattion_Val)<0|| val==0) return 0;
    if((Calibrattion_Val+val)>4095||val==4095) return 4095;
    return (val+Calibrattion_Val);
}

/**
 * @brief 哭声检测模块初始化
 */
void cry_detect_init(void)
{
    printf("Cry detect init starting...\r\n");
    
    // 初始化FFT配置
    fft_cfg = kiss_fft_alloc(SAMPLE_SIZE, 0, NULL, NULL);
    if (fft_cfg == NULL) {
        printf("FFT config allocation failed!\r\n");
        return;
    }
    
    // 初始化ADC和DMA
    ADC_Function_Init();
    printf("ADC calibration value: %d\r\n", Calibrattion_Val);
    
    DMA_Tx_Init(DMA1_Channel1, (u32)&ADC1->RDATAR, (u32)TxBuf, SAMPLE_SIZE);
    DMA_Cmd(DMA1_Channel1, ENABLE);
    
    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_239Cycles5);
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);
    
    // 初始化状态
    cry_status.initialized = 1;
    cry_status.cry_detected = 0;
    cry_status.cry_type = CRY_TYPE_UNKNOWN;
    cry_status.last_send_time = 0;
    
    printf("Cry detect init completed\r\n");
}

/**
 * @brief 哭声检测主处理函数
 */
void cry_detect_proc(void)
{
    if (!cry_status.initialized) {
        return;
    }
    
    // 计算DC偏移
    uint32_t dc_offset_sum = 0;
    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        dc_offset_sum += Get_ConversionVal(TxBuf[i]);
    }
    uint16_t dc_offset = dc_offset_sum / SAMPLE_SIZE;
    
    // 计算能量、峰值和零交叉率
    uint32_t energy_sum = 0;
    uint16_t peak_val = 0;
    uint16_t zcr = 0;
    int16_t last_sample = Get_ConversionVal(TxBuf[0]) - dc_offset;
    
    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        int16_t sample = Get_ConversionVal(TxBuf[i]) - dc_offset;
        uint16_t abs_sample = abs(sample);
        
        if (abs_sample > peak_val) peak_val = abs_sample;
        
        energy_sum += (int32_t)sample * sample;
        
        // 计算零交叉率
        if ((sample > 0 && last_sample < 0) || (sample < 0 && last_sample > 0)) {
            zcr++;
        }
        last_sample = sample;
    }
    
    uint32_t avg_energy = energy_sum / SAMPLE_SIZE;
    
    // 平滑滤波
    energy_buf[smooth_index] = avg_energy;
    peak_buf[smooth_index] = peak_val;
    zcr_buf[smooth_index] = zcr;
    
    // 计算平滑值
    uint32_t energy_sum_s = 0;
    uint16_t peak_sum_s = 0, zcr_sum_s = 0;
    for (uint8_t i = 0; i < SMOOTH_SIZE; i++) {
        energy_sum_s += energy_buf[i];
        peak_sum_s += peak_buf[i];
        zcr_sum_s += zcr_buf[i];
    }
    
    uint32_t smooth_energy = energy_sum_s / SMOOTH_SIZE;
    uint16_t smooth_peak = peak_sum_s / SMOOTH_SIZE;
    uint16_t smooth_zcr = zcr_sum_s / SMOOTH_SIZE;
    
    smooth_index = (smooth_index + 1) % SMOOTH_SIZE;
    
    // 打印监控信息
    printf("Peak: %d, Energy: %ld, ZCR: %d\r\n", smooth_peak, smooth_energy, smooth_zcr);
    
    // 检测哭声
    if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr > ZCR_THRESHOLD) {
        printf(">>> Crying Detected <<<\r\n");
        cry_status.cry_detected = 1;
        
        // 执行FFT分析进行分类
        cry_classify_cry_type();
        
        // 发送UART8触发信号
        Uart8_SendByte('b');
        Uart8_SendByte('b');
        
    } else if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr <= ZCR_THRESHOLD) {
        printf(">>> Speaking Detected <<<\r\n");
        cry_status.cry_detected = 0;
    } else {
        printf(">>> Quiet <<<\r\n");
        cry_status.cry_detected = 0;
    }
}

/**
 * @brief 哭声类型分类
 */
void cry_classify_cry_type(void)
{
    // 准备FFT输入数据
    for (int i = 0; i < SAMPLE_SIZE; i++) {
        int16_t sample = Get_ConversionVal(TxBuf[i]) - 2048;  // 去偏置
        fft_in[i].r = sample;
        fft_in[i].i = 0;
    }
    
    // 执行FFT
    kiss_fft(fft_cfg, fft_in, fft_out);
    
    // 计算频段能量(只分析前半部分)
    float low_energy = 0, mid_energy = 0, high_energy = 0;
    for (int i = 1; i < SAMPLE_SIZE / 2; i++) {
        float mag = sqrtf(fft_out[i].r * fft_out[i].r + fft_out[i].i * fft_out[i].i);
        
        // 采样率为8000Hz，每个频点频率为8000/1024≈7.8Hz
        float freq = (8000.0f / SAMPLE_SIZE) * i;
        
        if (freq < 500) {
            low_energy += mag;
        } else if (freq < 1500) {
            mid_energy += mag;
        } else {
            high_energy += mag;
        }
    }
    
    // 分类判断
    if (low_energy > mid_energy && low_energy > high_energy) {
        cry_status.cry_type = CRY_TYPE_SLEEPY;
        printf(">>> Sleepy Cry <<<\r\n");
    } else if (mid_energy > low_energy && mid_energy > high_energy) {
        cry_status.cry_type = CRY_TYPE_ANGRY;
        printf(">>> Angry Cry <<<\r\n");
    } else {
        cry_status.cry_type = CRY_TYPE_HUNGRY;
        printf(">>> Hungry Cry <<<\r\n");
    }
    
    // 打印频段能量值
    uint32_t low_val = (uint32_t)(low_energy * 1000);
    uint32_t mid_val = (uint32_t)(mid_energy * 1000);
    uint32_t high_val = (uint32_t)(high_energy * 1000);
    printf("Low: %lu Mid: %lu High: %lu\r\n", low_val, mid_val, high_val);
}

/**
 * @brief 获取检测状态
 */
uint8_t cry_get_status(void)
{
    return cry_status.cry_detected;
}

/**
 * @brief 获取哭声类型
 */
cry_type_e cry_get_type(void)
{
    return cry_status.cry_type;
}
