Archive member included to satisfy reference by file (symbol)

d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
                              ./FFT/kiss_fft.o (cos)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
                              ./FFT/kiss_fft.o (floor)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
                              ./FFT/kiss_fft.o (sin)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
                              ./FFT/kiss_fft.o (sqrt)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
                              ./APP/cry_detect.o (sqrtf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o) (__ieee754_rem_pio2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o) (__ieee754_sqrt)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o) (__ieee754_sqrtf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o) (__kernel_cos)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o) (__kernel_rem_pio2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o) (__kernel_sin)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o) (fabs)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_lib_ver.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o) (__fdlib_version)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o) (matherr)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o) (scalbn)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o) (copysign)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o) (__adddf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
                              ./FFT/kiss_fft.o (__divdf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o) (__eqdf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o) (__gedf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
                              ./FFT/kiss_fft.o (__ltdf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
                              ./FFT/kiss_fft.o (__muldf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o) (__subdf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o) (__unorddf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o) (__fixdfsi)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
                              ./FFT/kiss_fft.o (__floatsidf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
                              ./FFT/kiss_fft.o (__addsf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
                              ./APP/cry_detect.o (__divsf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
                              ./APP/cry_detect.o (__eqsf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
                              ./APP/cry_detect.o (__gesf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
                              ./APP/cry_detect.o (__lesf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
                              ./User/main.o (__mulsf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
                              ./FFT/kiss_fft.o (__subsf3)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o) (__unordsf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
                              ./APP/cry_detect.o (__fixsfsi)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
                              ./User/main.o (__fixunssfsi)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
                              ./APP/cry_detect.o (__floatsisf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
                              ./APP/cry_detect.o (__floatunsisf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
                              ./APP/cry_detect.o (__extendsfdf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
                              ./FFT/kiss_fft.o (__truncdfsf2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
                              ./FFT/kiss_fft.o (__riscv_save_12)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o) (__clzsi2)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o) (__clz_tab)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o) (__errno)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
                              ./FFT/kiss_fft.o (fputc)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
                              ./FFT/kiss_fft.o (fputs)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                              ./FFT/kiss_fft.o (_impure_ptr)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
                              ./FFT/kiss_fft.o (malloc)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
                              ./User/main.o (memcpy)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
                              ./APP/cry_detect.o (memset)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o) (_free_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o) (_malloc_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
                              ./User/main.o (printf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o) (_putc_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                              ./User/main.o (puts)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o) (_sbrk_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
                              ./User/main.o (snprintf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
                              ./User/main.o (sscanf)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o) (__seofread)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
                              ./User/main.o (strlen)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
                              ./User/main.o (strncmp)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
                              ./User/main.o (strstr)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o) (__swbuf_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_write_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o) (__swsetup_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_close_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o) (_fflush_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o) (__sinit)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o) (_fwalk)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_lseek_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o) (__smakebuf_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o) (__malloc_lock)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o) (_svfprintf_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o) (__ssvfscanf_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o) (_vfprintf_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o) (_printf_i)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o) (_scanf_chars)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o) (_read_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o) (errno)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o) (__sccl)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o) (_strtol_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o) (_strtoul_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o) (__submore)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o) (_fstat_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o) (__sfvwrite_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o) (_isatty_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o) (__locale_ctype_ptr_l)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o) (__ascii_mbtowc)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o) (memchr)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o) (memmove)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o) (_realloc_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strcmp.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o) (strcmp)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o) (__ascii_wctomb)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ctype_.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o) (_ctype_)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o) (_malloc_usable_size_r)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o) (_close)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o) (_fstat)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o) (_isatty)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o) (_lseek)
d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                              d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o) (_read)

Allocating common symbols
Common symbol       size              file

errno               0x4               d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)

Discarded input sections

 .text          0x***********00000        0x0 ./User/ch32v30x_it.o
 .data          0x***********00000        0x0 ./User/ch32v30x_it.o
 .bss           0x***********00000        0x0 ./User/ch32v30x_it.o
 .text          0x***********00000        0x0 ./User/main.o
 .data          0x***********00000        0x0 ./User/main.o
 .bss           0x***********00000        0x0 ./User/main.o
 .text.scheduler_init
                0x***********00000        0xc ./User/main.o
 .text.json     0x***********00000      0x128 ./User/main.o
 .rodata.json.str1.4
                0x***********00000       0x22 ./User/main.o
 .sbss.time1000ms
                0x***********00000        0x4 ./User/main.o
 .sbss.usart3_state
                0x***********00000        0x1 ./User/main.o
 .sdata.u_add   0x***********00000        0x3 ./User/main.o
 .text          0x***********00000        0x0 ./User/system_ch32v30x.o
 .data          0x***********00000        0x0 ./User/system_ch32v30x.o
 .bss           0x***********00000        0x0 ./User/system_ch32v30x.o
 .text          0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .data          0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .bss           0x***********00000        0x0 ./Startup/startup_ch32v30x_D8C.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_StructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ITConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetSoftwareStartConvStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DiscModeChannelCountConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_DiscModeCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigConvCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetConversionValue
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetDualModeConversionValue
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AutoInjectedConvCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedDiscModeCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigInjectedConvConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ExternalTrigInjectedConvCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_SoftwareStartInjectedConvCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetSoftwareStartInjectedConvCmdStatus
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedChannelConfig
                0x***********00000       0x7a ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_InjectedSequencerLengthConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_SetInjectedOffset
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetInjectedConversionValue
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogThresholdsConfig
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_AnalogWatchdogSingleChannelConfig
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_TempSensorVrefintCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_GetITStatus
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_adc.o
 .text.ADC_ClearITPendingBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_adc.o
 .text.TempSensor_Volt_To_Temper
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_adc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_DeInit
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_TamperPinLevelConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_TamperPinCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ITConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_RTCOutputConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_SetRTCCalibrationValue
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_WriteBackupRegister
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ReadBackupRegister
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ClearFlag
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_GetITStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_bkp.o
 .text.BKP_ClearITPendingBit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_bkp.o
 .debug_info    0x***********00000     0x10d1 ./Peripheral/src/ch32v30x_bkp.o
 .debug_abbrev  0x***********00000      0x2cc ./Peripheral/src/ch32v30x_bkp.o
 .debug_loc     0x***********00000       0xd8 ./Peripheral/src/ch32v30x_bkp.o
 .debug_aranges
                0x***********00000       0x78 ./Peripheral/src/ch32v30x_bkp.o
 .debug_ranges  0x***********00000       0x68 ./Peripheral/src/ch32v30x_bkp.o
 .debug_line    0x***********00000      0x66e ./Peripheral/src/ch32v30x_bkp.o
 .debug_str     0x***********00000      0xaa5 ./Peripheral/src/ch32v30x_bkp.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_bkp.o
 .debug_frame   0x***********00000       0xec ./Peripheral/src/ch32v30x_bkp.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_DeInit
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Init
                0x***********00000       0xe8 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_FilterInit
                0x***********00000       0xee ./Peripheral/src/ch32v30x_can.o
 .text.CAN_StructInit
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_SlaveStartBank
                0x***********00000       0x38 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_DBGFreeze
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_TTComModeCmd
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Transmit
                0x***********00000       0xbc ./Peripheral/src/ch32v30x_can.o
 .text.CAN_TransmitStatus
                0x***********00000       0x62 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_CancelTransmit
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Receive
                0x***********00000       0x8c ./Peripheral/src/ch32v30x_can.o
 .text.CAN_FIFORelease
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_MessagePending
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_OperatingModeRequest
                0x***********00000       0x72 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_Sleep
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_WakeUp
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetLastErrorCode
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetReceiveErrorCounter
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetLSBTransmitErrorCounter
                0x***********00000        0xa ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetFlagStatus
                0x***********00000       0x56 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ClearFlag
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_GetITStatus
                0x***********00000       0xd0 ./Peripheral/src/ch32v30x_can.o
 .text.CAN_ClearITPendingBit
                0x***********00000       0x94 ./Peripheral/src/ch32v30x_can.o
 .debug_info    0x***********00000     0x1807 ./Peripheral/src/ch32v30x_can.o
 .debug_abbrev  0x***********00000      0x3eb ./Peripheral/src/ch32v30x_can.o
 .debug_loc     0x***********00000      0x999 ./Peripheral/src/ch32v30x_can.o
 .debug_aranges
                0x***********00000       0xd8 ./Peripheral/src/ch32v30x_can.o
 .debug_ranges  0x***********00000       0xf8 ./Peripheral/src/ch32v30x_can.o
 .debug_line    0x***********00000     0x1a31 ./Peripheral/src/ch32v30x_can.o
 .debug_str     0x***********00000      0xbbb ./Peripheral/src/ch32v30x_can.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_can.o
 .debug_frame   0x***********00000      0x19c ./Peripheral/src/ch32v30x_can.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_ResetDR
                0x***********00000        0xa ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_CalcCRC
                0x***********00000        0xa ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_CalcBlockCRC
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_GetCRC
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_SetIDRegister
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .text.CRC_GetIDRegister
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_crc.o
 .debug_info    0x***********00000      0xab3 ./Peripheral/src/ch32v30x_crc.o
 .debug_abbrev  0x***********00000      0x25f ./Peripheral/src/ch32v30x_crc.o
 .debug_loc     0x***********00000       0x75 ./Peripheral/src/ch32v30x_crc.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_crc.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_crc.o
 .debug_line    0x***********00000      0x3bc ./Peripheral/src/ch32v30x_crc.o
 .debug_str     0x***********00000      0x67b ./Peripheral/src/ch32v30x_crc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_crc.o
 .debug_frame   0x***********00000       0x70 ./Peripheral/src/ch32v30x_crc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DeInit
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_Init
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_Cmd  0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DMACmd
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SoftwareTriggerCmd
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_DualSoftwareTriggerCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_WaveGenerationCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetChannel1Data
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetChannel2Data
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_SetDualChannelData
                0x***********00000       0x1c ./Peripheral/src/ch32v30x_dac.o
 .text.DAC_GetDataOutputValue
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_dac.o
 .debug_info    0x***********00000      0xdbd ./Peripheral/src/ch32v30x_dac.o
 .debug_abbrev  0x***********00000      0x2fe ./Peripheral/src/ch32v30x_dac.o
 .debug_loc     0x***********00000      0x238 ./Peripheral/src/ch32v30x_dac.o
 .debug_aranges
                0x***********00000       0x78 ./Peripheral/src/ch32v30x_dac.o
 .debug_ranges  0x***********00000       0x68 ./Peripheral/src/ch32v30x_dac.o
 .debug_line    0x***********00000      0x7ee ./Peripheral/src/ch32v30x_dac.o
 .debug_str     0x***********00000      0x7ff ./Peripheral/src/ch32v30x_dac.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dac.o
 .debug_frame   0x***********00000       0xf4 ./Peripheral/src/ch32v30x_dac.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_GetREVID
                0x***********00000        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_GetDEVID
                0x***********00000        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.__get_DEBUG_CR
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.__set_DEBUG_CR
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text.DBGMCU_Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_dbgmcu.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_StructInit
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_SetCurrDataCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_dma.o
 .text.DMA_GetCurrDataCounter
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_dma.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_INTCfg
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_Mode
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_dvp.o
 .text.DVP_Cfg  0x***********00000       0x62 ./Peripheral/src/ch32v30x_dvp.o
 .debug_info    0x***********00000      0xbc5 ./Peripheral/src/ch32v30x_dvp.o
 .debug_abbrev  0x***********00000      0x241 ./Peripheral/src/ch32v30x_dvp.o
 .debug_loc     0x***********00000       0x74 ./Peripheral/src/ch32v30x_dvp.o
 .debug_aranges
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_dvp.o
 .debug_ranges  0x***********00000       0x20 ./Peripheral/src/ch32v30x_dvp.o
 .debug_line    0x***********00000      0x42f ./Peripheral/src/ch32v30x_dvp.o
 .debug_str     0x***********00000      0x73c ./Peripheral/src/ch32v30x_dvp.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_dvp.o
 .debug_frame   0x***********00000       0x40 ./Peripheral/src/ch32v30x_dvp.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DeInit
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_StructInit
                0x***********00000       0xd8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandleTxPkt
                0x***********00000       0x8c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandleRxPkt
                0x***********00000       0x9e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetRxPktSize
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DropRxPkt
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ReadPHYRegister
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_WritePHYRegister
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PHYLoopBackCmd
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACTransmissionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACReceptionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetFlowControlBusyStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_InitiatePauseControlFrame
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_BackPressureActivationCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressConfig
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMACAddress
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressPerfectFilterCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressFilterConfig
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MACAddressMaskBytesFilterConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescChainInit
                0x***********00000       0x46 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescRingInit
                0x***********00000       0x44 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMATxDescFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMATxDescCollisionCount
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetDMATxDescOwnBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescTransmitITConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescFrameSegmentConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescChecksumInsertionConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescCRCCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescEndOfRingCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescSecondAddressChainedCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescShortFramePaddingCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescTimeStampCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATxDescBufferSizeConfig
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescChainInit
                0x***********00000       0x50 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescRingInit
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetDMARxDescOwnBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescFrameLength
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescReceiveITConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescEndOfRingCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMARxDescSecondAddressChainedCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMARxDescBufferSize
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SoftwareReset
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetSoftwareResetStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetlinkStaus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetTransmitProcessState
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetReceiveProcessState
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_FlushTransmitFIFO
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_Start
                0x***********00000       0x40 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetFlushTransmitFIFOStatus
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMATransmissionCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAReceptionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetDMAOverflowStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetRxOverflowMissedFrameCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetBufferUnavailableMissedFrameCounter
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentTxDescStartAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentRxDescStartAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentTxBufferAddress
                0x***********00000        0xc ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetCurrentRxBufferAddress
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResumeDMATransmission
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResumeDMAReception
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_ResetWakeUpFrameFilterRegisterPointer
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetWakeUpFrameFilterRegister
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GlobalUnicastWakeUpCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPMTFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_WakeUpFrameDetectionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MagicPacketDetectionCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PowerDownCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCounterFreezeCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCResetOnReadCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCounterRolloverCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCCountersReset
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_MMCITConfig
                0x***********00000       0x5c ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMMCITStatus
                0x***********00000       0x3a ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetMMCRegister
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampAddend
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampInterruptTrigger
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_EnablePTPTimeStampUpdate
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_InitializePTPTimeStamp
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PTPUpdateMethodConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_PTPTimeStampCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPTPFlagStatus
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPSubSecondIncrement
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTimeStampUpdate
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTimeStampAddend
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_SetPTPTargetTime
                0x***********00000        0xe ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_GetPTPRegister
                0x***********00000        0xa ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAPTPTxDescChainInit
                0x***********00000       0x66 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_DMAPTPRxDescChainInit
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandlePTPTxPkt
                0x***********00000       0xe6 ./Peripheral/src/ch32v30x_eth.o
 .text.ETH_HandlePTPRxPkt
                0x***********00000       0xd0 ./Peripheral/src/ch32v30x_eth.o
 .text.RGMII_TXC_Delay
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMAPTPRxDescToGet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMAPTPTxDescToSet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMARxDescToGet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .sbss.DMATxDescToSet
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_eth.o
 .debug_info    0x***********00000     0x270c ./Peripheral/src/ch32v30x_eth.o
 .debug_abbrev  0x***********00000      0x47b ./Peripheral/src/ch32v30x_eth.o
 .debug_loc     0x***********00000      0xef1 ./Peripheral/src/ch32v30x_eth.o
 .debug_aranges
                0x***********00000      0x300 ./Peripheral/src/ch32v30x_eth.o
 .debug_ranges  0x***********00000      0x368 ./Peripheral/src/ch32v30x_eth.o
 .debug_line    0x***********00000     0x3222 ./Peripheral/src/ch32v30x_eth.o
 .debug_str     0x***********00000     0x1910 ./Peripheral/src/ch32v30x_eth.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_eth.o
 .debug_frame   0x***********00000      0x6a4 ./Peripheral/src/ch32v30x_eth.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_DeInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_Init
                0x***********00000       0x6a ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GenerateSWInterrupt
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_ClearFlag
                0x***********00000        0xa ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_GetITStatus
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_exti.o
 .text.EXTI_ClearITPendingBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_exti.o
 .debug_info    0x***********00000      0xc0d ./Peripheral/src/ch32v30x_exti.o
 .debug_abbrev  0x***********00000      0x2da ./Peripheral/src/ch32v30x_exti.o
 .debug_loc     0x***********00000      0x181 ./Peripheral/src/ch32v30x_exti.o
 .debug_aranges
                0x***********00000       0x50 ./Peripheral/src/ch32v30x_exti.o
 .debug_ranges  0x***********00000       0x40 ./Peripheral/src/ch32v30x_exti.o
 .debug_line    0x***********00000      0x5da ./Peripheral/src/ch32v30x_exti.o
 .debug_str     0x***********00000      0x78d ./Peripheral/src/ch32v30x_exti.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_exti.o
 .debug_frame   0x***********00000       0x90 ./Peripheral/src/ch32v30x_exti.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_flash.o
 .text.ROM_ERASE
                0x***********00000       0xa0 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Unlock
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_UnlockBank1
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Lock
                0x***********00000        0xe ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_LockBank1
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetUserOptionByte
                0x***********00000        0xa ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetWriteProtectionOptionByte
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetReadOutProtectionStatus
                0x***********00000        0xc ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetFlagStatus
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_GetBank1Status
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_WaitForLastOperation
                0x***********00000       0x34 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ErasePage
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseAllPages
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseAllBank1Pages
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseOptionBytes
                0x***********00000       0xe6 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramWord
                0x***********00000       0x68 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramHalfWord
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramOptionByteData
                0x***********00000      0x11a ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EnableWriteProtection
                0x***********00000      0x106 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ReadOutProtection
                0x***********00000       0xf4 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_UserOptionByteConfig
                0x***********00000      0x108 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_WaitForLastBank1Operation
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Unlock_Fast
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Lock_Fast
                0x***********00000        0xe ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ErasePage_Fast
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseBlock_32K_Fast
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_EraseBlock_64K_Fast
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ProgramPage_Fast
                0x***********00000       0x64 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Access_Clock_Cfg
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_Enhance_Mode
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ROM_ERASE
                0x***********00000      0x1a4 ./Peripheral/src/ch32v30x_flash.o
 .text.FLASH_ROM_WRITE
                0x***********00000       0xce ./Peripheral/src/ch32v30x_flash.o
 .debug_info    0x***********00000     0x15b7 ./Peripheral/src/ch32v30x_flash.o
 .debug_abbrev  0x***********00000      0x468 ./Peripheral/src/ch32v30x_flash.o
 .debug_loc     0x***********00000      0xc6e ./Peripheral/src/ch32v30x_flash.o
 .debug_aranges
                0x***********00000      0x110 ./Peripheral/src/ch32v30x_flash.o
 .debug_ranges  0x***********00000      0x100 ./Peripheral/src/ch32v30x_flash.o
 .debug_line    0x***********00000     0x24d1 ./Peripheral/src/ch32v30x_flash.o
 .debug_str     0x***********00000      0xb13 ./Peripheral/src/ch32v30x_flash.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_flash.o
 .debug_frame   0x***********00000      0x380 ./Peripheral/src/ch32v30x_flash.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMDeInit
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDDeInit
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMInit
                0x***********00000       0xae ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDInit
                0x***********00000       0x84 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMStructInit
                0x***********00000       0x5c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDStructInit
                0x***********00000       0x36 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NORSRAMCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDCmd
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_NANDECCCmd
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_GetECC
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_fsmc.o
 .text.FSMC_GetFlagStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_info    0x***********00000      0xf08 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_abbrev  0x***********00000      0x2d9 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_loc     0x***********00000      0x2ae ./Peripheral/src/ch32v30x_fsmc.o
 .debug_aranges
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_ranges  0x***********00000       0x60 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_line    0x***********00000      0xc1a ./Peripheral/src/ch32v30x_fsmc.o
 .debug_str     0x***********00000      0xa60 ./Peripheral/src/ch32v30x_fsmc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_fsmc.o
 .debug_frame   0x***********00000       0xc0 ./Peripheral/src/ch32v30x_fsmc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_DeInit
                0x***********00000       0xa4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_AFIODeInit
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_StructInit
                0x***********00000        0xe ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadInputDataBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadInputData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadOutputDataBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ReadOutputData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_SetBits
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ResetBits
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_WriteBit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_Write
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_PinLockConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EventOutputConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EventOutputCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_PinRemapConfig
                0x***********00000       0xd6 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_EXTILineConfig
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_ETH_MediaInterfaceConfig
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_gpio.o
 .text.GPIO_IPD_Unused
                0x***********00000      0x1fc ./Peripheral/src/ch32v30x_gpio.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DeInit
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Init
                0x***********00000      0x10c ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_StructInit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Cmd  0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DMACmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DMALastTransferCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GenerateSTART
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GenerateSTOP
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_AcknowledgeConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_OwnAddress2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_DualAddressCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GeneralCallCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ITConfig
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SendData
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ReceiveData
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_Send7bitAddress
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ReadRegister
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SoftwareResetCmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_NACKPositionConfig
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_SMBusAlertConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_TransmitPEC
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_PECPositionConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_CalculatePEC
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetPEC
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ARPCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_StretchClockCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_FastModeDutyCycleConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_CheckEvent
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetLastEvent
                0x***********00000        0xe ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetFlagStatus
                0x***********00000       0x32 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_GetITStatus
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_i2c.o
 .text.I2C_ClearITPendingBit
                0x***********00000        0xc ./Peripheral/src/ch32v30x_i2c.o
 .debug_info    0x***********00000     0x1481 ./Peripheral/src/ch32v30x_i2c.o
 .debug_abbrev  0x***********00000      0x3a7 ./Peripheral/src/ch32v30x_i2c.o
 .debug_loc     0x***********00000      0x6cc ./Peripheral/src/ch32v30x_i2c.o
 .debug_aranges
                0x***********00000      0x110 ./Peripheral/src/ch32v30x_i2c.o
 .debug_ranges  0x***********00000      0x100 ./Peripheral/src/ch32v30x_i2c.o
 .debug_line    0x***********00000     0x113d ./Peripheral/src/ch32v30x_i2c.o
 .debug_str     0x***********00000      0xae0 ./Peripheral/src/ch32v30x_i2c.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_i2c.o
 .debug_frame   0x***********00000      0x264 ./Peripheral/src/ch32v30x_i2c.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_WriteAccessCmd
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_SetPrescaler
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_SetReload
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_ReloadCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_Enable
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_iwdg.o
 .text.IWDG_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_iwdg.o
 .debug_info    0x***********00000      0xb74 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_abbrev  0x***********00000      0x282 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_loc     0x***********00000       0x68 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_line    0x***********00000      0x3f9 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_str     0x***********00000      0x704 ./Peripheral/src/ch32v30x_iwdg.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_iwdg.o
 .debug_frame   0x***********00000       0x70 ./Peripheral/src/ch32v30x_iwdg.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_misc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_DeInit
                0x***********00000        0xa ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_Init
                0x***********00000       0x36 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_opa.o
 .text.OPA_Cmd  0x***********00000       0x2c ./Peripheral/src/ch32v30x_opa.o
 .debug_info    0x***********00000      0xb28 ./Peripheral/src/ch32v30x_opa.o
 .debug_abbrev  0x***********00000      0x252 ./Peripheral/src/ch32v30x_opa.o
 .debug_loc     0x***********00000       0x56 ./Peripheral/src/ch32v30x_opa.o
 .debug_aranges
                0x***********00000       0x38 ./Peripheral/src/ch32v30x_opa.o
 .debug_ranges  0x***********00000       0x28 ./Peripheral/src/ch32v30x_opa.o
 .debug_line    0x***********00000      0x462 ./Peripheral/src/ch32v30x_opa.o
 .debug_str     0x***********00000      0x6bb ./Peripheral/src/ch32v30x_opa.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_opa.o
 .debug_frame   0x***********00000       0x50 ./Peripheral/src/ch32v30x_opa.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_DeInit
                0x***********00000       0x2c ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_BackupAccessCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_PVDCmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_PVDLevelConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_WakeUpPinCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTOPMode
                0x***********00000       0x7c ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode
                0x***********00000       0x34 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_ClearFlag
                0x***********00000        0xe ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_LV
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_VBAT_EN
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTANDBYMode_RAM_LV_VBAT_EN
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_pwr.o
 .text.PWR_EnterSTOPMode_RAM_LV
                0x***********00000       0x82 ./Peripheral/src/ch32v30x_pwr.o
 .debug_info    0x***********00000     0x101d ./Peripheral/src/ch32v30x_pwr.o
 .debug_abbrev  0x***********00000      0x3aa ./Peripheral/src/ch32v30x_pwr.o
 .debug_loc     0x***********00000      0x278 ./Peripheral/src/ch32v30x_pwr.o
 .debug_aranges
                0x***********00000       0x88 ./Peripheral/src/ch32v30x_pwr.o
 .debug_ranges  0x***********00000       0xf8 ./Peripheral/src/ch32v30x_pwr.o
 .debug_line    0x***********00000      0xac5 ./Peripheral/src/ch32v30x_pwr.o
 .debug_str     0x***********00000      0x857 ./Peripheral/src/ch32v30x_pwr.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_pwr.o
 .debug_frame   0x***********00000       0xfc ./Peripheral/src/ch32v30x_pwr.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_DeInit
                0x***********00000       0x52 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HSEConfig
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_AdjustHSICalibrationValue
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HSICmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLLConfig
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLLCmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_SYSCLKConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetSYSCLKSource
                0x***********00000        0xa ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_HCLKConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PCLK1Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PCLK2Config
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ITConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_LSEConfig
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_LSICmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RTCCLKConfig
                0x***********00000        0xc ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RTCCLKCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_APB1PeriphResetCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_BackupResetCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClockSecuritySystemCmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_MCOConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetFlagStatus
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_WaitForHSEStartUp
                0x***********00000       0x42 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClearFlag
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_GetITStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PREDIV1Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PREDIV2Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL2Cmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL3Config
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_PLL3Cmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBFSCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_I2S2CLKConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_I2S3CLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_AHBPeriphResetCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ADCCLKADJcmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_RNGCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ETH1GCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_ETH1G_125Mcmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPLLCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPLLCKREFCLKConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBHSPHYPLLALIVEcmd
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rcc.o
 .text.RCC_USBCLK48MConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_rcc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_Cmd  0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetRandomNumber
                0x***********00000        0xa ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ITConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ClearFlag
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_GetITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rng.o
 .text.RNG_ClearITPendingBit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rng.o
 .debug_info    0x***********00000      0xb02 ./Peripheral/src/ch32v30x_rng.o
 .debug_abbrev  0x***********00000      0x2c8 ./Peripheral/src/ch32v30x_rng.o
 .debug_loc     0x***********00000       0xa8 ./Peripheral/src/ch32v30x_rng.o
 .debug_aranges
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_rng.o
 .debug_ranges  0x***********00000       0x38 ./Peripheral/src/ch32v30x_rng.o
 .debug_line    0x***********00000      0x42f ./Peripheral/src/ch32v30x_rng.o
 .debug_str     0x***********00000      0x68c ./Peripheral/src/ch32v30x_rng.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_rng.o
 .debug_frame   0x***********00000       0x8c ./Peripheral/src/ch32v30x_rng.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ITConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_EnterConfigMode
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ExitConfigMode
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetCounter
                0x***********00000       0x5a ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetCounter
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetPrescaler
                0x***********00000       0x3e ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_SetAlarm
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetDivider
                0x***********00000       0x60 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_WaitForLastTask
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_WaitForSynchro
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetFlagStatus
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ClearFlag
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_GetITStatus
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_rtc.o
 .text.RTC_ClearITPendingBit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_rtc.o
 .debug_info    0x***********00000      0xdd4 ./Peripheral/src/ch32v30x_rtc.o
 .debug_abbrev  0x***********00000      0x30b ./Peripheral/src/ch32v30x_rtc.o
 .debug_loc     0x***********00000      0x377 ./Peripheral/src/ch32v30x_rtc.o
 .debug_aranges
                0x***********00000       0x80 ./Peripheral/src/ch32v30x_rtc.o
 .debug_ranges  0x***********00000       0x70 ./Peripheral/src/ch32v30x_rtc.o
 .debug_line    0x***********00000      0x9a9 ./Peripheral/src/ch32v30x_rtc.o
 .debug_str     0x***********00000      0x806 ./Peripheral/src/ch32v30x_rtc.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_rtc.o
 .debug_frame   0x***********00000      0x12c ./Peripheral/src/ch32v30x_rtc.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DeInit
                0x***********00000       0x30 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_Init
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClockCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetPowerState
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetPowerState
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DMACmd
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendCommand
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CmdStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetCommandResponse
                0x***********00000        0xc ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetResponse
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DataConfig
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_DataStructInit
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetDataCounter
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ReadData
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_WriteData
                0x***********00000        0xa ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetFIFOCount
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StartSDIOReadWait
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_StopSDIOReadWait
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetSDIOReadWaitMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SetSDIOOperation
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendSDIOSuspendCmd
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CommandCompletionCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_CEATAITCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_SendCEATACmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetFlagStatus
                0x***********00000        0xe ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClearFlag
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_GetITStatus
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_sdio.o
 .text.SDIO_ClearITPendingBit
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_info    0x***********00000     0x10fd ./Peripheral/src/ch32v30x_sdio.o
 .debug_abbrev  0x***********00000      0x385 ./Peripheral/src/ch32v30x_sdio.o
 .debug_loc     0x***********00000      0x1bb ./Peripheral/src/ch32v30x_sdio.o
 .debug_aranges
                0x***********00000       0xf8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_ranges  0x***********00000       0xe8 ./Peripheral/src/ch32v30x_sdio.o
 .debug_line    0x***********00000      0xc9f ./Peripheral/src/ch32v30x_sdio.o
 .debug_str     0x***********00000      0xa6f ./Peripheral/src/ch32v30x_sdio.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_sdio.o
 .debug_frame   0x***********00000      0x204 ./Peripheral/src/ch32v30x_sdio.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_DeInit
                0x***********00000       0x70 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_Init
                0x***********00000       0x3e ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_Init
                0x***********00000       0xc6 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_StructInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_StructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_Cmd  0x***********00000       0x1a ./Peripheral/src/ch32v30x_spi.o
 .text.I2S_Cmd  0x***********00000       0x1a ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ITConfig
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_DMACmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_SendData
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ReceiveData
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_NSSInternalSoftwareConfig
                0x***********00000       0x24 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_SSOutputCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_DataSizeConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_TransmitCRC
                0x***********00000        0xa ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_CalculateCRC
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_GetCRC
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_GetCRCPolynomial
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_BiDirectionalLineConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_GetITStatus
                0x***********00000       0x28 ./Peripheral/src/ch32v30x_spi.o
 .text.SPI_I2S_ClearITPendingBit
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_spi.o
 .debug_info    0x***********00000     0x12cd ./Peripheral/src/ch32v30x_spi.o
 .debug_abbrev  0x***********00000      0x312 ./Peripheral/src/ch32v30x_spi.o
 .debug_loc     0x***********00000      0x598 ./Peripheral/src/ch32v30x_spi.o
 .debug_aranges
                0x***********00000       0xd0 ./Peripheral/src/ch32v30x_spi.o
 .debug_ranges  0x***********00000       0xc0 ./Peripheral/src/ch32v30x_spi.o
 .debug_line    0x***********00000      0xd34 ./Peripheral/src/ch32v30x_spi.o
 .debug_str     0x***********00000      0xa8f ./Peripheral/src/ch32v30x_spi.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_spi.o
 .debug_frame   0x***********00000      0x1a8 ./Peripheral/src/ch32v30x_spi.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_tim.o
 .text.TI1_Config
                0x***********00000       0x82 ./Peripheral/src/ch32v30x_tim.o
 .text.TI2_Config
                0x***********00000       0x9a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DeInit
                0x***********00000      0x138 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1Init
                0x***********00000       0x82 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2Init
                0x***********00000       0xae ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3Init
                0x***********00000       0xac ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4Init
                0x***********00000       0x88 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_BDTRConfig
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_TimeBaseStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OCStructInit
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ICStructInit
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_BDTRStructInit
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CtrlPWMOutputs
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GenerateEvent
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DMAConfig
                0x***********00000        0x8 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_DMACmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_InternalClockConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ITRxExternalClockConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_TIxExternalClockConfig
                0x***********00000       0x48 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRClockMode1Config
                0x***********00000       0x2a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ETRClockMode2Config
                0x***********00000       0x22 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_PrescalerConfig
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CounterModeConfig
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectInputTrigger
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_EncoderInterfaceConfig
                0x***********00000       0x3c ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC1Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC2Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC3Config
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ForcedOC4Config
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ARRPreloadConfig
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectCOM
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectCCDMA
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCPreloadControl
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1PreloadConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2PreloadConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3PreloadConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4PreloadConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1FastConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2FastConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3FastConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4FastConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC1Ref
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC2Ref
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC3Ref
                0x***********00000       0x10 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearOC4Ref
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1PolarityConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC1NPolarityConfig
                0x***********00000        0xe ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2PolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC2NPolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3PolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC3NPolarityConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_OC4PolarityConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCxCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_CCxNCmd
                0x***********00000       0x20 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOCxM
                0x***********00000       0x4c ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_UpdateDisableConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_UpdateRequestConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectHallSensor
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOnePulseMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectOutputTrigger
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectSlaveMode
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SelectMasterSlaveMode
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetAutoreload
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare1
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare2
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare3
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetCompare4
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC1Prescaler
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC2Prescaler
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_PWMIConfig
                0x***********00000       0x92 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC3Prescaler
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetIC4Prescaler
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ICInit
                0x***********00000      0x1ba ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_SetClockDivision
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture1
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture2
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture3
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCapture4
                0x***********00000        0x6 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetCounter
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetPrescaler
                0x***********00000        0x4 ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_tim.o
 .text.TIM_ClearFlag
                0x***********00000        0xc ./Peripheral/src/ch32v30x_tim.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_DeInit
                0x***********00000      0x112 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_StructInit
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClockInit
                0x***********00000       0x1e ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ClockStructInit
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_DMACmd
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetAddress
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_WakeUpConfig
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_ReceiverWakeUpCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_LINBreakDetectLengthConfig
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_LINCmd
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SendBreak
                0x***********00000        0xa ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetGuardTime
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SetPrescaler
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SmartCardCmd
                0x***********00000       0x1a ./Peripheral/src/ch32v30x_usart.o
 .text.USART_SmartCardNACKCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_HalfDuplexCmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_IrDAConfig
                0x***********00000       0x16 ./Peripheral/src/ch32v30x_usart.o
 .text.USART_IrDACmd
                0x***********00000       0x18 ./Peripheral/src/ch32v30x_usart.o
 .text          0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .data          0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .bss           0x***********00000        0x0 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_DeInit
                0x***********00000       0x2e ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetPrescaler
                0x***********00000       0x14 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetWindowValue
                0x***********00000       0x26 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_EnableIT
                0x***********00000       0x12 ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_SetCounter
                0x***********00000        0xe ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_Enable
                0x***********00000        0xe ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_GetFlagStatus
                0x***********00000        0xa ./Peripheral/src/ch32v30x_wwdg.o
 .text.WWDG_ClearFlag
                0x***********00000        0xa ./Peripheral/src/ch32v30x_wwdg.o
 .debug_info    0x***********00000      0xb28 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_abbrev  0x***********00000      0x29d ./Peripheral/src/ch32v30x_wwdg.o
 .debug_loc     0x***********00000       0xae ./Peripheral/src/ch32v30x_wwdg.o
 .debug_aranges
                0x***********00000       0x58 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_ranges  0x***********00000       0x48 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_line    0x***********00000      0x49d ./Peripheral/src/ch32v30x_wwdg.o
 .debug_str     0x***********00000      0x6b2 ./Peripheral/src/ch32v30x_wwdg.o
 .comment       0x***********00000       0x34 ./Peripheral/src/ch32v30x_wwdg.o
 .debug_frame   0x***********00000       0xa4 ./Peripheral/src/ch32v30x_wwdg.o
 .text          0x***********00000        0x0 ./FFT/kiss_fft.o
 .data          0x***********00000        0x0 ./FFT/kiss_fft.o
 .bss           0x***********00000        0x0 ./FFT/kiss_fft.o
 .text.kiss_fft_alloc
                0x***********00000      0x13e ./FFT/kiss_fft.o
 .text.kiss_fft_cleanup
                0x***********00000        0x2 ./FFT/kiss_fft.o
 .text.kiss_fft_next_fast_size
                0x***********00000       0x3e ./FFT/kiss_fft.o
 .rodata.kiss_fft_alloc.cst8
                0x***********00000        0x8 ./FFT/kiss_fft.o
 .text          0x***********00000        0x0 ./Debug/debug.o
 .data          0x***********00000        0x0 ./Debug/debug.o
 .bss           0x***********00000        0x0 ./Debug/debug.o
 .text.Delay_Us
                0x***********00000       0x3a ./Debug/debug.o
 .text.SDI_Printf_Enable
                0x***********00000       0x2a ./Debug/debug.o
 .text          0x***********00000        0x0 ./Core/core_riscv.o
 .data          0x***********00000        0x0 ./Core/core_riscv.o
 .bss           0x***********00000        0x0 ./Core/core_riscv.o
 .text.__get_FFLAGS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FFLAGS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_FRM
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FRM
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_FCSR
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_FCSR
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MSTATUS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MSTATUS
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MISA
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MISA
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MTVEC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MTVEC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MSCRATCH
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MSCRATCH
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MEPC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MEPC
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MCAUSE
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MCAUSE
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MTVAL
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__set_MTVAL
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MVENDORID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MARCHID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MIMPID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_MHARTID
                0x***********00000        0x6 ./Core/core_riscv.o
 .text.__get_SP
                0x***********00000        0x4 ./Core/core_riscv.o
 .debug_info    0x***********00000      0x46d ./Core/core_riscv.o
 .debug_abbrev  0x***********00000      0x10d ./Core/core_riscv.o
 .debug_aranges
                0x***********00000       0xe0 ./Core/core_riscv.o
 .debug_ranges  0x***********00000       0xd0 ./Core/core_riscv.o
 .debug_line    0x***********00000      0x53e ./Core/core_riscv.o
 .debug_str     0x***********00000      0x2e0 ./Core/core_riscv.o
 .comment       0x***********00000       0x34 ./Core/core_riscv.o
 .debug_frame   0x***********00000      0x1a0 ./Core/core_riscv.o
 .text          0x***********00000        0x0 ./APP/audio.o
 .data          0x***********00000        0x0 ./APP/audio.o
 .bss           0x***********00000        0x0 ./APP/audio.o
 .text          0x***********00000        0x0 ./APP/cry_detect.o
 .data          0x***********00000        0x0 ./APP/cry_detect.o
 .bss           0x***********00000        0x0 ./APP/cry_detect.o
 .text.agc_process
                0x***********00000       0x1e ./APP/cry_detect.o
 .text.extract_enhanced_features
                0x***********00000       0x20 ./APP/cry_detect.o
 .text.is_high_confidence_result
                0x***********00000       0x26 ./APP/cry_detect.o
 .text.debug_init
                0x***********00000       0x6e ./APP/cry_detect.o
 .text.performance_stats_init
                0x***********00000       0x52 ./APP/cry_detect.o
 .text.get_current_rms
                0x***********00000       0x28 ./APP/cry_detect.o
 .text.debug_print_system_status
                0x***********00000      0x166 ./APP/cry_detect.o
 .text.debug_print_features
                0x***********00000       0xea ./APP/cry_detect.o
 .text.debug_print_performance_stats
                0x***********00000      0x16a ./APP/cry_detect.o
 .text.debug_print_help
                0x***********00000      0x108 ./APP/cry_detect.o
 .bss.debug_command_buffer
                0x***********00000       0x80 ./APP/cry_detect.o
 .bss.debug_state
                0x***********00000       0x28 ./APP/cry_detect.o
 .bss.perf_stats
                0x***********00000       0x20 ./APP/cry_detect.o
 .rodata.debug_init.str1.4
                0x***********00000       0x40 ./APP/cry_detect.o
 .rodata.debug_print_features.str1.4
                0x***********00000       0xb3 ./APP/cry_detect.o
 .rodata.debug_print_help.str1.4
                0x***********00000      0x20a ./APP/cry_detect.o
 .rodata.debug_print_performance_stats.str1.4
                0x***********00000       0xf6 ./APP/cry_detect.o
 .rodata.debug_print_system_status.cst8
                0x***********00000        0x8 ./APP/cry_detect.o
 .rodata.debug_print_system_status.str1.4
                0x***********00000      0x12e ./APP/cry_detect.o
 .rodata.performance_stats_init.str1.4
                0x***********00000       0x24 ./APP/cry_detect.o
 .text          0x***********00000        0x0 ./APP/esp8266.o
 .data          0x***********00000        0x0 ./APP/esp8266.o
 .bss           0x***********00000        0x0 ./APP/esp8266.o
 .text          0x***********00000        0x0 ./APP/key.o
 .data          0x***********00000        0x0 ./APP/key.o
 .bss           0x***********00000        0x0 ./APP/key.o
 .text.key_init
                0x***********00000       0xba ./APP/key.o
 .text.key_read
                0x***********00000      0x28c ./APP/key.o
 .debug_info    0x***********00000      0xfa7 ./APP/key.o
 .debug_abbrev  0x***********00000      0x29a ./APP/key.o
 .debug_loc     0x***********00000       0x1f ./APP/key.o
 .debug_aranges
                0x***********00000       0x28 ./APP/key.o
 .debug_ranges  0x***********00000       0x18 ./APP/key.o
 .debug_line    0x***********00000      0x729 ./APP/key.o
 .debug_str     0x***********00000      0x775 ./APP/key.o
 .comment       0x***********00000       0x34 ./APP/key.o
 .debug_frame   0x***********00000       0x60 ./APP/key.o
 .text          0x***********00000        0x0 ./APP/lcd.o
 .data          0x***********00000        0x0 ./APP/lcd.o
 .bss           0x***********00000        0x0 ./APP/lcd.o
 .text.SPI_LCD_Init
                0x***********00000      0x118 ./APP/lcd.o
 .text.spi_readwrite
                0x***********00000       0x7a ./APP/lcd.o
 .text.LCD_WR_DATA8
                0x***********00000       0x4a ./APP/lcd.o
 .text.LCD_WR_DATA
                0x***********00000       0x58 ./APP/lcd.o
 .text.LCD_WR_REG
                0x***********00000       0x4a ./APP/lcd.o
 .text.LCD_Address_Set
                0x***********00000       0x86 ./APP/lcd.o
 .text.LCD_Init
                0x***********00000      0x43c ./APP/lcd.o
 .text.LCD_Fill
                0x***********00000       0x52 ./APP/lcd.o
 .text.LCD_DrawPoint
                0x***********00000       0x28 ./APP/lcd.o
 .text.LCD_DrawLine
                0x***********00000       0x90 ./APP/lcd.o
 .text.LCD_DrawRectangle
                0x***********00000       0x5a ./APP/lcd.o
 .text.Draw_Circle
                0x***********00000       0xfa ./APP/lcd.o
 .text.LCD_ShowChinese16x16
                0x***********00000       0xfe ./APP/lcd.o
 .text.LCD_ShowChinese24x24
                0x***********00000       0xfe ./APP/lcd.o
 .text.LCD_ShowChinese32x32
                0x***********00000       0xfe ./APP/lcd.o
 .text.LCD_ShowChinese
                0x***********00000       0x88 ./APP/lcd.o
 .text.lcd_show_chinese
                0x***********00000       0x54 ./APP/lcd.o
 .text.LCD_ShowChar
                0x***********00000       0xfe ./APP/lcd.o
 .text.LCD_ShowString
                0x***********00000       0x44 ./APP/lcd.o
 .text.mypow    0x***********00000       0x1a ./APP/lcd.o
 .text.LCD_ShowIntNum
                0x***********00000       0xa0 ./APP/lcd.o
 .text.LCD_ShowFloatNum1
                0x***********00000       0xd4 ./APP/lcd.o
 .text.LCD_ShowPicture
                0x***********00000       0x76 ./APP/lcd.o
 .rodata.LCD_ShowFloatNum1.cst4
                0x***********00000        0x4 ./APP/lcd.o
 .rodata.ascii_1608
                0x***********00000      0x5f0 ./APP/lcd.o
 .rodata.ascii_3216
                0x***********00000     0x17c0 ./APP/lcd.o
 .rodata.tfont16
                0x***********00000       0xaa ./APP/lcd.o
 .rodata.tfont24
                0x***********00000      0x172 ./APP/lcd.o
 .rodata.tfont32
                0x***********00000      0x28a ./APP/lcd.o
 .text          0x***********00000        0x0 ./APP/timer.o
 .data          0x***********00000        0x0 ./APP/timer.o
 .bss           0x***********00000        0x0 ./APP/timer.o
 .text.TIM2_PWM_Init
                0x***********00000       0xb2 ./APP/timer.o
 .text.pwm_lock
                0x***********00000       0x26 ./APP/timer.o
 .text          0x***********00000        0x0 ./APP/uart.o
 .data          0x***********00000        0x0 ./APP/uart.o
 .bss           0x***********00000        0x0 ./APP/uart.o
 .text.Uart8_SendByte
                0x***********00000       0x30 ./APP/uart.o
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
 .text.cos      0x***********00000       0xae d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
 .debug_frame   0x***********00000       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_cos.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .text.floor    0x***********00000      0x174 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .rodata.floor.cst8
                0x***********00000        0x8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .debug_frame   0x***********00000       0x4c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_floor.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
 .text.sin      0x***********00000       0xb0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
 .debug_frame   0x***********00000       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_sin.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .text.sqrt     0x***********00000       0xda d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .rodata.sqrt.str1.4
                0x***********00000        0x5 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .debug_frame   0x***********00000       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-w_sqrt.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .text.__ieee754_rem_pio2
                0x***********00000      0x522 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .rodata.__ieee754_rem_pio2.cst8
                0x***********00000       0x48 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .rodata.npio2_hw
                0x***********00000       0x80 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .rodata.two_over_pi
                0x***********00000      0x108 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .debug_frame   0x***********00000       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_rem_pio2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
 .text.__ieee754_sqrt
                0x***********00000      0x1e6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
 .debug_frame   0x***********00000       0x48 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-e_sqrt.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .text.__kernel_cos
                0x***********00000      0x354 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .rodata.__kernel_cos.cst8
                0x***********00000       0x50 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .debug_frame   0x***********00000       0x64 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_cos.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .text.__kernel_rem_pio2
                0x***********00000      0xa2e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .rodata.PIo2   0x***********00000       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .rodata.__kernel_rem_pio2.cst8
                0x***********00000       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .rodata.init_jk
                0x***********00000       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .debug_frame   0x***********00000       0x64 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_rem_pio2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .text.__kernel_sin
                0x***********00000      0x1f6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .rodata.__kernel_sin.cst8
                0x***********00000       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .debug_frame   0x***********00000       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-k_sin.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
 .text.fabs     0x***********00000        0x6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_fabs.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_lib_ver.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_lib_ver.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_lib_ver.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .text.scalbn   0x***********00000      0x15a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .rodata.scalbn.cst8
                0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .debug_frame   0x***********00000       0x64 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_scalbn.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
 .text.copysign
                0x***********00000       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_copysign.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
 .text.__adddf3
                0x***********00000      0x738 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
 .debug_frame   0x***********00000       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(adddf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
 .text.__eqdf2  0x***********00000       0x6a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqdf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
 .text.__gedf2  0x***********00000       0xae d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gedf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
 .text.__ledf2  0x***********00000       0xb6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(ledf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .text.__muldf3
                0x***********00000      0x4c8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .rodata.__muldf3
                0x***********00000       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .debug_frame   0x***********00000       0x54 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(muldf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
 .text.__subdf3
                0x***********00000      0x746 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
 .debug_frame   0x***********00000       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subdf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
 .text.__unorddf2
                0x***********00000       0x3c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unorddf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
 .text.__fixdfsi
                0x***********00000       0x68 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixdfsi.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
 .text.__floatsidf
                0x***********00000       0x76 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
 .debug_frame   0x***********00000       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsidf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .eh_frame      0x***********00000       0xf8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .text._printf_r
                0x***********00000       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
 .text.putc     0x***********00000       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .text._snprintf_r
                0x***********00000       0x6a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .text.snprintf
                0x***********00000       0x74 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .debug_frame   0x***********00000       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-snprintf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
 .text._sscanf_r
                0x***********00000       0x5c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .text.__swbuf  0x***********00000       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .text.fflush   0x***********00000       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock
                0x***********00000        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock
                0x***********00000        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text._cleanup
                0x***********00000       0x12 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_acquire
                0x***********00000        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_release
                0x***********00000        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_acquire
                0x***********00000        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_release
                0x***********00000        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock_all
                0x***********00000       0x1a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock_all
                0x***********00000       0x1a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .text._fwalk   0x***********00000       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .text.__ssputs_r
                0x***********00000       0xf8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .text.__ssprint_r
                0x***********00000      0x13e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .text._svfprintf_r
                0x***********00000      0x284 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .rodata._svfprintf_r.str1.4
                0x***********00000       0x13 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .debug_frame   0x***********00000       0xe0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfprintf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.__sprint_r
                0x***********00000       0x2c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.vfprintf
                0x***********00000       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text.cleanup_glue
                0x***********00000       0x2c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text._reclaim_reent
                0x***********00000      0x10c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .text.strtol_l
                0x***********00000       0x1a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .text.strtol   0x***********00000       0x26 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .text.strtoul_l
                0x***********00000       0x1a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .text.strtoul  0x***********00000       0x26 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .text._ungetc_r
                0x***********00000      0x11e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .text.ungetc   0x***********00000       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .text.__sfvwrite_r
                0x***********00000      0x306 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .debug_frame   0x***********00000       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fvwrite.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .text._setlocale_r
                0x***********00000       0x64 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x***********00000       0x1c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .text.setlocale
                0x***********00000       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .sbss._PathLocale
                0x***********00000        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x***********00000       0x1c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .text.memmove  0x***********00000       0x46 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .debug_frame   0x***********00000       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memmove.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .text          0x***********00000      0x11a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strcmp.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strcmp.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strcmp.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x***********00000       0x1c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ctype_.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ctype_.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ctype_.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .text          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
 .data          0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
 .bss           0x***********00000        0x0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x***********00000 0x***********48000 xr
RAM              0x0000000020000000 0x***********08000 xrw
*default*        0x***********00000 0xffffffffffffffff

Linker script and memory map

LOAD ./User/ch32v30x_it.o
LOAD ./User/main.o
LOAD ./User/system_ch32v30x.o
LOAD ./Startup/startup_ch32v30x_D8C.o
LOAD ./Peripheral/src/ch32v30x_adc.o
LOAD ./Peripheral/src/ch32v30x_bkp.o
LOAD ./Peripheral/src/ch32v30x_can.o
LOAD ./Peripheral/src/ch32v30x_crc.o
LOAD ./Peripheral/src/ch32v30x_dac.o
LOAD ./Peripheral/src/ch32v30x_dbgmcu.o
LOAD ./Peripheral/src/ch32v30x_dma.o
LOAD ./Peripheral/src/ch32v30x_dvp.o
LOAD ./Peripheral/src/ch32v30x_eth.o
LOAD ./Peripheral/src/ch32v30x_exti.o
LOAD ./Peripheral/src/ch32v30x_flash.o
LOAD ./Peripheral/src/ch32v30x_fsmc.o
LOAD ./Peripheral/src/ch32v30x_gpio.o
LOAD ./Peripheral/src/ch32v30x_i2c.o
LOAD ./Peripheral/src/ch32v30x_iwdg.o
LOAD ./Peripheral/src/ch32v30x_misc.o
LOAD ./Peripheral/src/ch32v30x_opa.o
LOAD ./Peripheral/src/ch32v30x_pwr.o
LOAD ./Peripheral/src/ch32v30x_rcc.o
LOAD ./Peripheral/src/ch32v30x_rng.o
LOAD ./Peripheral/src/ch32v30x_rtc.o
LOAD ./Peripheral/src/ch32v30x_sdio.o
LOAD ./Peripheral/src/ch32v30x_spi.o
LOAD ./Peripheral/src/ch32v30x_tim.o
LOAD ./Peripheral/src/ch32v30x_usart.o
LOAD ./Peripheral/src/ch32v30x_wwdg.o
LOAD ./FFT/kiss_fft.o
LOAD ./Debug/debug.o
LOAD ./Core/core_riscv.o
LOAD ./APP/audio.o
LOAD ./APP/cry_detect.o
LOAD ./APP/esp8266.o
LOAD ./APP/key.o
LOAD ./APP/lcd.o
LOAD ./APP/timer.o
LOAD ./APP/uart.o
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
START GROUP
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a
END GROUP
START GROUP
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libc_nano.a
LOAD d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a
END GROUP
                0x***********00800                __stack_size = 0x800
                [!provide]                        PROVIDE (_stack_size = __stack_size)

.init           0x***********00000        0x4
                0x***********00000                _sinit = .
                0x***********00000                . = ALIGN (0x4)
 *(SORT_NONE(.init))
 .init          0x***********00000        0x4 ./Startup/startup_ch32v30x_D8C.o
                0x***********00000                _start
                0x***********00004                . = ALIGN (0x4)
                0x***********00004                _einit = .

.vector         0x***********00004      0x1bc
 *(.vector)
 .vector        0x***********00004      0x1a0 ./Startup/startup_ch32v30x_D8C.o
                0x***********001c0                . = ALIGN (0x40)
 *fill*         0x***********001a4       0x1c 

.text           0x***********001c0     0x8744
                0x***********001c0                . = ALIGN (0x4)
 *(.text)
 .text          0x***********001c0       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(save-restore.o)
                0x***********001c0                __riscv_save_12
                0x***********001c8                __riscv_save_9
                0x***********001c8                __riscv_save_11
                0x***********001c8                __riscv_save_10
                0x***********001c8                __riscv_save_8
                0x***********001d6                __riscv_save_4
                0x***********001d6                __riscv_save_6
                0x***********001d6                __riscv_save_5
                0x***********001d6                __riscv_save_7
                0x***********001f0                __riscv_save_3
                0x***********001f0                __riscv_save_2
                0x***********001f0                __riscv_save_1
                0x***********001f0                __riscv_save_0
                0x***********001fc                __riscv_restore_12
                0x***********00200                __riscv_restore_11
                0x***********00200                __riscv_restore_9
                0x***********00200                __riscv_restore_10
                0x***********00200                __riscv_restore_8
                0x***********0020a                __riscv_restore_5
                0x***********0020a                __riscv_restore_7
                0x***********0020a                __riscv_restore_6
                0x***********0020a                __riscv_restore_4
                0x***********00214                __riscv_restore_3
                0x***********00214                __riscv_restore_0
                0x***********00214                __riscv_restore_2
                0x***********00214                __riscv_restore_1
 .text          0x***********00220       0xa8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memset.o)
                0x***********00220                memset
 *(.text.*)
 .text.NMI_Handler
                0x***********002c8        0x2 ./User/ch32v30x_it.o
                0x***********002c8                NMI_Handler
 .text.HardFault_Handler
                0x***********002ca        0x2 ./User/ch32v30x_it.o
                0x***********002ca                HardFault_Handler
 .text.DMA1_Channel1_IRQHandler
                0x***********002cc       0x20 ./User/ch32v30x_it.o
                0x***********002cc                DMA1_Channel1_IRQHandler
 .text.esp8266_proc
                0x***********002ec       0x7c ./User/main.o
                0x***********002ec                esp8266_proc
 .text.tianwen_proc
                0x***********00368       0x70 ./User/main.o
                0x***********00368                tianwen_proc
 .text.noise_status_proc
                0x***********003d8      0x1a6 ./User/main.o
                0x***********003d8                noise_status_proc
 .text.TIM3_IRQHandler
                0x***********0057e       0x40 ./User/main.o
                0x***********0057e                TIM3_IRQHandler
 .text.scheduler_run
                0x***********005be       0x3c ./User/main.o
                0x***********005be                scheduler_run
 .text.UART6_IRQHandler
                0x***********005fa       0x4c ./User/main.o
                0x***********005fa                UART6_IRQHandler
 .text.UART8_IRQHandler
                0x***********00646       0x3c ./User/main.o
                0x***********00646                UART8_IRQHandler
 .text.USART3_IRQHandler
                0x***********00682       0x44 ./User/main.o
                0x***********00682                USART3_IRQHandler
 .text.startup.main
                0x***********006c6       0xb4 ./User/main.o
                0x***********006c6                main
 .text.SystemInit
                0x***********0077a       0xfa ./User/system_ch32v30x.o
                0x***********0077a                SystemInit
 .text.SystemCoreClockUpdate
                0x***********00874      0x12a ./User/system_ch32v30x.o
                0x***********00874                SystemCoreClockUpdate
 .text.vector_handler
                0x***********0099e        0x2 ./Startup/startup_ch32v30x_D8C.o
                0x***********0099e                EXTI2_IRQHandler
                0x***********0099e                TIM8_TRG_COM_IRQHandler
                0x***********0099e                TIM8_CC_IRQHandler
                0x***********0099e                TIM1_CC_IRQHandler
                0x***********0099e                TIM6_IRQHandler
                0x***********0099e                SysTick_Handler
                0x***********0099e                PVD_IRQHandler
                0x***********0099e                SDIO_IRQHandler
                0x***********0099e                TIM9_BRK_IRQHandler
                0x***********0099e                DMA2_Channel8_IRQHandler
                0x***********0099e                CAN2_RX1_IRQHandler
                0x***********0099e                EXTI3_IRQHandler
                0x***********0099e                USBHS_IRQHandler
                0x***********0099e                DMA2_Channel9_IRQHandler
                0x***********0099e                TIM10_CC_IRQHandler
                0x***********0099e                USBFS_IRQHandler
                0x***********0099e                EXTI0_IRQHandler
                0x***********0099e                I2C2_EV_IRQHandler
                0x***********0099e                TIM10_TRG_COM_IRQHandler
                0x***********0099e                CAN2_SCE_IRQHandler
                0x***********0099e                ADC1_2_IRQHandler
                0x***********0099e                Break_Point_Handler
                0x***********0099e                SPI1_IRQHandler
                0x***********0099e                TAMPER_IRQHandler
                0x***********0099e                CAN2_RX0_IRQHandler
                0x***********0099e                TIM8_UP_IRQHandler
                0x***********0099e                Ecall_M_Mode_Handler
                0x***********0099e                DMA2_Channel2_IRQHandler
                0x***********0099e                DMA1_Channel4_IRQHandler
                0x***********0099e                TIM9_UP_IRQHandler
                0x***********0099e                RTC_IRQHandler
                0x***********0099e                DMA1_Channel7_IRQHandler
                0x***********0099e                CAN1_RX1_IRQHandler
                0x***********0099e                DVP_IRQHandler
                0x***********0099e                UART5_IRQHandler
                0x***********0099e                TIM4_IRQHandler
                0x***********0099e                DMA2_Channel1_IRQHandler
                0x***********0099e                I2C1_EV_IRQHandler
                0x***********0099e                DMA1_Channel6_IRQHandler
                0x***********0099e                UART4_IRQHandler
                0x***********0099e                DMA2_Channel4_IRQHandler
                0x***********0099e                RCC_IRQHandler
                0x***********0099e                TIM1_TRG_COM_IRQHandler
                0x***********0099e                DMA2_Channel7_IRQHandler
                0x***********0099e                EXTI15_10_IRQHandler
                0x***********0099e                TIM7_IRQHandler
                0x***********0099e                CAN2_TX_IRQHandler
                0x***********0099e                TIM5_IRQHandler
                0x***********0099e                EXTI9_5_IRQHandler
                0x***********0099e                ETH_WKUP_IRQHandler
                0x***********0099e                SPI2_IRQHandler
                0x***********0099e                TIM10_BRK_IRQHandler
                0x***********0099e                TIM9_CC_IRQHandler
                0x***********0099e                DMA2_Channel5_IRQHandler
                0x***********0099e                DMA1_Channel5_IRQHandler
                0x***********0099e                EXTI4_IRQHandler
                0x***********0099e                USB_LP_CAN1_RX0_IRQHandler
                0x***********0099e                RNG_IRQHandler
                0x***********0099e                USB_HP_CAN1_TX_IRQHandler
                0x***********0099e                DMA1_Channel3_IRQHandler
                0x***********0099e                ETH_IRQHandler
                0x***********0099e                TIM1_UP_IRQHandler
                0x***********0099e                WWDG_IRQHandler
                0x***********0099e                USBHSWakeup_IRQHandler
                0x***********0099e                DMA2_Channel11_IRQHandler
                0x***********0099e                Ecall_U_Mode_Handler
                0x***********0099e                DMA2_Channel6_IRQHandler
                0x***********0099e                TIM2_IRQHandler
                0x***********0099e                SW_Handler
                0x***********0099e                TIM1_BRK_IRQHandler
                0x***********0099e                DMA2_Channel10_IRQHandler
                0x***********0099e                EXTI1_IRQHandler
                0x***********0099e                RTCAlarm_IRQHandler
                0x***********0099e                TIM10_UP_IRQHandler
                0x***********0099e                TIM9_TRG_COM_IRQHandler
                0x***********0099e                UART7_IRQHandler
                0x***********0099e                USART2_IRQHandler
                0x***********0099e                I2C2_ER_IRQHandler
                0x***********0099e                DMA1_Channel2_IRQHandler
                0x***********0099e                TIM8_BRK_IRQHandler
                0x***********0099e                CAN1_SCE_IRQHandler
                0x***********0099e                FLASH_IRQHandler
                0x***********0099e                USART1_IRQHandler
                0x***********0099e                I2C1_ER_IRQHandler
                0x***********0099e                USBWakeUp_IRQHandler
                0x***********0099e                DMA2_Channel3_IRQHandler
 .text.handle_reset
                0x***********009a0       0x8e ./Startup/startup_ch32v30x_D8C.o
                0x***********009a0                handle_reset
 .text.ADC_DeInit
                0x***********00a2e       0x46 ./Peripheral/src/ch32v30x_adc.o
                0x***********00a2e                ADC_DeInit
 .text.ADC_Init
                0x***********00a74       0x56 ./Peripheral/src/ch32v30x_adc.o
                0x***********00a74                ADC_Init
 .text.ADC_Cmd  0x***********00aca       0x10 ./Peripheral/src/ch32v30x_adc.o
                0x***********00aca                ADC_Cmd
 .text.ADC_DMACmd
                0x***********00ada       0x12 ./Peripheral/src/ch32v30x_adc.o
                0x***********00ada                ADC_DMACmd
 .text.ADC_ResetCalibration
                0x***********00aec        0xa ./Peripheral/src/ch32v30x_adc.o
                0x***********00aec                ADC_ResetCalibration
 .text.ADC_GetResetCalibrationStatus
                0x***********00af6        0x8 ./Peripheral/src/ch32v30x_adc.o
                0x***********00af6                ADC_GetResetCalibrationStatus
 .text.ADC_StartCalibration
                0x***********00afe        0xa ./Peripheral/src/ch32v30x_adc.o
                0x***********00afe                ADC_StartCalibration
 .text.ADC_GetCalibrationStatus
                0x***********00b08        0x8 ./Peripheral/src/ch32v30x_adc.o
                0x***********00b08                ADC_GetCalibrationStatus
 .text.ADC_SoftwareStartConvCmd
                0x***********00b10       0x18 ./Peripheral/src/ch32v30x_adc.o
                0x***********00b10                ADC_SoftwareStartConvCmd
 .text.ADC_RegularChannelConfig
                0x***********00b28       0xb8 ./Peripheral/src/ch32v30x_adc.o
                0x***********00b28                ADC_RegularChannelConfig
 .text.ADC_BufferCmd
                0x***********00be0       0x18 ./Peripheral/src/ch32v30x_adc.o
                0x***********00be0                ADC_BufferCmd
 .text.Get_CalibrationValue
                0x***********00bf8      0x130 ./Peripheral/src/ch32v30x_adc.o
                0x***********00bf8                Get_CalibrationValue
 .text.DBGMCU_GetCHIPID
                0x***********00d28        0xa ./Peripheral/src/ch32v30x_dbgmcu.o
                0x***********00d28                DBGMCU_GetCHIPID
 .text.DMA_DeInit
                0x***********00d32      0x16c ./Peripheral/src/ch32v30x_dma.o
                0x***********00d32                DMA_DeInit
 .text.DMA_Init
                0x***********00e9e       0x38 ./Peripheral/src/ch32v30x_dma.o
                0x***********00e9e                DMA_Init
 .text.DMA_Cmd  0x***********00ed6       0x14 ./Peripheral/src/ch32v30x_dma.o
                0x***********00ed6                DMA_Cmd
 .text.DMA_GetFlagStatus
                0x***********00eea       0x32 ./Peripheral/src/ch32v30x_dma.o
                0x***********00eea                DMA_GetFlagStatus
 .text.DMA_ClearFlag
                0x***********00f1c       0x2c ./Peripheral/src/ch32v30x_dma.o
                0x***********00f1c                DMA_ClearFlag
 .text.DMA_GetITStatus
                0x***********00f48        0xa ./Peripheral/src/ch32v30x_dma.o
                0x***********00f48                DMA_GetITStatus
 .text.DMA_ClearITPendingBit
                0x***********00f52        0xa ./Peripheral/src/ch32v30x_dma.o
                0x***********00f52                DMA_ClearITPendingBit
 .text.GPIO_Init
                0x***********00f5c       0xc0 ./Peripheral/src/ch32v30x_gpio.o
                0x***********00f5c                GPIO_Init
 .text.NVIC_PriorityGroupConfig
                0x***********0101c        0x6 ./Peripheral/src/ch32v30x_misc.o
                0x***********0101c                NVIC_PriorityGroupConfig
 .text.NVIC_Init
                0x***********01022       0x4e ./Peripheral/src/ch32v30x_misc.o
                0x***********01022                NVIC_Init
 .text.RCC_ADCCLKConfig
                0x***********01070       0x12 ./Peripheral/src/ch32v30x_rcc.o
                0x***********01070                RCC_ADCCLKConfig
 .text.RCC_GetClocksFreq
                0x***********01082      0x176 ./Peripheral/src/ch32v30x_rcc.o
                0x***********01082                RCC_GetClocksFreq
 .text.RCC_AHBPeriphClockCmd
                0x***********011f8       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********011f8                RCC_AHBPeriphClockCmd
 .text.RCC_APB2PeriphClockCmd
                0x***********01216       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********01216                RCC_APB2PeriphClockCmd
 .text.RCC_APB1PeriphClockCmd
                0x***********01234       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********01234                RCC_APB1PeriphClockCmd
 .text.RCC_APB2PeriphResetCmd
                0x***********01252       0x1e ./Peripheral/src/ch32v30x_rcc.o
                0x***********01252                RCC_APB2PeriphResetCmd
 .text.TIM_TimeBaseInit
                0x***********01270       0xaa ./Peripheral/src/ch32v30x_tim.o
                0x***********01270                TIM_TimeBaseInit
 .text.TIM_Cmd  0x***********0131a       0x18 ./Peripheral/src/ch32v30x_tim.o
                0x***********0131a                TIM_Cmd
 .text.TIM_ITConfig
                0x***********01332       0x12 ./Peripheral/src/ch32v30x_tim.o
                0x***********01332                TIM_ITConfig
 .text.TIM_GetITStatus
                0x***********01344       0x18 ./Peripheral/src/ch32v30x_tim.o
                0x***********01344                TIM_GetITStatus
 .text.TIM_ClearITPendingBit
                0x***********0135c        0xc ./Peripheral/src/ch32v30x_tim.o
                0x***********0135c                TIM_ClearITPendingBit
 .text.USART_Init
                0x***********01368       0x8e ./Peripheral/src/ch32v30x_usart.o
                0x***********01368                USART_Init
 .text.USART_Cmd
                0x***********013f6       0x16 ./Peripheral/src/ch32v30x_usart.o
                0x***********013f6                USART_Cmd
 .text.USART_ITConfig
                0x***********0140c       0x36 ./Peripheral/src/ch32v30x_usart.o
                0x***********0140c                USART_ITConfig
 .text.USART_SendData
                0x***********01442        0x8 ./Peripheral/src/ch32v30x_usart.o
                0x***********01442                USART_SendData
 .text.USART_ReceiveData
                0x***********0144a        0x8 ./Peripheral/src/ch32v30x_usart.o
                0x***********0144a                USART_ReceiveData
 .text.USART_GetFlagStatus
                0x***********01452        0xa ./Peripheral/src/ch32v30x_usart.o
                0x***********01452                USART_GetFlagStatus
 .text.USART_ClearFlag
                0x***********0145c        0xc ./Peripheral/src/ch32v30x_usart.o
                0x***********0145c                USART_ClearFlag
 .text.USART_GetITStatus
                0x***********01468       0x3c ./Peripheral/src/ch32v30x_usart.o
                0x***********01468                USART_GetITStatus
 .text.USART_ClearITPendingBit
                0x***********014a4       0x14 ./Peripheral/src/ch32v30x_usart.o
                0x***********014a4                USART_ClearITPendingBit
 .text.kf_work  0x***********014b8      0x9c4 ./FFT/kiss_fft.o
 .text.kiss_fft_stride
                0x***********01e7c       0xa6 ./FFT/kiss_fft.o
                0x***********01e7c                kiss_fft_stride
 .text.kiss_fft
                0x***********01f22        0xc ./FFT/kiss_fft.o
                0x***********01f22                kiss_fft
 .text.Delay_Init
                0x***********01f2e       0x2a ./Debug/debug.o
                0x***********01f2e                Delay_Init
 .text.Delay_Ms
                0x***********01f58       0x36 ./Debug/debug.o
                0x***********01f58                Delay_Ms
 .text.USART_Printf_Init
                0x***********01f8e       0x5a ./Debug/debug.o
                0x***********01f8e                USART_Printf_Init
 .text._write   0x***********01fe8       0x3e ./Debug/debug.o
                0x***********01fe8                _write
 .text._sbrk    0x***********02026       0x2a ./Debug/debug.o
                0x***********02026                _sbrk
 .text.audio_init
                0x***********02050        0xc ./APP/audio.o
                0x***********02050                audio_init
 .text.audio_play
                0x***********0205c       0x4c ./APP/audio.o
                0x***********0205c                audio_play
 .text.audio_yinliang
                0x***********020a8       0x4e ./APP/audio.o
                0x***********020a8                audio_yinliang
 .text.uart_flush
                0x***********020f6       0x20 ./APP/cry_detect.o
 .text.calculate_rms.part.2
                0x***********02116       0x50 ./APP/cry_detect.o
 .text.ADC_Function_Init
                0x***********02166       0xca ./APP/cry_detect.o
                0x***********02166                ADC_Function_Init
 .text.DMA_Tx_Init
                0x***********02230       0x60 ./APP/cry_detect.o
                0x***********02230                DMA_Tx_Init
 .text.Get_ConversionVal
                0x***********02290       0x2a ./APP/cry_detect.o
                0x***********02290                Get_ConversionVal
 .text.cry_get_status
                0x***********022ba        0xa ./APP/cry_detect.o
                0x***********022ba                cry_get_status
 .text.cry_get_type
                0x***********022c4        0xa ./APP/cry_detect.o
                0x***********022c4                cry_get_type
 .text.noise_estimation_init
                0x***********022ce       0x4c ./APP/cry_detect.o
                0x***********022ce                noise_estimation_init
 .text.noise_estimation_proc
                0x***********0231a       0xf8 ./APP/cry_detect.o
                0x***********0231a                noise_estimation_proc
 .text.get_noise_baseline
                0x***********02412        0x6 ./APP/cry_detect.o
                0x***********02412                get_noise_baseline
 .text.get_adaptive_energy_threshold
                0x***********02418       0x38 ./APP/cry_detect.o
                0x***********02418                get_adaptive_energy_threshold
 .text.dynamic_threshold_init
                0x***********02450       0x38 ./APP/cry_detect.o
                0x***********02450                dynamic_threshold_init
 .text.dynamic_threshold_update
                0x***********02488       0xf4 ./APP/cry_detect.o
                0x***********02488                dynamic_threshold_update
 .text.get_current_energy_threshold
                0x***********0257c        0x6 ./APP/cry_detect.o
                0x***********0257c                get_current_energy_threshold
 .text.get_current_zcr_threshold
                0x***********02582        0x6 ./APP/cry_detect.o
                0x***********02582                get_current_zcr_threshold
 .text.agc_init
                0x***********02588       0x3c ./APP/cry_detect.o
                0x***********02588                agc_init
 .text.calculate_rms
                0x***********025c4       0x1c ./APP/cry_detect.o
                0x***********025c4                calculate_rms
 .text.agc_process.part.3
                0x***********025e0      0x134 ./APP/cry_detect.o
 .text.get_current_gain
                0x***********02714        0x6 ./APP/cry_detect.o
                0x***********02714                get_current_gain
 .text.calibration_start
                0x***********0271a       0x5e ./APP/cry_detect.o
                0x***********0271a                calibration_start
 .text.calibration_is_active
                0x***********02778        0x6 ./APP/cry_detect.o
                0x***********02778                calibration_is_active
 .text.calibration_load_from_flash
                0x***********0277e       0x44 ./APP/cry_detect.o
                0x***********0277e                calibration_load_from_flash
 .text.calibration_save_to_flash
                0x***********027c2       0x3c ./APP/cry_detect.o
                0x***********027c2                calibration_save_to_flash
 .text.calibration_process
                0x***********027fe      0x1da ./APP/cry_detect.o
                0x***********027fe                calibration_process
 .text.calibration_reset_to_default
                0x***********029d8       0x50 ./APP/cry_detect.o
                0x***********029d8                calibration_reset_to_default
 .text.calibration_init
                0x***********02a28       0x4c ./APP/cry_detect.o
                0x***********02a28                calibration_init
 .text.enhanced_features_init
                0x***********02a74       0x66 ./APP/cry_detect.o
                0x***********02a74                enhanced_features_init
 .text.calculate_spectral_centroid
                0x***********02ada       0x92 ./APP/cry_detect.o
                0x***********02ada                calculate_spectral_centroid
 .text.calculate_spectral_bandwidth
                0x***********02b6c       0xa4 ./APP/cry_detect.o
                0x***********02b6c                calculate_spectral_bandwidth
 .text.calculate_energy_variance
                0x***********02c10       0x80 ./APP/cry_detect.o
                0x***********02c10                calculate_energy_variance
 .text.calculate_spectral_rolloff
                0x***********02c90       0xc2 ./APP/cry_detect.o
                0x***********02c90                calculate_spectral_rolloff
 .text.extract_enhanced_features.part.9
                0x***********02d52      0x12c ./APP/cry_detect.o
 .text.classify_cry_with_enhanced_features
                0x***********02e7e      0x134 ./APP/cry_detect.o
                0x***********02e7e                classify_cry_with_enhanced_features
 .text.confidence_init
                0x***********02fb2       0x40 ./APP/cry_detect.o
                0x***********02fb2                confidence_init
 .text.cry_detect_init
                0x***********02ff2      0x12c ./APP/cry_detect.o
                0x***********02ff2                cry_detect_init
 .text.evaluate_feature_confidence
                0x***********0311e      0x140 ./APP/cry_detect.o
                0x***********0311e                evaluate_feature_confidence
 .text.calculate_confidence
                0x***********0325e       0xa2 ./APP/cry_detect.o
                0x***********0325e                calculate_confidence
 .text.update_confidence_history
                0x***********03300       0x6e ./APP/cry_detect.o
                0x***********03300                update_confidence_history
 .text.get_average_confidence
                0x***********0336e       0x44 ./APP/cry_detect.o
                0x***********0336e                get_average_confidence
 .text.process_classification_with_confidence
                0x***********033b2      0x144 ./APP/cry_detect.o
                0x***********033b2                process_classification_with_confidence
 .text.cry_detect_proc
                0x***********034f6      0x546 ./APP/cry_detect.o
                0x***********034f6                cry_detect_proc
 .text.esp8266_init
                0x***********03a3c        0xa ./APP/esp8266.o
                0x***********03a3c                esp8266_init
 .text.onenet_init
                0x***********03a46      0x130 ./APP/esp8266.o
                0x***********03a46                onenet_init
 .text.SPI3_IRQHandler
                0x***********03b76        0x4 ./APP/lcd.o
                0x***********03b76                SPI3_IRQHandler
 .text.Tim3_Init
                0x***********03b7a       0x5a ./APP/timer.o
                0x***********03b7a                Tim3_Init
 .text.Usart3_Init
                0x***********03bd4       0x96 ./APP/uart.o
                0x***********03bd4                Usart3_Init
 .text.Uart8_Init
                0x***********03c6a       0x88 ./APP/uart.o
                0x***********03c6a                Uart8_Init
 .text.Uart6_Init
                0x***********03cf2       0x8a ./APP/uart.o
                0x***********03cf2                Uart6_Init
 .text.uart6_send_string
                0x***********03d7c       0x32 ./APP/uart.o
                0x***********03d7c                uart6_send_string
 .text.usart1_send_string
                0x***********03dae       0x32 ./APP/uart.o
                0x***********03dae                usart1_send_string
 .text.uart8_send_string
                0x***********03de0       0x2e ./APP/uart.o
                0x***********03de0                uart8_send_string
 .text.sqrtf    0x***********03e0e       0xb4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
                0x***********03e0e                sqrtf
 .text.__ieee754_sqrtf
                0x***********03ec2       0xda d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
                0x***********03ec2                __ieee754_sqrtf
 .text.matherr  0x***********03f9c        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
                0x***********03f9c                matherr
 .text.__divdf3
                0x***********03fa0      0x5b4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
                0x***********03fa0                __divdf3
 .text.__addsf3
                0x***********04554      0x36a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
                0x***********04554                __addsf3
 .text.__divsf3
                0x***********048be      0x2b2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
                0x***********048be                __divsf3
 .text.__eqsf2  0x***********04b70       0x5c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
                0x***********04b70                __eqsf2
                0x***********04b70                __nesf2
 .text.__gesf2  0x***********04bcc       0x86 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
                0x***********04bcc                __gesf2
                0x***********04bcc                __gtsf2
 .text.__lesf2  0x***********04c52       0x8c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
                0x***********04c52                __ltsf2
                0x***********04c52                __lesf2
 .text.__mulsf3
                0x***********04cde      0x294 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
                0x***********04cde                __mulsf3
 .text.__subsf3
                0x***********04f72      0x388 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
                0x***********04f72                __subsf3
 .text.__unordsf2
                0x***********052fa       0x3c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
                0x***********052fa                __unordsf2
 .text.__fixsfsi
                0x***********05336       0x62 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
                0x***********05336                __fixsfsi
 .text.__fixunssfsi
                0x***********05398       0x56 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
                0x***********05398                __fixunssfsi
 .text.__floatsisf
                0x***********053ee       0xc6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
                0x***********053ee                __floatsisf
 .text.__floatunsisf
                0x***********054b4       0xce d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
                0x***********054b4                __floatunsisf
 .text.__extendsfdf2
                0x***********05582       0xac d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
                0x***********05582                __extendsfdf2
 .text.__truncdfsf2
                0x***********0562e      0x16a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
                0x***********0562e                __truncdfsf2
 .text.__clzsi2
                0x***********05798       0x6e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
                0x***********05798                __clzsi2
 .text.__errno  0x***********05806        0x8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
                0x***********05806                __errno
 .text._fputc_r
                0x***********0580e       0x52 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
                0x***********0580e                _fputc_r
 .text.fputc    0x***********05860        0xe d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
                0x***********05860                fputc
 .text._fputs_r
                0x***********0586e       0xac d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
                0x***********0586e                _fputs_r
 .text.fputs    0x***********0591a        0xe d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
                0x***********0591a                fputs
 .text.malloc   0x***********05928        0xa d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
                0x***********05928                malloc
 .text.free     0x***********05932        0xa d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
                0x***********05932                free
 .text.memcpy   0x***********0593c       0xb2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
                0x***********0593c                memcpy
 .text._free_r  0x***********059ee       0xa8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
                0x***********059ee                _free_r
 .text._malloc_r
                0x***********05a96       0xd4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x***********05a96                _malloc_r
 .text.printf   0x***********05b6a       0x42 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
                0x***********05b6a                iprintf
                0x***********05b6a                printf
 .text._putc_r  0x***********05bac       0x82 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
                0x***********05bac                _putc_r
 .text._puts_r  0x***********05c2e       0xd4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                0x***********05c2e                _puts_r
 .text.puts     0x***********05d02        0xc d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
                0x***********05d02                puts
 .text._sbrk_r  0x***********05d0e       0x32 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
                0x***********05d0e                _sbrk_r
 .text.sscanf   0x***********05d40       0x52 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
                0x***********05d40                siscanf
                0x***********05d40                sscanf
 .text.__sread  0x***********05d92       0x2e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********05d92                __sread
 .text.__seofread
                0x***********05dc0        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********05dc0                __seofread
 .text.__swrite
                0x***********05dc4       0x48 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********05dc4                __swrite
 .text.__sseek  0x***********05e0c       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********05e0c                __sseek
 .text.__sclose
                0x***********05e3c        0x6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
                0x***********05e3c                __sclose
 .text.strlen   0x***********05e42       0x12 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
                0x***********05e42                strlen
 .text.strncmp  0x***********05e54       0x28 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
                0x***********05e54                strncmp
 .text.strstr   0x***********05e7c       0x34 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
                0x***********05e7c                strstr
 .text.__swbuf_r
                0x***********05eb0       0xbc d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
                0x***********05eb0                __swbuf_r
 .text._write_r
                0x***********05f6c       0x36 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
                0x***********05f6c                _write_r
 .text.__swsetup_r
                0x***********05fa2       0xfe d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
                0x***********05fa2                __swsetup_r
 .text._close_r
                0x***********060a0       0x32 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
                0x***********060a0                _close_r
 .text.__sflush_r
                0x***********060d2      0x132 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                0x***********060d2                __sflush_r
 .text._fflush_r
                0x***********06204       0x66 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
                0x***********06204                _fflush_r
 .text.std      0x***********0626a       0x66 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .text._cleanup_r
                0x***********062d0        0xa d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********062d0                _cleanup_r
 .text.__sfmoreglue
                0x***********062da       0x48 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********062da                __sfmoreglue
 .text.__sinit  0x***********06322       0x6c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********06322                __sinit
 .text.__sfp    0x***********0638e       0xa0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********0638e                __sfp
 .text._fwalk_reent
                0x***********0642e       0x6a d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
                0x***********0642e                _fwalk_reent
 .text._lseek_r
                0x***********06498       0x36 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
                0x***********06498                _lseek_r
 .text.__swhatbuf_r
                0x***********064ce       0x58 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                0x***********064ce                __swhatbuf_r
 .text.__smakebuf_r
                0x***********06526       0x94 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
                0x***********06526                __smakebuf_r
 .text.__malloc_lock
                0x***********065ba        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                0x***********065ba                __malloc_lock
 .text.__malloc_unlock
                0x***********065bc        0x2 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
                0x***********065bc                __malloc_unlock
 .text._sungetc_r
                0x***********065be       0x96 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
                0x***********065be                _sungetc_r
 .text.__ssrefill_r
                0x***********06654       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
                0x***********06654                __ssrefill_r
 .text.__ssvfscanf_r
                0x***********06698      0x3a8 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
                0x***********06698                __ssvfiscanf_r
                0x***********06698                __ssvfscanf_r
 .text.__sfputc_r
                0x***********06a40       0x28 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .text.__sfputs_r
                0x***********06a68       0x42 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                0x***********06a68                __sfputs_r
 .text._vfprintf_r
                0x***********06aaa      0x28e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                0x***********06aaa                _vfprintf_r
                0x***********06aaa                _vfiprintf_r
 .text._printf_common
                0x***********06d38      0x10c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x***********06d38                _printf_common
 .text._printf_i
                0x***********06e44      0x2a4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x***********06e44                _printf_i
 .text._scanf_chars
                0x***********070e8       0xe0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
                0x***********070e8                _scanf_chars
 .text._scanf_i
                0x***********071c8      0x256 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
                0x***********071c8                _scanf_i
 .text._read_r  0x***********0741e       0x34 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
                0x***********0741e                _read_r
 .text.__sccl   0x***********07452       0x82 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
                0x***********07452                __sccl
 .text._strtol_l.isra.0
                0x***********074d4      0x138 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .text._strtol_r
                0x***********0760c       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
                0x***********0760c                _strtol_r
 .text._strtoul_l.isra.0
                0x***********07622      0x12e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .text._strtoul_r
                0x***********07750       0x16 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
                0x***********07750                _strtoul_r
 .text.__submore
                0x***********07766       0x84 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
                0x***********07766                __submore
 .text._fstat_r
                0x***********077ea       0x32 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
                0x***********077ea                _fstat_r
 .text._isatty_r
                0x***********0781c       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
                0x***********0781c                _isatty_r
 .text.__locale_ctype_ptr_l
                0x***********0784c        0x6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                0x***********0784c                __locale_ctype_ptr_l
 .text.__locale_ctype_ptr
                0x***********07852       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                0x***********07852                __locale_ctype_ptr
 .text.__ascii_mbtowc
                0x***********0786a       0x32 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
                0x***********0786a                __ascii_mbtowc
 .text.memchr   0x***********0789c       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
                0x***********0789c                memchr
 .text._realloc_r
                0x***********078b4       0x58 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
                0x***********078b4                _realloc_r
 .text.__ascii_wctomb
                0x***********0790c       0x1e d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
                0x***********0790c                __ascii_wctomb
 .text._malloc_usable_size_r
                0x***********0792a       0x14 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
                0x***********0792a                _malloc_usable_size_r
 .text._close   0x***********0793e       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
                0x***********0793e                _close
 .text._fstat   0x***********0794e       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                0x***********0794e                _fstat
 .text._isatty  0x***********0795e       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                0x***********0795e                _isatty
 .text._lseek   0x***********0796e       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                0x***********0796e                _lseek
 .text._read    0x***********0797e       0x10 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                0x***********0797e                _read
 *(.rodata)
 *fill*         0x***********0798e        0x2 
 .rodata        0x***********07990       0x10 ./User/main.o
 .rodata        0x***********079a0       0x14 ./APP/cry_detect.o
 *(.rodata*)
 .rodata.esp8266_proc.str1.4
                0x***********079b4       0x13 ./User/main.o
 *fill*         0x***********079c7        0x1 
 .rodata.main.str1.4
                0x***********079c8       0x4f ./User/main.o
 *fill*         0x***********07a17        0x1 
 .rodata.noise_status_proc.cst4
                0x***********07a18        0x4 ./User/main.o
 .rodata.noise_status_proc.str1.4
                0x***********07a1c      0x16e ./User/main.o
 *fill*         0x***********07b8a        0x2 
 .rodata.str1.4
                0x***********07b8c       0x1f ./User/main.o
 *fill*         0x***********07bab        0x1 
 .rodata.tianwen_proc.str1.4
                0x***********07bac        0xe ./User/main.o
 *fill*         0x***********07bba        0x2 
 .rodata.kf_work.cst4
                0x***********07bbc        0x4 ./FFT/kiss_fft.o
 .rodata.kf_work.str1.4
                0x***********07bc0       0x3a ./FFT/kiss_fft.o
 *fill*         0x***********07bfa        0x2 
 .rodata.kiss_fft_stride.str1.4
                0x***********07bfc       0x6d ./FFT/kiss_fft.o
 *fill*         0x***********07c69        0x3 
 .rodata.evaluate_feature_confidence
                0x***********07c6c       0x18 ./APP/cry_detect.o
 .rodata.agc_init.cst4
                0x***********07c84        0x4 ./APP/cry_detect.o
 *fill*         0x***********07c88        0x0 
 .rodata.agc_init.cst8
                0x***********07c88        0x8 ./APP/cry_detect.o
 .rodata.agc_init.str1.4
                0x***********07c90       0x1d ./APP/cry_detect.o
 *fill*         0x***********07cad        0x3 
 .rodata.agc_process.part.3.cst4
                0x***********07cb0       0x1c ./APP/cry_detect.o
                                         0x20 (size before relaxing)
 .rodata.agc_process.part.3.str1.4
                0x***********07ccc       0x1b ./APP/cry_detect.o
 .rodata.calculate_confidence.cst4
                0x***********07ce7        0x4 ./APP/cry_detect.o
 *fill*         0x***********07ce7        0x1 
 .rodata.calculate_energy_variance.cst4
                0x***********07ce8        0x4 ./APP/cry_detect.o
 .rodata.calculate_spectral_centroid.cst4
                0x***********07cec        0x4 ./APP/cry_detect.o
 .rodata.calibration_init.str1.4
                0x***********07cf0       0x4f ./APP/cry_detect.o
 *fill*         0x***********07d3f        0x1 
 .rodata.calibration_process.str1.4
                0x***********07d40       0xf5 ./APP/cry_detect.o
                                        0x121 (size before relaxing)
 *fill*         0x***********07e35        0x3 
 .rodata.calibration_reset_to_default.str1.4
                0x***********07e38       0x25 ./APP/cry_detect.o
 *fill*         0x***********07e5d        0x3 
 .rodata.calibration_save_to_flash.str1.4
                0x***********07e60       0x62 ./APP/cry_detect.o
 *fill*         0x***********07ec2        0x2 
 .rodata.calibration_start.str1.4
                0x***********07ec4       0x9e ./APP/cry_detect.o
 *fill*         0x***********07f62        0x2 
 .rodata.classify_cry_with_enhanced_features.cst4
                0x***********07f64       0x20 ./APP/cry_detect.o
 .rodata.confidence_init.str1.4
                0x***********07f84       0x23 ./APP/cry_detect.o
 *fill*         0x***********07fa7        0x1 
 .rodata.cry_detect_init.str1.4
                0x***********07fa8      0x13f ./APP/cry_detect.o
 *fill*         0x***********080e7        0x1 
 .rodata.cry_detect_proc.cst4
                0x***********080e8        0x8 ./APP/cry_detect.o
 .rodata.cry_detect_proc.str1.4
                0x***********080f0      0x19b ./APP/cry_detect.o
 *fill*         0x***********0828b        0x1 
 .rodata.dynamic_threshold_init.str1.4
                0x***********0828c       0x34 ./APP/cry_detect.o
 .rodata.dynamic_threshold_update.cst4
                0x***********082c0        0x8 ./APP/cry_detect.o
 .rodata.dynamic_threshold_update.str1.4
                0x***********082c8       0x34 ./APP/cry_detect.o
 .rodata.enhanced_features_init.str1.4
                0x***********082fc       0x1f ./APP/cry_detect.o
 *fill*         0x***********0831b        0x1 
 .rodata.evaluate_feature_confidence.cst4
                0x***********0831c        0x8 ./APP/cry_detect.o
 .rodata.extract_enhanced_features.part.9.cst4
                0x***********08324        0xc ./APP/cry_detect.o
 .rodata.feature_weights
                0x***********08330       0x18 ./APP/cry_detect.o
 .rodata.get_adaptive_energy_threshold.cst4
                0x***********08348        0x4 ./APP/cry_detect.o
 .rodata.is_high_confidence_result.cst4
                0x***********0834c        0x4 ./APP/cry_detect.o
 .rodata.noise_estimation_init.str1.4
                0x***********08350       0x2e ./APP/cry_detect.o
 *fill*         0x***********0837e        0x2 
 .rodata.noise_estimation_proc.str1.4
                0x***********08380       0x2c ./APP/cry_detect.o
 .rodata.process_classification_with_confidence.str1.4
                0x***********083ac      0x16a ./APP/cry_detect.o
                                        0x176 (size before relaxing)
 *fill*         0x***********08516        0x2 
 .rodata.str1.4
                0x***********08518       0x16 ./APP/cry_detect.o
                                         0x26 (size before relaxing)
 *fill*         0x***********0852e        0x2 
 .rodata.sqrtf.str1.4
                0x***********08530        0x6 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
 *fill*         0x***********08536        0x2 
 .rodata.__divdf3
                0x***********08538       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
 .rodata.__divsf3
                0x***********08578       0x80 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
 .rodata.__mulsf3
                0x***********085f8       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .rodata.__clz_tab
                0x***********08638      0x100 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clz.o)
                0x***********08638                __clz_tab
 .rodata.__sf_fake_stderr
                0x***********08738       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********08738                __sf_fake_stderr
 .rodata.__sf_fake_stdin
                0x***********08758       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********08758                __sf_fake_stdin
 .rodata.__sf_fake_stdout
                0x***********08778       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
                0x***********08778                __sf_fake_stdout
 .rodata.__ssvfscanf_r.str1.4
                0x***********08798        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
 .rodata._vfprintf_r.str1.4
                0x***********0879c        0xf d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
                                         0x13 (size before relaxing)
 *fill*         0x***********087ab        0x1 
 .rodata._printf_i.str1.4
                0x***********087ac       0x25 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 *fill*         0x***********087d1        0x3 
 .rodata._scanf_i.str1.4
                0x***********087d4       0x12 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 *fill*         0x***********087e6        0x2 
 .rodata.str1.4
                0x***********087e8        0xb d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 *fill*         0x***********087f3        0x1 
 .rodata._setlocale_r.str1.4
                0x***********087f4        0xa d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                                          0xd (size before relaxing)
 .rodata.str1.4
                0x***********087fe        0xc d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                                          0x2 (size before relaxing)
 *fill*         0x***********087fe        0x2 
 .rodata._ctype_
                0x***********08800      0x101 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ctype_.o)
                0x***********08800                _ctype_
 *(.gnu.linkonce.t.*)
                0x***********08904                . = ALIGN (0x4)
 *fill*         0x***********08901        0x3 

.rela.dyn       0x***********08904        0x0
 .rela.init     0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.vector   0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text.handle_reset
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._sbrk
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.sdata.curbrk.5276
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._sbrk_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._write_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._close_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._lseek_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text.__ssvfscanf_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._vfprintf_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._read_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._fstat_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._isatty_r
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._close
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._fstat
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._isatty
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._lseek
                0x***********08904        0x0 ./User/ch32v30x_it.o
 .rela.text._read
                0x***********08904        0x0 ./User/ch32v30x_it.o

.fini           0x***********08904        0x0
 *(SORT_NONE(.fini))
                0x***********08904                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (_eitcm = .)

.preinit_array  0x***********08904        0x0
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)

.init_array     0x***********08904        0x0
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
                [!provide]                        PROVIDE (__init_array_end = .)

.fini_array     0x***********08904        0x0
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                [!provide]                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)

.dalign         0x0000000020000000        0x0 load address 0x***********08904
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_data_vma = .)

.dlalign        0x***********08904        0x0
                0x***********08904                . = ALIGN (0x4)
                0x***********08904                PROVIDE (_data_lma = .)

.data           0x0000000020000000      0x4a0 load address 0x***********08904
 *(.gnu.linkonce.r.*)
 *(.data .data.*)
 .data.scheduler_task
                0x0000000020000000       0x30 ./User/main.o
                0x0000000020000000                scheduler_task
 .data.string9  0x0000000020000030       0x76 ./User/main.o
                0x0000000020000030                string9
 *fill*         0x00000000200000a6        0x2 
 .data.AHBPrescTable
                0x00000000200000a8       0x10 ./User/system_ch32v30x.o
                0x00000000200000a8                AHBPrescTable
 .data.APBAHBPrescTable
                0x00000000200000b8       0x10 ./Peripheral/src/ch32v30x_rcc.o
 .data.string1  0x00000000200000c8        0x9 ./APP/esp8266.o
                0x00000000200000c8                string1
 *fill*         0x00000000200000d1        0x3 
 .data.string2  0x00000000200000d4        0xe ./APP/esp8266.o
                0x00000000200000d4                string2
 *fill*         0x00000000200000e2        0x2 
 .data.string3  0x00000000200000e4       0x10 ./APP/esp8266.o
                0x00000000200000e4                string3
 .data.string4  0x00000000200000f4       0x1c ./APP/esp8266.o
                0x00000000200000f4                string4
 .data.string5  0x0000000020000110       0xad ./APP/esp8266.o
                0x0000000020000110                string5
 *fill*         0x00000000200001bd        0x3 
 .data.string6  0x00000000200001c0       0x2c ./APP/esp8266.o
                0x00000000200001c0                string6
 .data.string7  0x00000000200001ec       0x3b ./APP/esp8266.o
                0x00000000200001ec                string7
 *fill*         0x0000000020000227        0x1 
 .data.string8  0x0000000020000228       0x79 ./APP/esp8266.o
                0x0000000020000228                string8
 *fill*         0x00000000200002a1        0x3 
 .data.impure_data
                0x00000000200002a4       0x60 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
 .data.__global_locale
                0x0000000020000304      0x16c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
                0x0000000020000304                __global_locale
 *(.gnu.linkonce.d.*)
                0x0000000020000470                . = ALIGN (0x8)
                0x0000000020000c70                PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.*)
 .sdata.led_flag
                0x0000000020000470        0x4 ./User/main.o
                0x0000000020000470                led_flag
 .sdata.SystemCoreClock
                0x0000000020000474        0x4 ./User/system_ch32v30x.o
                0x0000000020000474                SystemCoreClock
 .sdata.ADCPrescTable
                0x0000000020000478        0x4 ./Peripheral/src/ch32v30x_rcc.o
 .sdata.curbrk.5276
                0x000000002000047c        0x4 ./Debug/debug.o
 .sdata.current_energy_threshold
                0x0000000020000480        0x4 ./APP/cry_detect.o
 .sdata.current_gain
                0x0000000020000484        0x4 ./APP/cry_detect.o
 .sdata.current_zcr_threshold
                0x0000000020000488        0x2 ./APP/cry_detect.o
 *fill*         0x000000002000048a        0x2 
 .sdata.noise_baseline
                0x000000002000048c        0x4 ./APP/cry_detect.o
 .sdata.smooth_gain
                0x0000000020000490        0x4 ./APP/cry_detect.o
 .sdata.__fdlib_version
                0x0000000020000494        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_lib_ver.o)
                0x0000000020000494                __fdlib_version
 .sdata._impure_ptr
                0x0000000020000498        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                0x0000000020000498                _impure_ptr
 *(.sdata2.*)
 .sdata2._global_impure_ptr
                0x000000002000049c        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-impure.o)
                0x000000002000049c                _global_impure_ptr
 *(.gnu.linkonce.s.*)
                0x00000000200004a0                . = ALIGN (0x8)
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 *(.srodata.cst2)
 *(.srodata .srodata.*)
                0x00000000200004a0                . = ALIGN (0x4)
                0x00000000200004a0                PROVIDE (_edata = .)

.bss            0x00000000200004a0     0x5c28 load address 0x***********08da4
                0x00000000200004a0                . = ALIGN (0x4)
                0x00000000200004a0                PROVIDE (_sbss = .)
 *(.sbss*)
 .sbss.last_report_time.6618
                0x00000000200004a0        0x4 ./User/main.o
 .sbss.task_num
                0x00000000200004a4        0x1 ./User/main.o
                0x00000000200004a4                task_num
 .sbss.uart6_rec_index
                0x00000000200004a5        0x1 ./User/main.o
                0x00000000200004a5                uart6_rec_index
 .sbss.uart6_rec_tick
                0x00000000200004a6        0x1 ./User/main.o
                0x00000000200004a6                uart6_rec_tick
 .sbss.uart8_rec_index
                0x00000000200004a7        0x1 ./User/main.o
                0x00000000200004a7                uart8_rec_index
 .sbss.uart8_rec_tick
                0x00000000200004a8        0x1 ./User/main.o
                0x00000000200004a8                uart8_rec_tick
 *fill*         0x00000000200004a9        0x3 
 .sbss.uwtick   0x00000000200004ac        0x4 ./User/main.o
                0x00000000200004ac                uwtick
 .sbss.NVIC_Priority_Group
                0x00000000200004b0        0x4 ./Peripheral/src/ch32v30x_misc.o
                0x00000000200004b0                NVIC_Priority_Group
 .sbss.p_ms     0x00000000200004b4        0x2 ./Debug/debug.o
 .sbss.p_us     0x00000000200004b6        0x1 ./Debug/debug.o
 *fill*         0x00000000200004b7        0x1 
 .sbss.Calibrattion_Val
                0x00000000200004b8        0x2 ./APP/cry_detect.o
                0x00000000200004b8                Calibrattion_Val
 .sbss.agc_initialized
                0x00000000200004ba        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004bb        0x1 
 .sbss.agc_update_counter
                0x00000000200004bc        0x4 ./APP/cry_detect.o
 .sbss.calibration_active
                0x00000000200004c0        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004c1        0x3 
 .sbss.calibration_energy_sum
                0x00000000200004c4        0x4 ./APP/cry_detect.o
 .sbss.calibration_quiet_counter
                0x00000000200004c8        0x2 ./APP/cry_detect.o
 *fill*         0x00000000200004ca        0x2 
 .sbss.calibration_rms_sum
                0x00000000200004cc        0x4 ./APP/cry_detect.o
 .sbss.calibration_samples
                0x00000000200004d0        0x2 ./APP/cry_detect.o
 *fill*         0x00000000200004d2        0x2 
 .sbss.calibration_zcr_sum
                0x00000000200004d4        0x4 ./APP/cry_detect.o
 .sbss.confidence_history_count
                0x00000000200004d8        0x1 ./APP/cry_detect.o
 .sbss.confidence_history_index
                0x00000000200004d9        0x1 ./APP/cry_detect.o
 .sbss.confidence_initialized
                0x00000000200004da        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004db        0x1 
 .sbss.debug_counter.6555
                0x00000000200004dc        0x4 ./APP/cry_detect.o
 .sbss.energy_history_index
                0x00000000200004e0        0x1 ./APP/cry_detect.o
 .sbss.enhanced_features_initialized
                0x00000000200004e1        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004e2        0x2 
 .sbss.fft_cfg  0x00000000200004e4        0x4 ./APP/cry_detect.o
 .sbss.last_noise_update
                0x00000000200004e8        0x4 ./APP/cry_detect.o
 .sbss.last_print_time
                0x00000000200004ec        0x4 ./APP/cry_detect.o
 .sbss.last_printed_baseline.6623
                0x00000000200004f0        0x4 ./APP/cry_detect.o
 .sbss.last_status
                0x00000000200004f4        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004f5        0x3 
 .sbss.last_threshold_update.6642
                0x00000000200004f8        0x4 ./APP/cry_detect.o
 .sbss.noise_initialized
                0x00000000200004fc        0x1 ./APP/cry_detect.o
 .sbss.noise_sample_count
                0x00000000200004fd        0x1 ./APP/cry_detect.o
 .sbss.noise_sample_index
                0x00000000200004fe        0x1 ./APP/cry_detect.o
 *fill*         0x00000000200004ff        0x1 
 .sbss.peak_buf
                0x0000000020000500        0x6 ./APP/cry_detect.o
 *fill*         0x0000000020000506        0x2 
 .sbss.quiet_sample_count
                0x0000000020000508        0x4 ./APP/cry_detect.o
 .sbss.smooth_confidence
                0x000000002000050c        0x4 ./APP/cry_detect.o
 .sbss.smooth_index
                0x0000000020000510        0x1 ./APP/cry_detect.o
 .sbss.threshold_history_index
                0x0000000020000511        0x1 ./APP/cry_detect.o
 .sbss.threshold_initialized
                0x0000000020000512        0x1 ./APP/cry_detect.o
 *fill*         0x0000000020000513        0x1 
 .sbss.time_counter
                0x0000000020000514        0x4 ./APP/cry_detect.o
 .sbss.zcr_buf  0x0000000020000518        0x6 ./APP/cry_detect.o
 *fill*         0x000000002000051e        0x2 
 .sbss.__malloc_free_list
                0x0000000020000520        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x0000000020000520                __malloc_free_list
 .sbss.__malloc_sbrk_start
                0x0000000020000524        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
                0x0000000020000524                __malloc_sbrk_start
 *(.gnu.linkonce.sb.*)
 *(.bss*)
 .bss.uart6_rec_string
                0x0000000020000528      0x100 ./User/main.o
                0x0000000020000528                uart6_rec_string
 .bss.uart8_rec_string
                0x0000000020000628      0x100 ./User/main.o
                0x0000000020000628                uart8_rec_string
 .bss.TxBuf     0x0000000020000728      0x800 ./APP/cry_detect.o
                0x0000000020000728                TxBuf
 .bss.audio_samples.6522
                0x0000000020000f28      0x800 ./APP/cry_detect.o
 .bss.confidence_history
                0x0000000020001728       0x14 ./APP/cry_detect.o
 .bss.cry_status
                0x000000002000173c        0xc ./APP/cry_detect.o
                0x000000002000173c                cry_status
 .bss.current_calibration
                0x0000000020001748       0x18 ./APP/cry_detect.o
 .bss.current_features
                0x0000000020001760       0x18 ./APP/cry_detect.o
 .bss.energy_buf
                0x0000000020001778        0xc ./APP/cry_detect.o
 .bss.energy_history
                0x0000000020001784       0x28 ./APP/cry_detect.o
 .bss.fft_in    0x00000000200017ac     0x2000 ./APP/cry_detect.o
 .bss.fft_magnitude
                0x00000000200037ac      0x800 ./APP/cry_detect.o
 .bss.fft_out   0x0000000020003fac     0x2000 ./APP/cry_detect.o
 .bss.noise_samples
                0x0000000020005fac      0x100 ./APP/cry_detect.o
 .bss.smooth_features
                0x00000000200060ac       0x18 ./APP/cry_detect.o
 *(.gnu.linkonce.b.*)
 *(COMMON*)
 COMMON         0x00000000200060c4        0x4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
                0x00000000200060c4                errno
                0x00000000200060c8                . = ALIGN (0x4)
                0x00000000200060c8                PROVIDE (_ebss = .)
                0x00000000200060c8                PROVIDE (_end = _ebss)
                [!provide]                        PROVIDE (end = .)

.stack          0x0000000020007800      0x800
                0x0000000020007800                PROVIDE (_heap_end = .)
                0x0000000020007800                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_susrstack = .)
                0x0000000020008000                . = (. + __stack_size)
 *fill*         0x0000000020007800      0x800 
                0x0000000020008000                PROVIDE (_eusrstack = .)
OUTPUT(v1.15.elf elf32-littleriscv)

.debug_info     0x***********00000    0x1a9e7
 .debug_info    0x***********00000      0xb5b ./User/ch32v30x_it.o
 .debug_info    0x***********00b5b     0x1d20 ./User/main.o
 .debug_info    0x***********0287b      0xbb5 ./User/system_ch32v30x.o
 .debug_info    0x***********03430       0x22 ./Startup/startup_ch32v30x_D8C.o
 .debug_info    0x***********03452     0x1703 ./Peripheral/src/ch32v30x_adc.o
 .debug_info    0x***********04b55      0xa94 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_info    0x***********055e9      0xd16 ./Peripheral/src/ch32v30x_dma.o
 .debug_info    0x***********062ff     0x1270 ./Peripheral/src/ch32v30x_gpio.o
 .debug_info    0x***********0756f      0xf60 ./Peripheral/src/ch32v30x_misc.o
 .debug_info    0x***********084cf     0x158a ./Peripheral/src/ch32v30x_rcc.o
 .debug_info    0x***********09a59     0x2a32 ./Peripheral/src/ch32v30x_tim.o
 .debug_info    0x***********0c48b     0x13cd ./Peripheral/src/ch32v30x_usart.o
 .debug_info    0x***********0d858     0x153c ./FFT/kiss_fft.o
 .debug_info    0x***********0ed94      0xf4a ./Debug/debug.o
 .debug_info    0x***********0fcde      0xbab ./APP/audio.o
 .debug_info    0x***********10889     0x40bf ./APP/cry_detect.o
 .debug_info    0x***********14948      0xda4 ./APP/esp8266.o
 .debug_info    0x***********156ec     0x2b6b ./APP/lcd.o
 .debug_info    0x***********18257     0x1357 ./APP/timer.o
 .debug_info    0x***********195ae     0x1439 ./APP/uart.o

.debug_abbrev   0x***********00000     0x3fd8
 .debug_abbrev  0x***********00000      0x27d ./User/ch32v30x_it.o
 .debug_abbrev  0x***********0027d      0x4b2 ./User/main.o
 .debug_abbrev  0x***********0072f      0x2c0 ./User/system_ch32v30x.o
 .debug_abbrev  0x***********009ef       0x12 ./Startup/startup_ch32v30x_D8C.o
 .debug_abbrev  0x***********00a01      0x3a9 ./Peripheral/src/ch32v30x_adc.o
 .debug_abbrev  0x***********00daa      0x2f7 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_abbrev  0x***********010a1      0x2fd ./Peripheral/src/ch32v30x_dma.o
 .debug_abbrev  0x***********0139e      0x31a ./Peripheral/src/ch32v30x_gpio.o
 .debug_abbrev  0x***********016b8      0x2e3 ./Peripheral/src/ch32v30x_misc.o
 .debug_abbrev  0x***********0199b      0x351 ./Peripheral/src/ch32v30x_rcc.o
 .debug_abbrev  0x***********01cec      0x408 ./Peripheral/src/ch32v30x_tim.o
 .debug_abbrev  0x***********020f4      0x312 ./Peripheral/src/ch32v30x_usart.o
 .debug_abbrev  0x***********02406      0x4af ./FFT/kiss_fft.o
 .debug_abbrev  0x***********028b5      0x2f5 ./Debug/debug.o
 .debug_abbrev  0x***********02baa      0x289 ./APP/audio.o
 .debug_abbrev  0x***********02e33      0x5c5 ./APP/cry_detect.o
 .debug_abbrev  0x***********033f8      0x233 ./APP/esp8266.o
 .debug_abbrev  0x***********0362b      0x436 ./APP/lcd.o
 .debug_abbrev  0x***********03a61      0x2a6 ./APP/timer.o
 .debug_abbrev  0x***********03d07      0x2d1 ./APP/uart.o

.debug_aranges  0x***********00000      0xd28
 .debug_aranges
                0x***********00000       0x30 ./User/ch32v30x_it.o
 .debug_aranges
                0x***********00030       0x70 ./User/main.o
 .debug_aranges
                0x***********000a0       0x28 ./User/system_ch32v30x.o
 .debug_aranges
                0x***********000c8       0x30 ./Startup/startup_ch32v30x_D8C.o
 .debug_aranges
                0x***********000f8      0x150 ./Peripheral/src/ch32v30x_adc.o
 .debug_aranges
                0x***********00248       0x48 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_aranges
                0x***********00290       0x60 ./Peripheral/src/ch32v30x_dma.o
 .debug_aranges
                0x***********002f0       0xb0 ./Peripheral/src/ch32v30x_gpio.o
 .debug_aranges
                0x***********003a0       0x28 ./Peripheral/src/ch32v30x_misc.o
 .debug_aranges
                0x***********003c8      0x1a8 ./Peripheral/src/ch32v30x_rcc.o
 .debug_aranges
                0x***********00570      0x2d0 ./Peripheral/src/ch32v30x_tim.o
 .debug_aranges
                0x***********00840       0xf0 ./Peripheral/src/ch32v30x_usart.o
 .debug_aranges
                0x***********00930       0x48 ./FFT/kiss_fft.o
 .debug_aranges
                0x***********00978       0x50 ./Debug/debug.o
 .debug_aranges
                0x***********009c8       0x30 ./APP/audio.o
 .debug_aranges
                0x***********009f8      0x1b0 ./APP/cry_detect.o
 .debug_aranges
                0x***********00ba8       0x28 ./APP/esp8266.o
 .debug_aranges
                0x***********00bd0       0xd8 ./APP/lcd.o
 .debug_aranges
                0x***********00ca8       0x30 ./APP/timer.o
 .debug_aranges
                0x***********00cd8       0x50 ./APP/uart.o

.debug_ranges   0x***********00000     0x1190
 .debug_ranges  0x***********00000       0x20 ./User/ch32v30x_it.o
 .debug_ranges  0x***********00020       0xc8 ./User/main.o
 .debug_ranges  0x***********000e8       0x38 ./User/system_ch32v30x.o
 .debug_ranges  0x***********00120       0x28 ./Startup/startup_ch32v30x_D8C.o
 .debug_ranges  0x***********00148      0x140 ./Peripheral/src/ch32v30x_adc.o
 .debug_ranges  0x***********00288       0x50 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_ranges  0x***********002d8       0x50 ./Peripheral/src/ch32v30x_dma.o
 .debug_ranges  0x***********00328       0xa0 ./Peripheral/src/ch32v30x_gpio.o
 .debug_ranges  0x***********003c8       0x48 ./Peripheral/src/ch32v30x_misc.o
 .debug_ranges  0x***********00410      0x198 ./Peripheral/src/ch32v30x_rcc.o
 .debug_ranges  0x***********005a8      0x2f0 ./Peripheral/src/ch32v30x_tim.o
 .debug_ranges  0x***********00898       0xe0 ./Peripheral/src/ch32v30x_usart.o
 .debug_ranges  0x***********00978      0x100 ./FFT/kiss_fft.o
 .debug_ranges  0x***********00a78       0x40 ./Debug/debug.o
 .debug_ranges  0x***********00ab8       0x20 ./APP/audio.o
 .debug_ranges  0x***********00ad8      0x510 ./APP/cry_detect.o
 .debug_ranges  0x***********00fe8       0x18 ./APP/esp8266.o
 .debug_ranges  0x***********01000      0x130 ./APP/lcd.o
 .debug_ranges  0x***********01130       0x20 ./APP/timer.o
 .debug_ranges  0x***********01150       0x40 ./APP/uart.o

.debug_line     0x***********00000    0x13d73
 .debug_line    0x***********00000      0x3f3 ./User/ch32v30x_it.o
 .debug_line    0x***********003f3      0xef2 ./User/main.o
 .debug_line    0x***********012e5      0x8f2 ./User/system_ch32v30x.o
 .debug_line    0x***********01bd7      0x122 ./Startup/startup_ch32v30x_D8C.o
 .debug_line    0x***********01cf9     0x1871 ./Peripheral/src/ch32v30x_adc.o
 .debug_line    0x***********0356a      0x37f ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_line    0x***********038e9      0x9d5 ./Peripheral/src/ch32v30x_dma.o
 .debug_line    0x***********042be     0x11ca ./Peripheral/src/ch32v30x_gpio.o
 .debug_line    0x***********05488      0x3d2 ./Peripheral/src/ch32v30x_misc.o
 .debug_line    0x***********0585a     0x198d ./Peripheral/src/ch32v30x_rcc.o
 .debug_line    0x***********071e7     0x32fa ./Peripheral/src/ch32v30x_tim.o
 .debug_line    0x***********0a4e1      0xefa ./Peripheral/src/ch32v30x_usart.o
 .debug_line    0x***********0b3db     0x142e ./FFT/kiss_fft.o
 .debug_line    0x***********0c809      0x743 ./Debug/debug.o
 .debug_line    0x***********0cf4c      0x3f2 ./APP/audio.o
 .debug_line    0x***********0d33e     0x3cb1 ./APP/cry_detect.o
 .debug_line    0x***********10fef      0x388 ./APP/esp8266.o
 .debug_line    0x***********11377     0x1c55 ./APP/lcd.o
 .debug_line    0x***********12fcc      0x52e ./APP/timer.o
 .debug_line    0x***********134fa      0x879 ./APP/uart.o

.debug_str      0x***********00000     0x468e
 .debug_str     0x***********00000      0x69a ./User/ch32v30x_it.o
                                        0x750 (size before relaxing)
 .debug_str     0x***********0069a      0x7c0 ./User/main.o
                                        0xee6 (size before relaxing)
 .debug_str     0x***********00e5a      0x109 ./User/system_ch32v30x.o
                                        0x6ea (size before relaxing)
 .debug_str     0x***********00f63       0x2e ./Startup/startup_ch32v30x_D8C.o
                                         0x6a (size before relaxing)
 .debug_str     0x***********00f91      0x5f8 ./Peripheral/src/ch32v30x_adc.o
                                        0xc21 (size before relaxing)
 .debug_str     0x***********01589       0x7e ./Peripheral/src/ch32v30x_dbgmcu.o
                                        0x646 (size before relaxing)
 .debug_str     0x***********01607      0x1d9 ./Peripheral/src/ch32v30x_dma.o
                                        0x7f3 (size before relaxing)
 .debug_str     0x***********017e0      0x246 ./Peripheral/src/ch32v30x_gpio.o
                                        0x9b0 (size before relaxing)
 .debug_str     0x***********01a26      0x693 ./Peripheral/src/ch32v30x_misc.o
                                        0xcb9 (size before relaxing)
 .debug_str     0x***********020b9      0x5c2 ./Peripheral/src/ch32v30x_rcc.o
                                        0xcdf (size before relaxing)
 .debug_str     0x***********0267b      0xa82 ./Peripheral/src/ch32v30x_tim.o
                                       0x1286 (size before relaxing)
 .debug_str     0x***********030fd      0x37d ./Peripheral/src/ch32v30x_usart.o
                                        0xafa (size before relaxing)
 .debug_str     0x***********0347a      0x1bc ./FFT/kiss_fft.o
                                        0x7c3 (size before relaxing)
 .debug_str     0x***********03636       0x9a ./Debug/debug.o
                                        0x921 (size before relaxing)
 .debug_str     0x***********036d0        0xf ./APP/audio.o
                                        0x69d (size before relaxing)
 .debug_str     0x***********036df      0xb92 ./APP/cry_detect.o
                                       0x1ac9 (size before relaxing)
 .debug_str     0x***********04271       0x51 ./APP/esp8266.o
                                        0x624 (size before relaxing)
 .debug_str     0x***********042c2      0x347 ./APP/lcd.o
                                        0xb56 (size before relaxing)
 .debug_str     0x***********04609       0x63 ./APP/timer.o
                                       0x1037 (size before relaxing)
 .debug_str     0x***********0466c       0x22 ./APP/uart.o
                                        0xee0 (size before relaxing)

.comment        0x***********00000       0x33
 .comment       0x***********00000       0x33 ./User/ch32v30x_it.o
                                         0x34 (size before relaxing)
 .comment       0x***********00033       0x34 ./User/main.o
 .comment       0x***********00033       0x34 ./User/system_ch32v30x.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_adc.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_dbgmcu.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_dma.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_gpio.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_misc.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_rcc.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_tim.o
 .comment       0x***********00033       0x34 ./Peripheral/src/ch32v30x_usart.o
 .comment       0x***********00033       0x34 ./FFT/kiss_fft.o
 .comment       0x***********00033       0x34 ./Debug/debug.o
 .comment       0x***********00033       0x34 ./APP/audio.o
 .comment       0x***********00033       0x34 ./APP/cry_detect.o
 .comment       0x***********00033       0x34 ./APP/esp8266.o
 .comment       0x***********00033       0x34 ./APP/lcd.o
 .comment       0x***********00033       0x34 ./APP/timer.o
 .comment       0x***********00033       0x34 ./APP/uart.o

.debug_frame    0x***********00000     0x35a8
 .debug_frame   0x***********00000       0x40 ./User/ch32v30x_it.o
 .debug_frame   0x***********00040      0x180 ./User/main.o
 .debug_frame   0x***********001c0       0x3c ./User/system_ch32v30x.o
 .debug_frame   0x***********001fc      0x2b4 ./Peripheral/src/ch32v30x_adc.o
 .debug_frame   0x***********004b0       0x70 ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_frame   0x***********00520       0xd8 ./Peripheral/src/ch32v30x_dma.o
 .debug_frame   0x***********005f8      0x174 ./Peripheral/src/ch32v30x_gpio.o
 .debug_frame   0x***********0076c       0x30 ./Peripheral/src/ch32v30x_misc.o
 .debug_frame   0x***********0079c      0x344 ./Peripheral/src/ch32v30x_rcc.o
 .debug_frame   0x***********00ae0      0x614 ./Peripheral/src/ch32v30x_tim.o
 .debug_frame   0x***********010f4      0x1e4 ./Peripheral/src/ch32v30x_usart.o
 .debug_frame   0x***********012d8       0xfc ./FFT/kiss_fft.o
 .debug_frame   0x***********013d4       0xb8 ./Debug/debug.o
 .debug_frame   0x***********0148c       0x84 ./APP/audio.o
 .debug_frame   0x***********01510      0x734 ./APP/cry_detect.o
 .debug_frame   0x***********01c44       0x48 ./APP/esp8266.o
 .debug_frame   0x***********01c8c      0x4a0 ./APP/lcd.o
 .debug_frame   0x***********0212c       0x74 ./APP/timer.o
 .debug_frame   0x***********021a0      0x100 ./APP/uart.o
 .debug_frame   0x***********022a0       0x4c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-wf_sqrt.o)
 .debug_frame   0x***********022ec       0x48 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-ef_sqrt.o)
 .debug_frame   0x***********02334       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libm.a(lib_a-s_matherr.o)
 .debug_frame   0x***********02354       0x50 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divdf3.o)
 .debug_frame   0x***********023a4       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(addsf3.o)
 .debug_frame   0x***********023e4       0x50 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(divsf3.o)
 .debug_frame   0x***********02434       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(eqsf2.o)
 .debug_frame   0x***********02454       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(gesf2.o)
 .debug_frame   0x***********02474       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(lesf2.o)
 .debug_frame   0x***********02494       0x50 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(mulsf3.o)
 .debug_frame   0x***********024e4       0x3c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(subsf3.o)
 .debug_frame   0x***********02520       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(unordsf2.o)
 .debug_frame   0x***********02540       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixsfsi.o)
 .debug_frame   0x***********02560       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(fixunssfsi.o)
 .debug_frame   0x***********02580       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatsisf.o)
 .debug_frame   0x***********025b8       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(floatunsisf.o)
 .debug_frame   0x***********025fc       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(extendsfdf2.o)
 .debug_frame   0x***********02634       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(truncdfsf2.o)
 .debug_frame   0x***********02654       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32imacxw/ilp32\libgcc.a(_clzsi2.o)
 .debug_frame   0x***********02674       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-errno.o)
 .debug_frame   0x***********02694       0x44 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputc.o)
 .debug_frame   0x***********026d8       0x54 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fputs.o)
 .debug_frame   0x***********0272c       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-malloc.o)
 .debug_frame   0x***********0275c       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memcpy.o)
 .debug_frame   0x***********0277c       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-freer.o)
 .debug_frame   0x***********027bc       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-mallocr.o)
 .debug_frame   0x***********027fc       0x54 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-printf.o)
 .debug_frame   0x***********02850       0x4c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-putc.o)
 .debug_frame   0x***********0289c       0x54 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-puts.o)
 .debug_frame   0x***********028f0       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sbrkr.o)
 .debug_frame   0x***********02920       0x54 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sscanf.o)
 .debug_frame   0x***********02974       0xa4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-stdio.o)
 .debug_frame   0x***********02a18       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strlen.o)
 .debug_frame   0x***********02a38       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strncmp.o)
 .debug_frame   0x***********02a58       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strstr.o)
 .debug_frame   0x***********02a78       0x50 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wbuf.o)
 .debug_frame   0x***********02ac8       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-writer.o)
 .debug_frame   0x***********02af8       0x3c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wsetup.o)
 .debug_frame   0x***********02b34       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-closer.o)
 .debug_frame   0x***********02b64       0x7c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fflush.o)
 .debug_frame   0x***********02be0      0x148 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-findfp.o)
 .debug_frame   0x***********02d28       0x88 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fwalk.o)
 .debug_frame   0x***********02db0       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-lseekr.o)
 .debug_frame   0x***********02de0       0x64 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-makebuf.o)
 .debug_frame   0x***********02e44       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mlock.o)
 .debug_frame   0x***********02e74       0xb4 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-svfscanf.o)
 .debug_frame   0x***********02f28       0xd0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf.o)
 .debug_frame   0x***********02ff8       0x8c d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .debug_frame   0x***********03084       0xa0 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .debug_frame   0x***********03124       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-readr.o)
 .debug_frame   0x***********03154       0x70 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-reent.o)
 .debug_frame   0x***********031c4       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-sccl.o)
 .debug_frame   0x***********031e4       0x70 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtol.o)
 .debug_frame   0x***********03254       0x70 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-strtoul.o)
 .debug_frame   0x***********032c4       0x84 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-ungetc.o)
 .debug_frame   0x***********03348       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-fstatr.o)
 .debug_frame   0x***********03378       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-isattyr.o)
 .debug_frame   0x***********033a8       0x78 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-locale.o)
 .debug_frame   0x***********03420       0x38 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-mbtowc_r.o)
 .debug_frame   0x***********03458       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-memchr.o)
 .debug_frame   0x***********03478       0x40 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-reallocr.o)
 .debug_frame   0x***********034b8       0x30 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-wctomb_r.o)
 .debug_frame   0x***********034e8       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libg_nano.a(lib_a-nano-msizer.o)
 .debug_frame   0x***********03508       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .debug_frame   0x***********03528       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
 .debug_frame   0x***********03548       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
 .debug_frame   0x***********03568       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
 .debug_frame   0x***********03588       0x20 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)

.debug_loc      0x***********00000     0x8a94
 .debug_loc     0x***********00000      0x3e9 ./User/main.o
 .debug_loc     0x***********003e9      0x163 ./User/system_ch32v30x.o
 .debug_loc     0x***********0054c      0xab6 ./Peripheral/src/ch32v30x_adc.o
 .debug_loc     0x***********01002       0x7c ./Peripheral/src/ch32v30x_dbgmcu.o
 .debug_loc     0x***********0107e      0x19a ./Peripheral/src/ch32v30x_dma.o
 .debug_loc     0x***********01218      0x716 ./Peripheral/src/ch32v30x_gpio.o
 .debug_loc     0x***********0192e       0x47 ./Peripheral/src/ch32v30x_misc.o
 .debug_loc     0x***********01975      0xadb ./Peripheral/src/ch32v30x_rcc.o
 .debug_loc     0x***********02450     0x195e ./Peripheral/src/ch32v30x_tim.o
 .debug_loc     0x***********03dae      0x779 ./Peripheral/src/ch32v30x_usart.o
 .debug_loc     0x***********04527     0x10f8 ./FFT/kiss_fft.o
 .debug_loc     0x***********0561f      0x177 ./Debug/debug.o
 .debug_loc     0x***********05796       0xb2 ./APP/audio.o
 .debug_loc     0x***********05848     0x1a94 ./APP/cry_detect.o
 .debug_loc     0x***********072dc     0x15c6 ./APP/lcd.o
 .debug_loc     0x***********088a2       0x63 ./APP/timer.o
 .debug_loc     0x***********08905      0x18f ./APP/uart.o

.stab           0x***********00000       0x84
 .stab          0x***********00000       0x24 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
 .stab          0x***********00024       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(fstat.o)
                                         0x24 (size before relaxing)
 .stab          0x***********0003c       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(isatty.o)
                                         0x24 (size before relaxing)
 .stab          0x***********00054       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(lseek.o)
                                         0x24 (size before relaxing)
 .stab          0x***********0006c       0x18 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(read.o)
                                         0x24 (size before relaxing)

.stabstr        0x***********00000      0x117
 .stabstr       0x***********00000      0x117 d:/desktop/mounriver_studio/toolchain/risc-v embedded gcc/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32imacxw/ilp32\libnosys.a(close.o)
