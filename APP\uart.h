/*
 * uart.h
 *
 *  Created on: 2025��4��23��
 *      Author: 20360
 */

#ifndef APP_UART_H_
#define APP_UART_H_

void Usart3_Init();//����
void Uart6_Init();
void Uart8_Init(); // 串口8初始化
void Uart8_SendByte(uint8_t byte); // 串口8发送单字节
void uart6_send_string(uint8_t* string,uint8_t len);
void uart8_send_string(uint8_t* string,uint8_t len); // 串口8发送字符串
void usart1_send_string(u8* string,u8 len);

#endif /* APP_UART_H_ */
